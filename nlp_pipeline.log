2025-06-02 15:42:33,648 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\intelligent_agent.py changed, reloading.
2025-06-02 15:42:33,980 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\intelligent_agent.py changed, reloading.
2025-06-02 15:43:13,549 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\deepseek_llm_handler.py changed, reloading.
2025-06-02 15:43:14,490 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\deepseek_llm_handler.py changed, reloading.
2025-06-02 15:47:46,030 - INFO - Loading DeepSeek-R1 model: deepseek-ai/DeepSeek-R1-0528
2025-06-02 15:47:46,031 - INFO - Using device: cpu
2025-06-02 15:47:51,639 - INFO - Configuring for CPU-only operation
2025-06-02 15:47:52,779 - WARNING - Pipeline loading failed: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 15:47:52,780 - INFO - Trying CPU-compatible direct model loading...
2025-06-02 15:47:54,512 - ERROR - Failed to load DeepSeek-R1 model: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 15:47:54,513 - INFO - Will use fallback LLM system
2025-06-02 15:48:08,069 - INFO - Loading DeepSeek-R1 model: deepseek-ai/DeepSeek-R1-0528
2025-06-02 15:48:08,069 - INFO - Using device: cpu
2025-06-02 15:48:08,070 - INFO - Configuring for CPU-only operation
2025-06-02 15:48:08,815 - WARNING - Pipeline loading failed: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 15:48:08,815 - INFO - Trying CPU-compatible direct model loading...
2025-06-02 15:48:10,658 - ERROR - Failed to load DeepSeek-R1 model: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 15:48:10,662 - INFO - Will use fallback LLM system
2025-06-02 15:48:34,563 - INFO - Loading DeepSeek-R1 model: deepseek-ai/DeepSeek-R1-0528
2025-06-02 15:48:34,564 - INFO - Using device: cpu
2025-06-02 15:48:34,564 - INFO - Configuring for CPU-only operation
2025-06-02 15:48:35,198 - WARNING - Pipeline loading failed: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 15:48:35,199 - INFO - Trying CPU-compatible direct model loading...
2025-06-02 15:48:36,313 - ERROR - Failed to load DeepSeek-R1 model: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 15:48:36,313 - INFO - Will use fallback LLM system
2025-06-02 15:48:46,149 - INFO - Loading DeepSeek-R1 model: deepseek-ai/DeepSeek-R1-0528
2025-06-02 15:48:46,149 - INFO - Using device: cpu
2025-06-02 15:48:46,150 - INFO - Configuring for CPU-only operation
2025-06-02 15:48:46,864 - WARNING - Pipeline loading failed: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 15:48:46,865 - INFO - Trying CPU-compatible direct model loading...
2025-06-02 15:48:48,182 - ERROR - Failed to load DeepSeek-R1 model: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 15:48:48,182 - INFO - Will use fallback LLM system
2025-06-02 15:48:49,127 - INFO - Attempting Google search for: give me an overview of  stefan ribisch
2025-06-02 15:48:49,663 - INFO - Google API response status: 200
2025-06-02 15:48:49,664 - INFO - Google search returned 0 results
2025-06-02 15:48:49,664 - WARNING - No search results found for: give me an overview of  stefan ribisch
2025-06-02 15:48:59,543 - INFO - Loading DeepSeek-R1 model: deepseek-ai/DeepSeek-R1-0528
2025-06-02 15:48:59,544 - INFO - Using device: cpu
2025-06-02 15:48:59,544 - INFO - Configuring for CPU-only operation
2025-06-02 15:49:00,246 - WARNING - Pipeline loading failed: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 15:49:00,247 - INFO - Trying CPU-compatible direct model loading...
2025-06-02 15:49:01,463 - ERROR - Failed to load DeepSeek-R1 model: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 15:49:01,463 - INFO - Will use fallback LLM system
2025-06-02 15:49:09,820 - INFO - Loading DeepSeek-R1 model: deepseek-ai/DeepSeek-R1-0528
2025-06-02 15:49:09,820 - INFO - Using device: cpu
2025-06-02 15:49:09,821 - INFO - Configuring for CPU-only operation
2025-06-02 15:49:10,482 - WARNING - Pipeline loading failed: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 15:49:10,482 - INFO - Trying CPU-compatible direct model loading...
2025-06-02 15:49:11,844 - ERROR - Failed to load DeepSeek-R1 model: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 15:49:11,844 - INFO - Will use fallback LLM system
2025-06-02 15:49:14,353 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\deepseek_llm_handler.py changed, reloading.
2025-06-02 15:49:15,325 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\deepseek_llm_handler.py changed, reloading.
2025-06-02 15:49:15,457 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\deepseek_llm_handler.py changed, reloading.
2025-06-02 15:49:23,764 - INFO - Using CPU-compatible model: microsoft/DialoGPT-medium
2025-06-02 15:49:23,767 - INFO - Loading DeepSeek-R1 model: microsoft/DialoGPT-medium
2025-06-02 15:49:23,768 - INFO - Using device: cpu
2025-06-02 15:49:27,740 - INFO - Configuring for CPU-only operation
2025-06-02 15:49:50,088 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\deepseek_llm_handler.py changed, reloading.
2025-06-02 15:49:50,313 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\deepseek_llm_handler.py changed, reloading.
2025-06-02 15:49:50,958 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\deepseek_llm_handler.py changed, reloading.
2025-06-02 16:37:53,511 - INFO - Using CPU-compatible model: microsoft/DialoGPT-medium
2025-06-02 16:37:53,512 - INFO - Loading model: microsoft/DialoGPT-medium
2025-06-02 16:37:53,512 - INFO - Using device: cpu
2025-06-02 16:37:59,752 - INFO - Using DeepSeek-R1 for Jira query
2025-06-02 16:38:05,355 - INFO - Using CPU-compatible model: microsoft/DialoGPT-medium
2025-06-02 16:38:05,356 - INFO - Loading model: microsoft/DialoGPT-medium
2025-06-02 16:38:05,356 - INFO - Using device: cpu
2025-06-02 16:38:07,614 - INFO - Using DeepSeek-R1 for Jira query
2025-06-02 16:38:38,127 - INFO - Using CPU-compatible model: microsoft/DialoGPT-medium
2025-06-02 16:38:38,128 - INFO - Loading model: microsoft/DialoGPT-medium
2025-06-02 16:38:38,128 - INFO - Using device: cpu
2025-06-02 16:38:40,852 - INFO - Using DeepSeek-R1 for Jira query
2025-06-02 16:38:43,266 - INFO - Attempting Google search for: give me experience improvement recommendations
2025-06-02 16:38:43,868 - INFO - Google API response status: 200
2025-06-02 16:38:43,869 - INFO - Google search returned 0 results
2025-06-02 16:38:43,869 - WARNING - No search results found for: give me experience improvement recommendations
2025-06-02 16:38:54,293 - INFO - Using CPU-compatible model: microsoft/DialoGPT-medium
2025-06-02 16:38:54,293 - INFO - Loading model: microsoft/DialoGPT-medium
2025-06-02 16:38:54,294 - INFO - Using device: cpu
2025-06-02 16:38:56,810 - INFO - Using DeepSeek-R1 for Jira query
2025-06-02 16:39:00,036 - INFO - Attempting Google search for: give me experience improvement recommendations
2025-06-02 16:39:00,403 - INFO - Google API response status: 200
2025-06-02 16:39:00,404 - INFO - Google search returned 0 results
2025-06-02 16:39:00,405 - WARNING - No search results found for: give me experience improvement recommendations
2025-06-02 16:40:20,395 - INFO - Using CPU-compatible model: microsoft/DialoGPT-medium
2025-06-02 16:40:20,396 - INFO - Loading model: microsoft/DialoGPT-medium
2025-06-02 16:40:20,396 - INFO - Using device: cpu
2025-06-02 16:40:22,774 - INFO - Using DeepSeek-R1 for Jira query
2025-06-02 16:42:11,514 - INFO - Using CPU-compatible model: microsoft/DialoGPT-medium
2025-06-02 16:42:11,514 - INFO - Loading model: microsoft/DialoGPT-medium
2025-06-02 16:42:11,514 - INFO - Using device: cpu
2025-06-02 16:42:13,860 - INFO - Using DeepSeek-R1 for Jira query
2025-06-02 16:42:38,339 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\Ticket_UAT.xls_IAfvnxR.xlsx
2025-06-02 16:42:38,859 - INFO - Successfully loaded 'general_report' sheet
2025-06-02 16:42:38,864 - INFO - Columns before processing: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description', 'Description.1']
2025-06-02 16:42:38,865 - INFO - Found description columns: ['Description', 'Description.1']
2025-06-02 16:42:38,867 - INFO - Dropped first Description column, remaining columns: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description.1']
2025-06-02 16:42:38,872 - INFO - Cleaned Creator column
2025-06-02 16:42:38,877 - INFO - Removed duplicates based on Key column
2025-06-02 16:42:40,828 - INFO - Using 'Description.1' column for text analysis
2025-06-02 16:43:23,593 - INFO - Using 'Created' as creation date
2025-06-02 16:43:23,594 - INFO - Using 'Date of First Response' as response date
2025-06-02 16:43:23,610 - INFO - Using 'Priority' for priority impact calculation
2025-06-02 16:43:23,611 - INFO - Using 'Issue Type' for issue type impact calculation
2025-06-02 16:43:23,678 - INFO - Generated client metrics for 8 creators
2025-06-02 16:43:23,680 - WARNING - No Status column found, using default status distribution
2025-06-02 16:43:23,680 - INFO - Analysis completed successfully
2025-06-02 16:43:43,707 - INFO - Using CPU-compatible model: microsoft/DialoGPT-medium
2025-06-02 16:43:43,707 - INFO - Loading model: microsoft/DialoGPT-medium
2025-06-02 16:43:43,708 - INFO - Using device: cpu
2025-06-02 16:43:45,900 - INFO - Using DeepSeek-R1 for Jira query
2025-06-02 16:44:08,005 - INFO - Using CPU-compatible model: microsoft/DialoGPT-medium
2025-06-02 16:44:08,005 - INFO - Loading model: microsoft/DialoGPT-medium
2025-06-02 16:44:08,006 - INFO - Using device: cpu
2025-06-02 16:44:10,287 - INFO - Using DeepSeek-R1 for Jira query
2025-06-02 16:44:12,496 - INFO - Attempting Google search for: give me your recommendations
2025-06-02 16:44:13,018 - INFO - Google API response status: 200
2025-06-02 16:44:13,018 - INFO - Google search returned 2 results
2025-06-02 16:44:13,018 - INFO - Successfully formatted Google search result for: give me your recommendations
2025-06-02 16:45:41,476 - WARNING - Not Found: /.well-known/appspecific/com.chrome.devtools.json
2025-06-02 16:45:58,015 - INFO - Using CPU-compatible model: microsoft/DialoGPT-medium
2025-06-02 16:45:58,015 - INFO - Loading model: microsoft/DialoGPT-medium
2025-06-02 16:45:58,015 - INFO - Using device: cpu
2025-06-02 16:46:00,290 - INFO - Using DeepSeek-R1 for Jira query
2025-06-02 16:46:33,753 - INFO - Using CPU-compatible model: microsoft/DialoGPT-medium
2025-06-02 16:46:33,753 - INFO - Loading model: microsoft/DialoGPT-medium
2025-06-02 16:46:33,754 - INFO - Using device: cpu
2025-06-02 16:46:36,505 - INFO - Using DeepSeek-R1 for Jira query
2025-06-02 16:46:48,075 - INFO - Using CPU-compatible model: microsoft/DialoGPT-medium
2025-06-02 16:46:48,075 - INFO - Loading model: microsoft/DialoGPT-medium
2025-06-02 16:46:48,076 - INFO - Using device: cpu
2025-06-02 16:46:50,284 - INFO - Using DeepSeek-R1 for Jira query
2025-06-02 16:47:06,273 - INFO - Using CPU-compatible model: microsoft/DialoGPT-medium
2025-06-02 16:47:06,273 - INFO - Loading model: microsoft/DialoGPT-medium
2025-06-02 16:47:06,273 - INFO - Using device: cpu
2025-06-02 16:47:08,585 - INFO - Using DeepSeek-R1 for Jira query
2025-06-02 16:47:59,883 - INFO - Using CPU-compatible model: microsoft/DialoGPT-medium
2025-06-02 16:47:59,883 - INFO - Loading model: microsoft/DialoGPT-medium
2025-06-02 16:47:59,883 - INFO - Using device: cpu
2025-06-02 16:48:02,261 - INFO - Using DeepSeek-R1 for Jira query
2025-06-02 16:50:35,659 - INFO - Using CPU-compatible model: microsoft/DialoGPT-medium
2025-06-02 16:50:35,660 - INFO - Loading model: microsoft/DialoGPT-medium
2025-06-02 16:50:35,660 - INFO - Using device: cpu
2025-06-02 16:51:10,114 - INFO - Using CPU-compatible model: microsoft/DialoGPT-medium
2025-06-02 16:51:10,114 - INFO - Loading model: microsoft/DialoGPT-medium
2025-06-02 16:51:10,114 - INFO - Using device: cpu
2025-06-02 16:51:12,840 - INFO - Using DeepSeek-R1 for Jira query
2025-06-02 16:51:34,831 - INFO - Using CPU-compatible model: microsoft/DialoGPT-medium
2025-06-02 16:51:34,832 - INFO - Loading model: microsoft/DialoGPT-medium
2025-06-02 16:51:34,833 - INFO - Using device: cpu
2025-06-02 16:51:37,057 - INFO - Using DeepSeek-R1 for Jira query
2025-06-02 16:51:38,177 - INFO - Attempting Google search for: no it is wrong
2025-06-02 16:51:38,675 - INFO - Google API response status: 200
2025-06-02 16:51:38,676 - INFO - Google search returned 1 results
2025-06-02 16:51:38,676 - INFO - Successfully formatted Google search result for: no it is wrong
2025-06-02 16:52:52,299 - INFO - Using CPU-compatible model: microsoft/DialoGPT-medium
2025-06-02 16:52:52,299 - INFO - Loading model: microsoft/DialoGPT-medium
2025-06-02 16:52:52,300 - INFO - Using device: cpu
2025-06-02 16:52:54,444 - INFO - Using DeepSeek-R1 for Jira query
2025-06-02 16:52:56,755 - INFO - Attempting Google search for: i want recommendations: actionable improvements
2025-06-02 16:52:57,346 - INFO - Google API response status: 200
2025-06-02 16:52:57,347 - INFO - Google search returned 0 results
2025-06-02 16:52:57,347 - WARNING - No search results found for: i want recommendations: actionable improvements
2025-06-02 16:53:18,814 - INFO - Using CPU-compatible model: microsoft/DialoGPT-medium
2025-06-02 16:53:18,815 - INFO - Loading model: microsoft/DialoGPT-medium
2025-06-02 16:53:18,815 - INFO - Using device: cpu
2025-06-02 16:53:21,054 - INFO - Attempting Google search for: implement proactive updates
2025-06-02 16:53:21,558 - INFO - Google API response status: 200
2025-06-02 16:53:21,559 - INFO - Google search returned 0 results
2025-06-02 16:53:21,559 - WARNING - No search results found for: implement proactive updates
2025-06-02 16:53:58,771 - INFO - Using CPU-compatible model: microsoft/DialoGPT-medium
2025-06-02 16:53:58,771 - INFO - Loading model: microsoft/DialoGPT-medium
2025-06-02 16:53:58,771 - INFO - Using device: cpu
2025-06-02 16:54:00,886 - INFO - Using DeepSeek-R1 for Jira query
2025-06-02 18:17:44,224 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\deepseek_llm_handler.py changed, reloading.
2025-06-02 18:18:43,562 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\deepseek_llm_handler.py changed, reloading.
2025-06-02 18:20:01,593 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\chat_handler.py changed, reloading.
2025-06-02 18:20:28,924 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\chat_handler.py changed, reloading.
2025-06-02 18:21:05,661 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\chat_handler.py changed, reloading.
2025-06-02 18:21:48,961 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\chat_handler.py changed, reloading.
2025-06-02 18:23:19,411 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\opensource_llm_integration.py changed, reloading.
2025-06-02 18:23:45,596 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\opensource_llm_integration.py changed, reloading.
2025-06-02 18:24:13,796 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\opensource_llm_integration.py changed, reloading.
2025-06-02 18:35:36,833 - INFO - Attempting to load: DeepSeek R1 (cached)
2025-06-02 18:35:39,733 - WARNING - Pipeline loading failed for deepseek-ai/DeepSeek-R1-0528: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 18:35:41,030 - WARNING - Direct loading failed for deepseek-ai/DeepSeek-R1-0528: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 18:35:41,031 - INFO - Attempting to load: DeepSeek Coder 1.3B
2025-06-02 18:36:49,822 - INFO - Attempting to load: DeepSeek R1 (cached)
2025-06-02 18:36:51,148 - WARNING - Pipeline loading failed for deepseek-ai/DeepSeek-R1-0528: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 18:36:52,175 - WARNING - Direct loading failed for deepseek-ai/DeepSeek-R1-0528: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 18:36:52,176 - INFO - Attempting to load: DeepSeek Coder 1.3B
2025-06-02 18:39:58,749 - INFO - Attempting to load: DeepSeek R1 (cached)
2025-06-02 18:40:01,953 - WARNING - Pipeline loading failed for deepseek-ai/DeepSeek-R1-0528: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 18:40:03,232 - WARNING - Direct loading failed for deepseek-ai/DeepSeek-R1-0528: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 18:40:03,233 - INFO - Attempting to load: DeepSeek Coder 1.3B
2025-06-02 19:19:06,532 - INFO - Attempting to load: DeepSeek R1 (cached)
2025-06-02 19:19:08,363 - WARNING - Pipeline loading failed for deepseek-ai/DeepSeek-R1-0528: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 19:19:09,633 - WARNING - Direct loading failed for deepseek-ai/DeepSeek-R1-0528: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 19:19:09,634 - INFO - Attempting to load: DeepSeek Coder 1.3B
2025-06-02 19:19:23,770 - INFO - Attempting to load: DeepSeek R1 (cached)
2025-06-02 19:19:24,641 - WARNING - Pipeline loading failed for deepseek-ai/DeepSeek-R1-0528: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 19:19:25,905 - WARNING - Direct loading failed for deepseek-ai/DeepSeek-R1-0528: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 19:19:25,909 - INFO - Attempting to load: DeepSeek Coder 1.3B
2025-06-02 19:19:48,078 - INFO - Attempting to load: DeepSeek R1 (cached)
2025-06-02 19:19:48,971 - WARNING - Pipeline loading failed for deepseek-ai/DeepSeek-R1-0528: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 19:19:50,254 - WARNING - Direct loading failed for deepseek-ai/DeepSeek-R1-0528: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 19:19:50,255 - INFO - Attempting to load: DeepSeek Coder 1.3B
2025-06-02 19:19:54,633 - INFO - Using DeepSeek-R1 for Jira query
2025-06-02 19:19:54,647 - ERROR - Pipeline generation error: The following `model_kwargs` are not used by the model: ['low_cpu_mem_usage'] (note: typos in the generate arguments will also show up in this list)
2025-06-02 19:20:05,871 - INFO - Attempting to load: DeepSeek R1 (cached)
2025-06-02 19:20:07,028 - WARNING - Pipeline loading failed for deepseek-ai/DeepSeek-R1-0528: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 19:20:08,398 - WARNING - Direct loading failed for deepseek-ai/DeepSeek-R1-0528: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 19:20:08,398 - INFO - Attempting to load: DeepSeek Coder 1.3B
2025-06-02 19:20:40,585 - INFO - Attempting to load: DeepSeek R1 (cached)
2025-06-02 19:20:41,801 - WARNING - Pipeline loading failed for deepseek-ai/DeepSeek-R1-0528: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 19:20:42,952 - WARNING - Direct loading failed for deepseek-ai/DeepSeek-R1-0528: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 19:20:42,954 - INFO - Attempting to load: DeepSeek Coder 1.3B
2025-06-02 19:20:47,084 - INFO - Using DeepSeek-R1 for Jira query
2025-06-02 19:20:47,086 - ERROR - Pipeline generation error: The following `model_kwargs` are not used by the model: ['low_cpu_mem_usage'] (note: typos in the generate arguments will also show up in this list)
2025-06-02 19:21:10,605 - INFO - Attempting to load: DeepSeek R1 (cached)
2025-06-02 19:21:15,177 - WARNING - Pipeline loading failed for deepseek-ai/DeepSeek-R1-0528: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 19:21:17,407 - WARNING - Direct loading failed for deepseek-ai/DeepSeek-R1-0528: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 19:21:17,407 - INFO - Attempting to load: DeepSeek Coder 1.3B
2025-06-02 19:21:22,266 - INFO - Using DeepSeek-R1 for Jira query
2025-06-02 19:21:22,268 - ERROR - Pipeline generation error: The following `model_kwargs` are not used by the model: ['low_cpu_mem_usage'] (note: typos in the generate arguments will also show up in this list)
2025-06-02 22:54:02,244 - WARNING - Error while downloading from https://cdn-lfs-us-1.hf.co/repos/9f/12/9f124e23c7197532f1b7eaf621c5fa6b463f2634c18cac12eb28695103048a16/a400155e495b4d6b1cd5b05f04c56032b7dbc6bdff940e046a89ffc1cc2fbdcd?response-content-disposition=inline%3B+filename*%3DUTF-8%27%27pytorch_model.bin%3B+filename%3D%22pytorch_model.bin%22%3B&response-content-type=application%2Foctet-stream&Expires=1748889435&Policy=eyJTdGF0ZW1lbnQiOlt7IkNvbmRpdGlvbiI6eyJEYXRlTGVzc1RoYW4iOnsiQVdTOkVwb2NoVGltZSI6MTc0ODg4OTQzNX19LCJSZXNvdXJjZSI6Imh0dHBzOi8vY2RuLWxmcy11cy0xLmhmLmNvL3JlcG9zLzlmLzEyLzlmMTI0ZTIzYzcxOTc1MzJmMWI3ZWFmNjIxYzVmYTZiNDYzZjI2MzRjMThjYWMxMmViMjg2OTUxMDMwNDhhMTYvYTQwMDE1NWU0OTViNGQ2YjFjZDViMDVmMDRjNTYwMzJiN2RiYzZiZGZmOTQwZTA0NmE4OWZmYzFjYzJmYmRjZD9yZXNwb25zZS1jb250ZW50LWRpc3Bvc2l0aW9uPSomcmVzcG9uc2UtY29udGVudC10eXBlPSoifV19&Signature=HqPS%7ENyZI7Iev8t7QzF3rOKOlfTDk7zZXrmhTp2RiUF1bmvd60rcsf2UDRpqWQf6yFzaUmag5Sf4NQ%7EbQypsJu3AzyJ4PwSWTm%7ErL9V4mr1j3xPX4Z7UhsvT4-hKyESd0fzjvbA73TwRBqoJ%7EkwI4lsFJIIz02Iwd%7ERwujiJh-BBSKUwZZ7oXchiQ%7ErFvXGQ9tF8BdEo2tVoEUGwEbeyp9q3jrZybZOv53GIb6ygdrC58I1NKA5TRVtwBlQluLJ95ne7wlrVJXpWp3KejOfa29KqSaJ9hEl5gr-1RT9X3G3huGbuT85wG4jbdqJBvaG3txGVuYummy-uWjts%7ENl1CQ__&Key-Pair-Id=K24J24Z295AEI9: HTTPSConnectionPool(host='cdn-lfs-us-1.hf.co', port=443): Read timed out.
Trying to resume download...
2025-06-02 22:54:14,004 - INFO - Using DeepSeek-R1 for Jira query
2025-06-02 22:54:14,014 - ERROR - Pipeline generation error: The following `model_kwargs` are not used by the model: ['low_cpu_mem_usage'] (note: typos in the generate arguments will also show up in this list)
2025-06-02 22:57:16,213 - INFO - Attempting to load: DeepSeek R1 (cached)
2025-06-02 22:57:17,592 - WARNING - Pipeline loading failed for deepseek-ai/DeepSeek-R1-0528: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 22:57:18,739 - WARNING - Direct loading failed for deepseek-ai/DeepSeek-R1-0528: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 22:57:18,739 - INFO - Attempting to load: DeepSeek Coder 1.3B
2025-06-02 22:57:21,925 - INFO - Using DeepSeek-R1 for Jira query
2025-06-02 22:57:21,932 - ERROR - Pipeline generation error: The following `model_kwargs` are not used by the model: ['low_cpu_mem_usage'] (note: typos in the generate arguments will also show up in this list)
2025-06-02 22:57:21,935 - INFO - Attempting Google search for: compare with previous month
2025-06-02 22:57:23,798 - INFO - Google API response status: 200
2025-06-02 22:57:23,799 - INFO - Google search returned 0 results
2025-06-02 22:57:23,799 - WARNING - No search results found for: compare with previous month
