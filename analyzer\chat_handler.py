import uuid
from datetime import datetime, timedelta
from django.db.models import Avg, Count, Q
from django.utils import timezone
from .models import JiraF<PERSON>, AnalysisResult
from .chat_models import ChatMessage
from .opensource_llm_integration import OpenSourceLLMHandler
import json
import asyncio
from asgiref.sync import sync_to_async

class ChatHandler:
    def __init__(self, user):
        self.user = user
        self.llm_handler = OpenSourceLLMHandler()

    async def process_message(self, message_content, conversation_id=None):
        """Process incoming chat messages and generate appropriate responses."""
        try:
            # Use existing conversation ID or create new one
            if not conversation_id:
                conversation_id = uuid.uuid4()
            elif isinstance(conversation_id, str):
                # Convert string UUID to UUID object
                try:
                    conversation_id = uuid.UUID(conversation_id)
                except ValueError:
                    conversation_id = uuid.uuid4()

            # Save user message
            user_message = await sync_to_async(ChatMessage.objects.create)(
                user=self.user,
                conversation_id=conversation_id,
                message_type='user',
                content=message_content
            )

            # Gather context data from analysis results
            context_data = await self._gather_context_data(message_content)

            # Generate response using Open Source LLM
            response_content = await self.llm_handler.generate_response(
                message_content,
                conversation_id=conversation_id,
                context_data=context_data
            )

            # Save AI response
            ai_message = await sync_to_async(ChatMessage.objects.create)(
                user=self.user,
                conversation_id=conversation_id,
                message_type='ai',
                content=response_content
            )

            return {
                'response': response_content,
                'conversation_id': str(conversation_id),
                'status': 'success'
            }

        except Exception as e:
            return {
                'response': f"I encountered an error: {str(e)}. Please try again.",
                'conversation_id': str(conversation_id) if conversation_id else str(uuid.uuid4()),
                'status': 'error'
            }

    async def _gather_context_data(self, message):
        """Gather relevant context data based on the message content."""
        context_data = {
            'latest_analysis': None,
            'client_metrics': {},
            'sentiment': {},
            'trends': {}
        }

        try:
            # Get latest analysis result
            latest_analysis = await sync_to_async(
                lambda: AnalysisResult.objects.filter(
                    jira_file__user=self.user
                ).order_by('-created_at').first()
            )()

            if not latest_analysis:
                return context_data

            # Add basic analysis data
            context_data['latest_analysis'] = {
                'issue_count': latest_analysis.issue_count,
                'ticket_types': latest_analysis.ticket_types,
                'priority_distribution': latest_analysis.priority_distribution,
                'status_distribution': latest_analysis.status_distribution,
                'common_themes': latest_analysis.common_themes
            }

            # Add client-specific data if mentioned
            message_lower = message.lower()
            for client_name in latest_analysis.client_metrics.keys():
                if client_name.lower() in message_lower:
                    context_data['client_metrics'][client_name] = latest_analysis.client_metrics[client_name]

            # Add sentiment data
            sentiment_data = latest_analysis.sentiment_analysis
            if sentiment_data:
                context_data['sentiment'] = {
                    'overall': sentiment_data.get('overall_sentiment', 'N/A'),
                    'positive_topics': sentiment_data.get('positive_topics', []),
                    'negative_topics': sentiment_data.get('negative_topics', []),
                    'sentiment_trends': sentiment_data.get('sentiment_trends', {})
                }

            # Add trend data if requested
            if any(word in message_lower for word in ['trend', 'trending', 'patterns', 'historical', 'over time']):
                # Get historical data for trend analysis
                historical_analyses = await sync_to_async(list)(
                    AnalysisResult.objects.filter(
                        jira_file__user=self.user,
                        created_at__gte=timezone.now() - timedelta(days=30)
                    ).order_by('created_at')
                )

                if historical_analyses:
                    trends = {
                        'issue_counts': [],
                        'priorities': {},
                        'themes': set(),
                        'resolution_times': [],
                        'client_satisfaction': {}
                    }

                    for analysis in historical_analyses:
                        date_str = analysis.created_at.strftime('%Y-%m-%d')
                        
                        # Track issue counts over time
                        trends['issue_counts'].append({
                            'date': date_str,
                            'count': analysis.issue_count
                        })

                        # Track priority distributions
                        for priority, count in analysis.priority_distribution.items():
                            if priority not in trends['priorities']:
                                trends['priorities'][priority] = []
                            trends['priorities'][priority].append({
                                'date': date_str,
                                'count': count
                            })

                        # Track common themes
                        if analysis.common_themes:
                            trends['themes'].update(analysis.common_themes)

                        # Track resolution times
                        if hasattr(analysis, 'resolution_times'):
                            trends['resolution_times'].append({
                                'date': date_str,
                                'average': analysis.resolution_times.get('average', 'N/A')
                            })

                        # Track client satisfaction if available
                        if hasattr(analysis, 'client_satisfaction'):
                            for client, score in analysis.client_satisfaction.items():
                                if client not in trends['client_satisfaction']:
                                    trends['client_satisfaction'][client] = []
                                trends['client_satisfaction'][client].append({
                                    'date': date_str,
                                    'score': score
                                })

                    # Convert themes set to list for JSON serialization
                    trends['themes'] = list(trends['themes'])
                    context_data['trends'] = trends

        except Exception as e:
            print(f"Error gathering context data: {str(e)}")

        return context_data

    def get_conversation_history(self, conversation_id):
        """Retrieve the conversation history for a given conversation ID."""
        try:
            messages = ChatMessage.objects.filter(
                conversation_id=conversation_id
            ).order_by('created_at')

            return [{
                'type': msg.message_type,
                'content': msg.content,
                'timestamp': msg.created_at.isoformat()
            } for msg in messages]

        except Exception as e:
            return []

    def clear_conversation(self, conversation_id):
        """Clear the conversation history for a given conversation ID."""
        try:
            ChatMessage.objects.filter(conversation_id=conversation_id).delete()
            return True
        except Exception as e:
            return False