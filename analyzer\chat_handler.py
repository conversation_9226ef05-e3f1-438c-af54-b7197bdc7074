import uuid
from datetime import datetime, timedelta
from django.db.models import Avg, Count, Q
from django.utils import timezone
from .models import <PERSON>raF<PERSON>, AnalysisResult
from .chat_models import ChatMessage
from .opensource_llm_integration import OpenSourceLLMHandler
from .intelligent_agent import IntelligentAgent
import json
import async<PERSON>
from asgiref.sync import sync_to_async

class ChatHandler:
    def __init__(self, user):
        self.user = user
        self.llm_handler = OpenSourceLLMHandler()
        self.intelligent_agent = IntelligentAgent(user)

    async def process_message(self, message_content, conversation_id=None):
        """Process incoming chat messages and generate appropriate responses."""
        try:
            # Use existing conversation ID or create new one
            if not conversation_id:
                conversation_id = uuid.uuid4()
            elif isinstance(conversation_id, str):
                # Convert string UUID to UUID object
                try:
                    conversation_id = uuid.UUID(conversation_id)
                except ValueError:
                    conversation_id = uuid.uuid4()

            # Save user message
            user_message = await sync_to_async(ChatMessage.objects.create)(
                user=self.user,
                conversation_id=conversation_id,
                message_type='user',
                content=message_content
            )

            # Gather context data from analysis results
            context_data = await self._gather_context_data(message_content)

            # Generate response using the intelligent agent
            response_content = await self.intelligent_agent.process_intelligent_query(
                message_content,
                conversation_id=conversation_id,
                context_data=context_data
            )

            # Save AI response
            ai_message = await sync_to_async(ChatMessage.objects.create)(
                user=self.user,
                conversation_id=conversation_id,
                message_type='ai',
                content=response_content
            )

            return {
                'response': response_content,
                'conversation_id': str(conversation_id),
                'status': 'success'
            }

        except Exception as e:
            return {
                'response': f"I encountered an error: {str(e)}. Please try again.",
                'conversation_id': str(conversation_id) if conversation_id else str(uuid.uuid4()),
                'status': 'error'
            }

    async def _gather_context_data(self, message):
        """Gather comprehensive context data based on the message content and available analysis."""
        context_data = {
            'latest_analysis': None,
            'all_analyses': [],
            'client_metrics': {},
            'sentiment': {},
            'trends': {},
            'comparative_data': {},
            'actionable_insights': [],
            'data_summary': {}
        }

        try:
            # Get all analysis results for the user
            all_analyses = await sync_to_async(list)(
                AnalysisResult.objects.filter(
                    jira_file__user=self.user
                ).order_by('-created_at').select_related('jira_file')
            )

            if not all_analyses:
                return context_data

            # Get latest analysis
            latest_analysis = all_analyses[0]

            # Enhanced analysis data with file context
            context_data['latest_analysis'] = {
                'id': latest_analysis.id,
                'file_name': latest_analysis.jira_file.original_filename or latest_analysis.jira_file.file.name,
                'analysis_date': latest_analysis.jira_file.analysis_date.strftime('%Y-%m-%d') if latest_analysis.jira_file.analysis_date else 'N/A',
                'upload_date': latest_analysis.created_at.strftime('%Y-%m-%d'),
                'issue_count': latest_analysis.issue_count,
                'ticket_types': latest_analysis.ticket_types,
                'priority_distribution': latest_analysis.priority_distribution,
                'status_distribution': latest_analysis.status_distribution,
                'common_themes': latest_analysis.common_themes,
                'sentiment_analysis': latest_analysis.sentiment_analysis,
                'client_metrics': latest_analysis.client_metrics,
                'actionable_insights': latest_analysis.actionable_insights
            }

            # Add all analyses summary for comparative analysis
            context_data['all_analyses'] = []
            for analysis in all_analyses:
                context_data['all_analyses'].append({
                    'id': analysis.id,
                    'file_name': analysis.jira_file.original_filename or analysis.jira_file.file.name,
                    'analysis_date': analysis.jira_file.analysis_date.strftime('%Y-%m-%d') if analysis.jira_file.analysis_date else 'N/A',
                    'issue_count': analysis.issue_count,
                    'ticket_types': analysis.ticket_types,
                    'priority_distribution': analysis.priority_distribution,
                    'client_count': len(analysis.client_metrics) if analysis.client_metrics else 0
                })

            # Enhanced client analysis
            message_lower = message.lower()

            # Always include top clients for context
            if latest_analysis.client_metrics:
                # Sort clients by ticket count or impact score
                sorted_clients = sorted(
                    latest_analysis.client_metrics.items(),
                    key=lambda x: x[1].get('Tickets', 0),
                    reverse=True
                )

                # Include top 5 clients or specific mentioned clients
                for client_name, metrics in sorted_clients[:5]:
                    context_data['client_metrics'][client_name] = metrics

                # Add specific client if mentioned
                for client_name in latest_analysis.client_metrics.keys():
                    if client_name.lower() in message_lower:
                        context_data['client_metrics'][client_name] = latest_analysis.client_metrics[client_name]

            # Enhanced sentiment analysis
            sentiment_data = latest_analysis.sentiment_analysis
            if sentiment_data:
                context_data['sentiment'] = {
                    'distribution': sentiment_data,
                    'total_tickets': sum(sentiment_data.values()) if isinstance(sentiment_data, dict) else 0,
                    'negative_percentage': round((sentiment_data.get('negative_low', 0) +
                                               sentiment_data.get('negative_medium', 0) +
                                               sentiment_data.get('negative_high', 0)) /
                                               sum(sentiment_data.values()) * 100, 1) if isinstance(sentiment_data, dict) and sum(sentiment_data.values()) > 0 else 0
                }

            # Enhanced trend and comparative analysis
            if len(all_analyses) > 1:
                # Calculate trends across all analyses
                context_data['trends'] = self._calculate_trends(all_analyses)
                context_data['comparative_data'] = self._calculate_comparative_data(all_analyses)

            # Add actionable insights from latest analysis
            if latest_analysis.actionable_insights:
                context_data['actionable_insights'] = latest_analysis.actionable_insights

            # Generate data summary for AI context
            context_data['data_summary'] = self._generate_data_summary(latest_analysis, all_analyses)

        except Exception as e:
            print(f"Error gathering context data: {str(e)}")

        return context_data

    def _calculate_trends(self, all_analyses):
        """Calculate trends across multiple analyses"""
        trends = {
            'issue_count_trend': [],
            'priority_trends': {},
            'client_trends': {},
            'sentiment_trends': []
        }

        for analysis in all_analyses:
            date_str = analysis.jira_file.analysis_date.strftime('%Y-%m-%d') if analysis.jira_file.analysis_date else analysis.created_at.strftime('%Y-%m-%d')

            # Issue count trend
            trends['issue_count_trend'].append({
                'date': date_str,
                'file_name': analysis.jira_file.original_filename or analysis.jira_file.file.name,
                'count': analysis.issue_count
            })

            # Priority trends
            for priority, count in analysis.priority_distribution.items():
                if priority not in trends['priority_trends']:
                    trends['priority_trends'][priority] = []
                trends['priority_trends'][priority].append({
                    'date': date_str,
                    'count': count,
                    'percentage': round(count / analysis.issue_count * 100, 1) if analysis.issue_count > 0 else 0
                })

            # Sentiment trends
            if analysis.sentiment_analysis:
                sentiment_data = analysis.sentiment_analysis
                negative_count = sentiment_data.get('negative_low', 0) + sentiment_data.get('negative_medium', 0) + sentiment_data.get('negative_high', 0)
                total_sentiment = sum(sentiment_data.values()) if isinstance(sentiment_data, dict) else 0

                trends['sentiment_trends'].append({
                    'date': date_str,
                    'negative_percentage': round(negative_count / total_sentiment * 100, 1) if total_sentiment > 0 else 0,
                    'positive_count': sentiment_data.get('positive', 0),
                    'negative_count': negative_count
                })

        return trends

    def _calculate_comparative_data(self, all_analyses):
        """Calculate comparative data between analyses"""
        if len(all_analyses) < 2:
            return {}

        latest = all_analyses[0]
        previous = all_analyses[1]

        comparative = {
            'issue_count_change': latest.issue_count - previous.issue_count,
            'issue_count_change_percentage': round((latest.issue_count - previous.issue_count) / previous.issue_count * 100, 1) if previous.issue_count > 0 else 0,
            'priority_changes': {},
            'client_changes': {},
            'new_clients': [],
            'lost_clients': []
        }

        # Priority changes
        for priority in set(list(latest.priority_distribution.keys()) + list(previous.priority_distribution.keys())):
            latest_count = latest.priority_distribution.get(priority, 0)
            previous_count = previous.priority_distribution.get(priority, 0)
            change = latest_count - previous_count

            comparative['priority_changes'][priority] = {
                'change': change,
                'change_percentage': round(change / previous_count * 100, 1) if previous_count > 0 else 0,
                'latest_count': latest_count,
                'previous_count': previous_count
            }

        # Client changes
        latest_clients = set(latest.client_metrics.keys()) if latest.client_metrics else set()
        previous_clients = set(previous.client_metrics.keys()) if previous.client_metrics else set()

        comparative['new_clients'] = list(latest_clients - previous_clients)
        comparative['lost_clients'] = list(previous_clients - latest_clients)

        # Client metric changes for common clients
        common_clients = latest_clients & previous_clients
        for client in common_clients:
            if client in latest.client_metrics and client in previous.client_metrics:
                latest_tickets = latest.client_metrics[client].get('Tickets', 0)
                previous_tickets = previous.client_metrics[client].get('Tickets', 0)

                comparative['client_changes'][client] = {
                    'ticket_change': latest_tickets - previous_tickets,
                    'latest_tickets': latest_tickets,
                    'previous_tickets': previous_tickets
                }

        return comparative

    def _generate_data_summary(self, latest_analysis, all_analyses):
        """Generate a comprehensive data summary for AI context"""
        summary = {
            'total_files_analyzed': len(all_analyses),
            'latest_file': latest_analysis.jira_file.original_filename or latest_analysis.jira_file.file.name,
            'total_issues_latest': latest_analysis.issue_count,
            'total_clients': len(latest_analysis.client_metrics) if latest_analysis.client_metrics else 0,
            'top_priority': max(latest_analysis.priority_distribution.items(), key=lambda x: x[1])[0] if latest_analysis.priority_distribution else 'N/A',
            'top_ticket_type': max(latest_analysis.ticket_types.items(), key=lambda x: x[1])[0] if latest_analysis.ticket_types else 'N/A',
            'sentiment_summary': 'N/A'
        }

        # Sentiment summary
        if latest_analysis.sentiment_analysis:
            sentiment_data = latest_analysis.sentiment_analysis
            total_sentiment = sum(sentiment_data.values()) if isinstance(sentiment_data, dict) else 0
            if total_sentiment > 0:
                negative_percentage = round((sentiment_data.get('negative_low', 0) +
                                           sentiment_data.get('negative_medium', 0) +
                                           sentiment_data.get('negative_high', 0)) / total_sentiment * 100, 1)

                if negative_percentage > 60:
                    summary['sentiment_summary'] = f"Concerning ({negative_percentage}% negative)"
                elif negative_percentage > 40:
                    summary['sentiment_summary'] = f"Mixed ({negative_percentage}% negative)"
                else:
                    summary['sentiment_summary'] = f"Generally positive ({negative_percentage}% negative)"

        # Top clients by ticket count
        if latest_analysis.client_metrics:
            top_clients = sorted(
                latest_analysis.client_metrics.items(),
                key=lambda x: x[1].get('Tickets', 0),
                reverse=True
            )[:3]
            summary['top_clients'] = [{'name': client, 'tickets': metrics.get('Tickets', 0)} for client, metrics in top_clients]

        return summary

    def get_conversation_history(self, conversation_id):
        """Retrieve the conversation history for a given conversation ID."""
        try:
            messages = ChatMessage.objects.filter(
                conversation_id=conversation_id
            ).order_by('created_at')

            return [{
                'type': msg.message_type,
                'content': msg.content,
                'timestamp': msg.created_at.isoformat()
            } for msg in messages]

        except Exception as e:
            return []

    def clear_conversation(self, conversation_id):
        """Clear the conversation history for a given conversation ID."""
        try:
            ChatMessage.objects.filter(conversation_id=conversation_id).delete()
            return True
        except Exception as e:
            return False