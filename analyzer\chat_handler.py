import uuid
from datetime import datetime, timedelta
from django.db.models import Avg, Count, Q
from django.utils import timezone
from .models import <PERSON>raF<PERSON>, AnalysisResult
from .chat_models import ChatMessage
from .opensource_llm_integration import OpenSourceLLMHandler
from .intelligent_agent import IntelligentAgent
import json
import async<PERSON>
from asgiref.sync import sync_to_async

class ChatHandler:
    def __init__(self, user):
        self.user = user
        self.llm_handler = OpenSourceLLMHandler()
        self.intelligent_agent = IntelligentAgent(user)

    async def process_message(self, message_content, conversation_id=None):
        """Process incoming chat messages and generate appropriate responses."""
        try:
            # Use existing conversation ID or create new one
            if not conversation_id:
                conversation_id = uuid.uuid4()
            elif isinstance(conversation_id, str):
                # Convert string UUID to UUID object
                try:
                    conversation_id = uuid.UUID(conversation_id)
                except ValueError:
                    conversation_id = uuid.uuid4()

            # Save user message
            user_message = await sync_to_async(ChatMessage.objects.create)(
                user=self.user,
                conversation_id=conversation_id,
                message_type='user',
                content=message_content
            )

            # Gather context data from analysis results
            context_data = await self._gather_context_data(message_content)

            # Generate response using the intelligent agent
            response_content = await self.intelligent_agent.process_intelligent_query(
                message_content,
                conversation_id=conversation_id,
                context_data=context_data
            )

            # Save AI response
            ai_message = await sync_to_async(ChatMessage.objects.create)(
                user=self.user,
                conversation_id=conversation_id,
                message_type='ai',
                content=response_content
            )

            return {
                'response': response_content,
                'conversation_id': str(conversation_id),
                'status': 'success'
            }

        except Exception as e:
            return {
                'response': f"I encountered an error: {str(e)}. Please try again.",
                'conversation_id': str(conversation_id) if conversation_id else str(uuid.uuid4()),
                'status': 'error'
            }

    async def _gather_context_data(self, message):
        """Gather comprehensive context data based on the message content and available analysis."""
        context_data = {
            'latest_analysis': None,
            'all_analyses': [],
            'historical_summary': {},
            'aggregated_client_metrics': {},
            'comprehensive_sentiment': {},
            'multi_file_trends': {},
            'client_metrics': {},
            'sentiment': {},
            'trends': {},
            'comparative_data': {},
            'actionable_insights': [],
            'data_summary': {},
            'file_timeline': [],
            'cross_file_patterns': {}
        }

        try:
            # Get all analysis results for the user
            all_analyses = await sync_to_async(list)(
                AnalysisResult.objects.filter(
                    jira_file__user=self.user
                ).order_by('-created_at').select_related('jira_file')
            )

            if not all_analyses:
                return context_data

            # Get latest analysis
            latest_analysis = all_analyses[0]

            # Enhanced analysis data with file context
            context_data['latest_analysis'] = {
                'id': latest_analysis.id,
                'file_name': latest_analysis.jira_file.original_filename or latest_analysis.jira_file.file.name,
                'analysis_date': latest_analysis.jira_file.analysis_date.strftime('%Y-%m-%d') if latest_analysis.jira_file.analysis_date else 'N/A',
                'upload_date': latest_analysis.created_at.strftime('%Y-%m-%d'),
                'issue_count': latest_analysis.issue_count,
                'ticket_types': latest_analysis.ticket_types,
                'priority_distribution': latest_analysis.priority_distribution,
                'status_distribution': latest_analysis.status_distribution,
                'common_themes': latest_analysis.common_themes,
                'sentiment_analysis': latest_analysis.sentiment_analysis,
                'client_metrics': latest_analysis.client_metrics,
                'actionable_insights': latest_analysis.actionable_insights
            }

            # Add all analyses summary for comparative analysis
            context_data['all_analyses'] = []
            for analysis in all_analyses:
                context_data['all_analyses'].append({
                    'id': analysis.id,
                    'file_name': analysis.jira_file.original_filename or analysis.jira_file.file.name,
                    'analysis_date': analysis.jira_file.analysis_date.strftime('%Y-%m-%d') if analysis.jira_file.analysis_date else 'N/A',
                    'issue_count': analysis.issue_count,
                    'ticket_types': analysis.ticket_types,
                    'priority_distribution': analysis.priority_distribution,
                    'client_count': len(analysis.client_metrics) if analysis.client_metrics else 0
                })

            # Enhanced client analysis
            message_lower = message.lower()

            # Always include top clients for context
            if latest_analysis.client_metrics:
                # Sort clients by ticket count or impact score
                sorted_clients = sorted(
                    latest_analysis.client_metrics.items(),
                    key=lambda x: x[1].get('Tickets', 0),
                    reverse=True
                )

                # Include top 5 clients or specific mentioned clients
                for client_name, metrics in sorted_clients[:5]:
                    context_data['client_metrics'][client_name] = metrics

                # Add specific client if mentioned
                for client_name in latest_analysis.client_metrics.keys():
                    if client_name.lower() in message_lower:
                        context_data['client_metrics'][client_name] = latest_analysis.client_metrics[client_name]

            # Enhanced sentiment analysis
            sentiment_data = latest_analysis.sentiment_analysis
            if sentiment_data:
                context_data['sentiment'] = {
                    'distribution': sentiment_data,
                    'total_tickets': sum(sentiment_data.values()) if isinstance(sentiment_data, dict) else 0,
                    'negative_percentage': round((sentiment_data.get('negative_low', 0) +
                                               sentiment_data.get('negative_medium', 0) +
                                               sentiment_data.get('negative_high', 0)) /
                                               sum(sentiment_data.values()) * 100, 1) if isinstance(sentiment_data, dict) and sum(sentiment_data.values()) > 0 else 0
                }

            # Enhanced comprehensive analysis across all files
            if len(all_analyses) > 1:
                # Calculate trends across all analyses
                context_data['trends'] = self._calculate_trends(all_analyses)
                context_data['comparative_data'] = self._calculate_comparative_data(all_analyses)

                # New comprehensive analysis methods
                context_data['historical_summary'] = self._generate_historical_summary(all_analyses)
                context_data['aggregated_client_metrics'] = self._aggregate_client_metrics(all_analyses)
                context_data['comprehensive_sentiment'] = self._analyze_comprehensive_sentiment(all_analyses)
                context_data['multi_file_trends'] = self._analyze_multi_file_trends(all_analyses)
                context_data['file_timeline'] = self._create_file_timeline(all_analyses)
                context_data['cross_file_patterns'] = self._detect_cross_file_patterns(all_analyses)

            # Add actionable insights from latest analysis
            if latest_analysis.actionable_insights:
                context_data['actionable_insights'] = latest_analysis.actionable_insights

            # Generate enhanced data summary for AI context
            context_data['data_summary'] = self._generate_enhanced_data_summary(latest_analysis, all_analyses)

        except Exception as e:
            print(f"Error gathering context data: {str(e)}")

        return context_data

    def _calculate_trends(self, all_analyses):
        """Calculate trends across multiple analyses"""
        trends = {
            'issue_count_trend': [],
            'priority_trends': {},
            'client_trends': {},
            'sentiment_trends': []
        }

        for analysis in all_analyses:
            date_str = analysis.jira_file.analysis_date.strftime('%Y-%m-%d') if analysis.jira_file.analysis_date else analysis.created_at.strftime('%Y-%m-%d')

            # Issue count trend
            trends['issue_count_trend'].append({
                'date': date_str,
                'file_name': analysis.jira_file.original_filename or analysis.jira_file.file.name,
                'count': analysis.issue_count
            })

            # Priority trends
            for priority, count in analysis.priority_distribution.items():
                if priority not in trends['priority_trends']:
                    trends['priority_trends'][priority] = []
                trends['priority_trends'][priority].append({
                    'date': date_str,
                    'count': count,
                    'percentage': round(count / analysis.issue_count * 100, 1) if analysis.issue_count > 0 else 0
                })

            # Sentiment trends
            if analysis.sentiment_analysis:
                sentiment_data = analysis.sentiment_analysis
                negative_count = sentiment_data.get('negative_low', 0) + sentiment_data.get('negative_medium', 0) + sentiment_data.get('negative_high', 0)
                total_sentiment = sum(sentiment_data.values()) if isinstance(sentiment_data, dict) else 0

                trends['sentiment_trends'].append({
                    'date': date_str,
                    'negative_percentage': round(negative_count / total_sentiment * 100, 1) if total_sentiment > 0 else 0,
                    'positive_count': sentiment_data.get('positive', 0),
                    'negative_count': negative_count
                })

        return trends

    def _calculate_comparative_data(self, all_analyses):
        """Calculate comparative data between analyses"""
        if len(all_analyses) < 2:
            return {}

        latest = all_analyses[0]
        previous = all_analyses[1]

        comparative = {
            'issue_count_change': latest.issue_count - previous.issue_count,
            'issue_count_change_percentage': round((latest.issue_count - previous.issue_count) / previous.issue_count * 100, 1) if previous.issue_count > 0 else 0,
            'priority_changes': {},
            'client_changes': {},
            'new_clients': [],
            'lost_clients': []
        }

        # Priority changes
        for priority in set(list(latest.priority_distribution.keys()) + list(previous.priority_distribution.keys())):
            latest_count = latest.priority_distribution.get(priority, 0)
            previous_count = previous.priority_distribution.get(priority, 0)
            change = latest_count - previous_count

            comparative['priority_changes'][priority] = {
                'change': change,
                'change_percentage': round(change / previous_count * 100, 1) if previous_count > 0 else 0,
                'latest_count': latest_count,
                'previous_count': previous_count
            }

        # Client changes
        latest_clients = set(latest.client_metrics.keys()) if latest.client_metrics else set()
        previous_clients = set(previous.client_metrics.keys()) if previous.client_metrics else set()

        comparative['new_clients'] = list(latest_clients - previous_clients)
        comparative['lost_clients'] = list(previous_clients - latest_clients)

        # Client metric changes for common clients
        common_clients = latest_clients & previous_clients
        for client in common_clients:
            if client in latest.client_metrics and client in previous.client_metrics:
                latest_tickets = latest.client_metrics[client].get('Tickets', 0)
                previous_tickets = previous.client_metrics[client].get('Tickets', 0)

                comparative['client_changes'][client] = {
                    'ticket_change': latest_tickets - previous_tickets,
                    'latest_tickets': latest_tickets,
                    'previous_tickets': previous_tickets
                }

        return comparative

    def _generate_data_summary(self, latest_analysis, all_analyses):
        """Generate a comprehensive data summary for AI context"""
        summary = {
            'total_files_analyzed': len(all_analyses),
            'latest_file': latest_analysis.jira_file.original_filename or latest_analysis.jira_file.file.name,
            'total_issues_latest': latest_analysis.issue_count,
            'total_clients': len(latest_analysis.client_metrics) if latest_analysis.client_metrics else 0,
            'top_priority': max(latest_analysis.priority_distribution.items(), key=lambda x: x[1])[0] if latest_analysis.priority_distribution else 'N/A',
            'top_ticket_type': max(latest_analysis.ticket_types.items(), key=lambda x: x[1])[0] if latest_analysis.ticket_types else 'N/A',
            'sentiment_summary': 'N/A'
        }

        # Sentiment summary
        if latest_analysis.sentiment_analysis:
            sentiment_data = latest_analysis.sentiment_analysis
            total_sentiment = sum(sentiment_data.values()) if isinstance(sentiment_data, dict) else 0
            if total_sentiment > 0:
                negative_percentage = round((sentiment_data.get('negative_low', 0) +
                                           sentiment_data.get('negative_medium', 0) +
                                           sentiment_data.get('negative_high', 0)) / total_sentiment * 100, 1)

                if negative_percentage > 60:
                    summary['sentiment_summary'] = f"Concerning ({negative_percentage}% negative)"
                elif negative_percentage > 40:
                    summary['sentiment_summary'] = f"Mixed ({negative_percentage}% negative)"
                else:
                    summary['sentiment_summary'] = f"Generally positive ({negative_percentage}% negative)"

        # Top clients by ticket count
        if latest_analysis.client_metrics:
            top_clients = sorted(
                latest_analysis.client_metrics.items(),
                key=lambda x: x[1].get('Tickets', 0),
                reverse=True
            )[:3]
            summary['top_clients'] = [{'name': client, 'tickets': metrics.get('Tickets', 0)} for client, metrics in top_clients]

        return summary

    def get_conversation_history(self, conversation_id):
        """Retrieve the conversation history for a given conversation ID."""
        try:
            messages = ChatMessage.objects.filter(
                conversation_id=conversation_id
            ).order_by('created_at')

            return [{
                'type': msg.message_type,
                'content': msg.content,
                'timestamp': msg.created_at.isoformat()
            } for msg in messages]

        except Exception as e:
            return []

    def clear_conversation(self, conversation_id):
        """Clear the conversation history for a given conversation ID."""
        try:
            ChatMessage.objects.filter(conversation_id=conversation_id).delete()
            return True
        except Exception as e:
            return False

    def _generate_historical_summary(self, all_analyses):
        """Generate comprehensive historical summary across all files"""
        if not all_analyses:
            return {}

        total_issues = sum(analysis.issue_count for analysis in all_analyses)
        total_files = len(all_analyses)

        # Date range
        dates = [analysis.jira_file.analysis_date for analysis in all_analyses if analysis.jira_file.analysis_date]
        date_range = {
            'earliest': min(dates).strftime('%Y-%m-%d') if dates else 'N/A',
            'latest': max(dates).strftime('%Y-%m-%d') if dates else 'N/A',
            'span_days': (max(dates) - min(dates)).days if len(dates) > 1 else 0
        }

        # Average metrics
        avg_issues_per_file = round(total_issues / total_files, 1)

        return {
            'total_files_analyzed': total_files,
            'total_issues_across_all_files': total_issues,
            'average_issues_per_file': avg_issues_per_file,
            'date_range': date_range,
            'file_names': [analysis.jira_file.original_filename or analysis.jira_file.file.name for analysis in all_analyses]
        }

    def _aggregate_client_metrics(self, all_analyses):
        """Aggregate client metrics across all files"""
        aggregated_clients = {}

        for analysis in all_analyses:
            if not analysis.client_metrics:
                continue

            for client_name, metrics in analysis.client_metrics.items():
                if client_name not in aggregated_clients:
                    aggregated_clients[client_name] = {
                        'total_tickets': 0,
                        'files_appeared_in': 0,
                        'avg_experience_score': 0,
                        'avg_sentiment': 0,
                        'experience_scores': [],
                        'sentiment_scores': [],
                        'ticket_counts': []
                    }

                # Aggregate data
                aggregated_clients[client_name]['total_tickets'] += metrics.get('Tickets', 0)
                aggregated_clients[client_name]['files_appeared_in'] += 1
                aggregated_clients[client_name]['ticket_counts'].append(metrics.get('Tickets', 0))

                if metrics.get('Customer_Experience_Score'):
                    aggregated_clients[client_name]['experience_scores'].append(metrics.get('Customer_Experience_Score', 0))

                if metrics.get('sentiment'):
                    aggregated_clients[client_name]['sentiment_scores'].append(metrics.get('sentiment', 0))

        # Calculate averages
        for client_name, data in aggregated_clients.items():
            if data['experience_scores']:
                data['avg_experience_score'] = round(sum(data['experience_scores']) / len(data['experience_scores']) * 100, 1)
            if data['sentiment_scores']:
                data['avg_sentiment'] = round(sum(data['sentiment_scores']) / len(data['sentiment_scores']), 2)

        return aggregated_clients

    def _analyze_comprehensive_sentiment(self, all_analyses):
        """Analyze sentiment patterns across all files"""
        sentiment_timeline = []
        overall_sentiment_totals = {}

        for analysis in all_analyses:
            if not analysis.sentiment_analysis:
                continue

            date_str = analysis.jira_file.analysis_date.strftime('%Y-%m-%d') if analysis.jira_file.analysis_date else analysis.created_at.strftime('%Y-%m-%d')

            sentiment_timeline.append({
                'date': date_str,
                'file_name': analysis.jira_file.original_filename or analysis.jira_file.file.name,
                'sentiment_distribution': analysis.sentiment_analysis
            })

            # Aggregate overall sentiment
            for sentiment_type, count in analysis.sentiment_analysis.items():
                if sentiment_type not in overall_sentiment_totals:
                    overall_sentiment_totals[sentiment_type] = 0
                overall_sentiment_totals[sentiment_type] += count

        # Calculate overall percentages
        total_sentiment_tickets = sum(overall_sentiment_totals.values())
        overall_percentages = {}
        if total_sentiment_tickets > 0:
            for sentiment_type, count in overall_sentiment_totals.items():
                overall_percentages[sentiment_type] = round(count / total_sentiment_tickets * 100, 1)

        return {
            'sentiment_timeline': sentiment_timeline,
            'overall_totals': overall_sentiment_totals,
            'overall_percentages': overall_percentages,
            'total_sentiment_tickets': total_sentiment_tickets
        }

    def _analyze_multi_file_trends(self, all_analyses):
        """Analyze trends across multiple files with enhanced insights"""
        if len(all_analyses) < 2:
            return {}

        # Sort by analysis date
        sorted_analyses = sorted(all_analyses, key=lambda x: x.jira_file.analysis_date or x.created_at)

        trends = {
            'issue_volume_trend': [],
            'priority_evolution': {},
            'client_evolution': {},
            'sentiment_evolution': [],
            'overall_direction': 'stable'
        }

        # Track issue volume changes
        for i, analysis in enumerate(sorted_analyses):
            date_str = analysis.jira_file.analysis_date.strftime('%Y-%m-%d') if analysis.jira_file.analysis_date else analysis.created_at.strftime('%Y-%m-%d')

            trend_point = {
                'date': date_str,
                'file_name': analysis.jira_file.original_filename or analysis.jira_file.file.name,
                'issue_count': analysis.issue_count,
                'change_from_previous': 0,
                'change_percentage': 0
            }

            if i > 0:
                previous_count = sorted_analyses[i-1].issue_count
                trend_point['change_from_previous'] = analysis.issue_count - previous_count
                trend_point['change_percentage'] = round((analysis.issue_count - previous_count) / previous_count * 100, 1) if previous_count > 0 else 0

            trends['issue_volume_trend'].append(trend_point)

        # Determine overall direction
        if len(trends['issue_volume_trend']) >= 2:
            first_count = trends['issue_volume_trend'][0]['issue_count']
            last_count = trends['issue_volume_trend'][-1]['issue_count']
            change_pct = (last_count - first_count) / first_count * 100 if first_count > 0 else 0

            if change_pct > 10:
                trends['overall_direction'] = 'increasing'
            elif change_pct < -10:
                trends['overall_direction'] = 'decreasing'
            else:
                trends['overall_direction'] = 'stable'

        return trends

    def _create_file_timeline(self, all_analyses):
        """Create a chronological timeline of all analyzed files"""
        timeline = []

        for analysis in all_analyses:
            date_str = analysis.jira_file.analysis_date.strftime('%Y-%m-%d') if analysis.jira_file.analysis_date else analysis.created_at.strftime('%Y-%m-%d')

            timeline_entry = {
                'date': date_str,
                'file_name': analysis.jira_file.original_filename or analysis.jira_file.file.name,
                'issue_count': analysis.issue_count,
                'client_count': len(analysis.client_metrics) if analysis.client_metrics else 0,
                'top_priority': max(analysis.priority_distribution.items(), key=lambda x: x[1])[0] if analysis.priority_distribution else 'N/A',
                'sentiment_summary': self._get_sentiment_summary(analysis.sentiment_analysis) if analysis.sentiment_analysis else 'N/A'
            }

            timeline.append(timeline_entry)

        # Sort by date
        timeline.sort(key=lambda x: x['date'])
        return timeline

    def _detect_cross_file_patterns(self, all_analyses):
        """Detect patterns that appear across multiple files"""
        patterns = {
            'recurring_clients': {},
            'consistent_priorities': {},
            'common_ticket_types': {},
            'persistent_themes': {}
        }

        # Track recurring clients
        client_appearances = {}
        for analysis in all_analyses:
            if analysis.client_metrics:
                for client_name in analysis.client_metrics.keys():
                    if client_name not in client_appearances:
                        client_appearances[client_name] = 0
                    client_appearances[client_name] += 1

        # Clients appearing in multiple files
        patterns['recurring_clients'] = {
            client: count for client, count in client_appearances.items()
            if count > 1
        }

        # Track consistent priorities across files
        priority_consistency = {}
        for analysis in all_analyses:
            if analysis.priority_distribution:
                top_priority = max(analysis.priority_distribution.items(), key=lambda x: x[1])[0]
                if top_priority not in priority_consistency:
                    priority_consistency[top_priority] = 0
                priority_consistency[top_priority] += 1

        patterns['consistent_priorities'] = priority_consistency

        return patterns

    def _get_sentiment_summary(self, sentiment_data):
        """Get a brief sentiment summary"""
        if not sentiment_data or not isinstance(sentiment_data, dict):
            return 'N/A'

        total = sum(sentiment_data.values())
        if total == 0:
            return 'N/A'

        negative_count = sentiment_data.get('negative_low', 0) + sentiment_data.get('negative_medium', 0) + sentiment_data.get('negative_high', 0)
        negative_pct = round(negative_count / total * 100, 1)

        if negative_pct > 60:
            return f"Concerning ({negative_pct}% negative)"
        elif negative_pct > 40:
            return f"Mixed ({negative_pct}% negative)"
        else:
            return f"Positive ({negative_pct}% negative)"

    def _generate_enhanced_data_summary(self, latest_analysis, all_analyses):
        """Generate enhanced data summary with comprehensive file memory"""
        summary = self._generate_data_summary(latest_analysis, all_analyses)

        # Add comprehensive insights
        if len(all_analyses) > 1:
            historical_summary = self._generate_historical_summary(all_analyses)
            summary.update({
                'total_files_analyzed': historical_summary['total_files_analyzed'],
                'total_issues_all_files': historical_summary['total_issues_across_all_files'],
                'average_issues_per_file': historical_summary['average_issues_per_file'],
                'analysis_date_range': historical_summary['date_range'],
                'has_historical_data': True
            })
        else:
            summary['has_historical_data'] = False

        return summary