#!/usr/bin/env python
"""
Test the Enhanced AI Agent with DeepSeek-R1 model and Google Custom Search
"""
import os
import sys
import django
from django.conf import settings

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'jira_analyzer.settings')
django.setup()

from django.contrib.auth.models import User
from analyzer.chat_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>
import asyncio
from asgiref.sync import sync_to_async

async def test_deepseek_enhanced_agent():
    """Test the enhanced AI Agent with DeepSeek-R1 and Google Search"""
    print("🧠 DeepSeek-R1 Enhanced AI Agent Test")
    print("=" * 80)
    
    try:
        # Get the user with actual data (Hamada)
        user = await sync_to_async(User.objects.get)(username='<PERSON><PERSON>')
        print(f"✅ Testing with user: {user.username}")
        
        # Initialize chat handler
        handler = <PERSON><PERSON><PERSON><PERSON><PERSON>(user)
        print("✅ Chat handler with DeepSeek-R1 intelligent agent initialized")
        
        # Check agent capabilities
        memory_status = handler.intelligent_agent.get_memory_status()
        print(f"🔍 Agent Status: {memory_status}")
        
        # Test various enhanced capabilities
        test_scenarios = [
            {
                'category': '🧠 DeepSeek-R1 Jira Analysis',
                'queries': [
                    "Analyze my latest Jira data and provide detailed insights",
                    "What patterns do you see in my client tickets?",
                    "Give me strategic recommendations for improving our support process"
                ]
            },
            {
                'category': '🌐 Enhanced Google Web Search',
                'queries': [
                    "What are the latest trends in project management for 2024?",
                    "How to implement DevOps best practices?",
                    "What is the difference between Scrum and Kanban methodologies?"
                ]
            },
            {
                'category': '🔄 Hybrid Intelligence (Jira + Web)',
                'queries': [
                    "How can I apply modern agile practices to improve my current Jira workflow?",
                    "Compare my client satisfaction metrics with industry benchmarks",
                    "What are the best practices for handling high-priority tickets like mine?"
                ]
            },
            {
                'category': '🧠 Memory & Context Awareness',
                'queries': [
                    "Remember my previous analysis about client satisfaction",
                    "Based on our earlier discussion, what should I focus on next?",
                    "How does my current data compare to what we discussed before?"
                ]
            }
        ]
        
        conversation_id = None
        
        print(f"\n🧪 Testing Enhanced AI Agent with DeepSeek-R1:")
        print("=" * 80)
        
        for scenario in test_scenarios:
            category = scenario['category']
            queries = scenario['queries']
            
            print(f"\n{category} ({len(queries)} queries)")
            print("-" * 70)
            
            for i, query in enumerate(queries, 1):
                print(f"\n{i}. Query: '{query}'")
                print("." * 50)
                
                # Process the message
                result = await handler.process_message(query, conversation_id)
                
                # Use the same conversation ID for memory testing
                if not conversation_id:
                    conversation_id = result['conversation_id']
                
                # Display the response
                response = result['response']
                print(f"🤖 Response:\n{response}")
                
                # Show enhanced metadata
                print(f"\n📊 Enhanced Metadata:")
                print(f"   Status: {result['status']}")
                print(f"   Response Length: {len(response)} characters")
                print(f"   Conversation ID: {result['conversation_id']}")
                
                # Check enhanced memory status
                memory_status = handler.intelligent_agent.get_memory_status()
                print(f"   DeepSeek Model: {memory_status.get('deepseek_model', 'N/A')}")
                print(f"   Google Search: {'Enabled' if memory_status.get('google_search') else 'Disabled'}")
                print(f"   Active Conversations: {memory_status.get('active_conversations', 0)}")
                print(f"   Web Cache Size: {memory_status.get('web_cache_size', 0)}")
                
                print("\n" + "=" * 70)
        
        print("\n🎉 All DeepSeek-R1 enhanced tests completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run the DeepSeek-R1 enhanced AI Agent test"""
    print("🧪 DeepSeek-R1 Enhanced AI Agent Test")
    print("=" * 100)
    
    success = asyncio.run(test_deepseek_enhanced_agent())
    
    print("\n" + "=" * 100)
    if success:
        print("🎉 SUCCESS! DeepSeek-R1 Enhanced AI Agent is working!")
        print("\n🧠 **Enhanced Capabilities Demonstrated:**")
        print("   ✅ DeepSeek-R1 Local LLM - Advanced reasoning and analysis")
        print("   ✅ Google Custom Search - Real-time web information access")
        print("   ✅ Enhanced Memory System - Context-aware conversations")
        print("   ✅ Hybrid Intelligence - Combines Jira data + web search")
        print("   ✅ Fallback System - Graceful degradation if models fail")
        print("   ✅ Professional Responses - Structured, detailed insights")
        
        print("\n🔧 **Technical Features:**")
        print("   • DeepSeek-R1 model with transformers pipeline")
        print("   • Google Custom Search API integration")
        print("   • Multi-layer fallback system (DeepSeek → HF API → Intelligent)")
        print("   • Persistent conversation memory")
        print("   • Web search result caching")
        print("   • Context-aware response generation")
        
        print("\n🌐 **Configuration Status:**")
        print(f"   • Google API Key: {'✅ Configured' if os.getenv('GOOGLE_API_KEY') else '❌ Missing'}")
        print(f"   • DeepSeek Model: {'✅ Enabled' if os.getenv('ENABLE_DEEPSEEK_MODEL', 'True').lower() == 'true' else '❌ Disabled'}")
        print(f"   • Web Search: {'✅ Enabled' if os.getenv('ENABLE_WEB_SEARCH', 'True').lower() == 'true' else '❌ Disabled'}")
        print(f"   • Memory System: {'✅ Enabled' if os.getenv('ENABLE_MEMORY_SYSTEM', 'True').lower() == 'true' else '❌ Disabled'}")
        
        print("\n🌐 Test in the web interface:")
        print("   1. Go to: http://127.0.0.1:8000/ai-agent/")
        print("   2. Login as 'Hamada' (or create account)")
        print("   3. Try these enhanced queries:")
        print("      • 'Analyze my Jira data using advanced AI'")
        print("      • 'What are the latest DevOps trends for 2024?'")
        print("      • 'How can I improve my team's productivity based on my data?'")
        print("      • 'Remember our previous discussion and provide updates'")
        
        print("\n💡 **The Enhanced AI Agent now provides:**")
        print("   • DeepSeek-R1 powered intelligent analysis")
        print("   • Real-time web search capabilities")
        print("   • Advanced reasoning and context understanding")
        print("   • Professional-grade insights and recommendations")
        print("   • Seamless integration of multiple data sources")
        
    else:
        print("❌ Test failed. Check the error messages above.")
        print("\n🔧 Troubleshooting:")
        print("   • Ensure all dependencies are installed (transformers, torch)")
        print("   • Check .env file for proper configuration")
        print("   • Verify Google API key is valid")
        print("   • Ensure sufficient system resources for DeepSeek model")
        print("   • Check internet connection for web search")

if __name__ == "__main__":
    main()
