import os
import openai
import requests
from datetime import datetime, timedelta
from django.conf import settings
from dotenv import load_dotenv
from .chat_models import ChatMessage

# Load environment variables
load_dotenv()

class GPTHandler:
    def __init__(self):
        # Initialize OpenAI client with API key from environment variables
        self.api_key = os.getenv('OPENAI_API_KEY')
        if not self.api_key:
            raise ValueError("OpenAI API key not found in environment variables")
        self.client = openai.OpenAI(api_key=self.api_key)
        
        # Define system prompt for Jira ticket analysis context
        self.system_prompt = """
        You are an AI assistant specialized in analyzing Jira ticket data and providing insights.
        You have access to:
        - Ticket metrics and distributions
        - Client-specific data and trends
        - Sentiment analysis results
        - Historical patterns and trends
        - Web search capabilities for best practices
        
        Your role is to:
        1. Answer questions about ticket data and trends
        2. Provide insights on client-specific patterns
        3. Explain sentiment analysis results
        4. Suggest improvements based on historical data
        5. Search and incorporate web-based best practices
        
        Always provide clear, actionable insights backed by data.
        When appropriate, enhance your responses with relevant web-sourced information.
        """

    async def generate_response(self, user_message, conversation_id=None, context_data=None):
        """Generate a response using the OpenAI API with conversation history, context, and web search capabilities."""
        try:
            # Get conversation history
            messages = self._get_conversation_history(conversation_id) if conversation_id else []
            
            # Add system prompt
            messages.insert(0, {"role": "system", "content": self.system_prompt})
            
            # Add context data if available
            if context_data:
                context_message = self._format_context_data(context_data)
                messages.append({"role": "system", "content": context_message})
            
            # Perform web search if needed based on user message
            web_info = self._get_web_information(user_message) if self._should_search_web(user_message) else None
            if web_info:
                messages.append({"role": "system", "content": f"Relevant web information:\n{web_info}"})
            
            # Add user message
            messages.append({"role": "user", "content": user_message})
            
            # Generate response using OpenAI API
            response = await self.client.chat.completions.create(
                model="gpt-4",  # Using GPT-4 for better analysis
                messages=messages,
                temperature=0.7,  # Balanced between creativity and consistency
                max_tokens=1000,  # Reasonable length for detailed responses
                top_p=0.9,
                frequency_penalty=0.5,
                presence_penalty=0.5
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            return f"I encountered an error: {str(e)}. Please try again."

    def _get_conversation_history(self, conversation_id, limit=5):
        """Retrieve recent conversation history for context."""
        recent_messages = ChatMessage.objects.filter(
            conversation_id=conversation_id
        ).order_by('-created_at')[:limit]

        messages = []
        for msg in reversed(recent_messages):  # Reverse to get chronological order
            role = "assistant" if msg.message_type == "ai" else "user"
            messages.append({"role": role, "content": msg.content})
            
        return messages

    def _format_context_data(self, context_data):
        """Format context data into a structured message."""
        context_message = "Here's the relevant context for your reference:\n\n"
        
        # Add basic analysis data
        if context_data.get('latest_analysis'):
            analysis = context_data['latest_analysis']
            context_message += f"Latest Analysis:\n"
            context_message += f"- Total Issues: {analysis.get('issue_count', 'N/A')}\n"
            context_message += f"- Ticket Types: {analysis.get('ticket_types', {})}\n"
            context_message += f"- Priority Distribution: {analysis.get('priority_distribution', {})}\n"
            context_message += f"- Status Distribution: {analysis.get('status_distribution', {})}\n"
            context_message += f"- Common Themes: {', '.join(analysis.get('common_themes', []))}\n\n"
        
        # Add client metrics if available
        if context_data.get('client_metrics'):
            context_message += f"Client Metrics:\n"
            for client, metrics in context_data['client_metrics'].items():
                context_message += f"- {client}: {metrics}\n"
            context_message += "\n"
        
        # Add sentiment data if available
        if context_data.get('sentiment'):
            sentiment = context_data['sentiment']
            context_message += f"Sentiment Analysis:\n"
            context_message += f"- Overall Sentiment: {sentiment.get('overall', 'N/A')}\n"
            context_message += f"- Positive Topics: {', '.join(sentiment.get('positive_topics', []))}\n"
            context_message += f"- Negative Topics: {', '.join(sentiment.get('negative_topics', []))}\n"
            
        return context_message

    def _should_search_web(self, message):
        """Determine if web search would be beneficial based on the message content."""
        web_search_keywords = [
            'best practice', 'industry standard', 'recommendation',
            'how to', 'example', 'tutorial', 'guide', 'documentation',
            'common solution', 'typical approach'
        ]
        return any(keyword in message.lower() for keyword in web_search_keywords)

    def _get_web_information(self, query):
        """Perform a web search and return relevant information."""
        try:
            # Use a search API or web scraping to gather information
            # This is a placeholder - implement actual web search logic
            return "Relevant web information would be fetched here based on the query."
        except Exception as e:
            return None