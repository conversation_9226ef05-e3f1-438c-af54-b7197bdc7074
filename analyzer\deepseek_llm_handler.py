import logging
import requests
import json
from dotenv import load_dotenv

# Set up logging
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

class DeepSeekLLMHandler:
    """
    Enhanced LLM handler using Ollama DeepSeek 1.5B model for local inference
    """

    def __init__(self):
        # Ollama configuration
        self.ollama_base_url = "http://localhost:11434"
        self.ollama_api_url = f"{self.ollama_base_url}/api"

        # Model configuration with priority order
        self.model_candidates = [
            {
                "name": "deepseek-coder:1.3b",
                "type": "deepseek_coder",
                "description": "DeepSeek Coder 1.3B (Ollama)",
                "ollama_model": True
            },
            {
                "name": "deepseek-coder:latest",
                "type": "deepseek_latest",
                "description": "DeepSeek Coder Latest (Ollama)",
                "ollama_model": True
            },
            {
                "name": "llama3.2:1b",
                "type": "llama_fallback",
                "description": "Llama 3.2 1B (Ollama Fallback)",
                "ollama_model": True
            }
        ]

        self.current_model = None
        self.model_type = None
        self.max_tokens = 2048
        self.is_loaded = False
        self.ollama_available = False

        # Initialize Ollama connection
        self._initialize_ollama()

    def _initialize_ollama(self):
        """
        Initialize Ollama connection and find the best available model
        """
        try:
            # Check if Ollama service is running
            response = requests.get(f"{self.ollama_api_url}/tags", timeout=5)
            if response.status_code == 200:
                self.ollama_available = True
                available_models = response.json().get('models', [])
                logger.info(f"Ollama service available with {len(available_models)} models")

                # Find the best available model
                self._select_best_model(available_models)
            else:
                logger.warning(f"Ollama service responded with status {response.status_code}")
                self.ollama_available = False

        except requests.exceptions.RequestException as e:
            logger.warning(f"Ollama service not available: {str(e)}")
            self.ollama_available = False

        except Exception as e:
            logger.error(f"Error initializing Ollama: {str(e)}")
            self.ollama_available = False

    def _select_best_model(self, available_models):
        """
        Select the best available model from Ollama
        """
        available_model_names = [model.get('name', '') for model in available_models]
        logger.info(f"Available Ollama models: {available_model_names}")

        # Try to find the best model from our candidates
        for candidate in self.model_candidates:
            model_name = candidate["name"]
            if model_name in available_model_names:
                self.current_model = model_name
                self.model_type = candidate["type"]
                self.is_loaded = True
                logger.info(f"✅ Selected Ollama model: {candidate['description']}")
                return

        # If no preferred models found, try to pull DeepSeek Coder
        logger.info("No preferred models found, attempting to pull deepseek-coder:1.3b")
        if self._pull_model("deepseek-coder:1.3b"):
            self.current_model = "deepseek-coder:1.3b"
            self.model_type = "deepseek_coder"
            self.is_loaded = True
            logger.info("✅ Successfully pulled and selected deepseek-coder:1.3b")
        else:
            logger.warning("Failed to pull DeepSeek model, will use fallback")
            self.is_loaded = False

    def _pull_model(self, model_name):
        """
        Pull a model using Ollama API
        """
        try:
            logger.info(f"Pulling Ollama model: {model_name}")
            response = requests.post(
                f"{self.ollama_api_url}/pull",
                json={"name": model_name},
                timeout=300  # 5 minutes timeout for model pulling
            )

            if response.status_code == 200:
                logger.info(f"Successfully pulled model: {model_name}")
                return True
            else:
                logger.error(f"Failed to pull model {model_name}: {response.status_code}")
                return False

        except Exception as e:
            logger.error(f"Error pulling model {model_name}: {str(e)}")
            return False



    async def generate_response(self, user_message, conversation_id=None, context_data=None):
        """
        Generate response using Ollama DeepSeek model
        """
        if not self.ollama_available or not self.is_loaded:
            logger.warning("Ollama DeepSeek model not available, using fallback")
            return self._get_fallback_response(user_message, context_data)

        try:
            # Prepare the prompt with context
            prompt = self._prepare_prompt(user_message, context_data)

            # Make request to Ollama API
            response = await self._call_ollama_api(prompt)

            if response:
                return self._clean_response(response, user_message)
            else:
                logger.warning("Ollama API returned empty response, using fallback")
                return self._get_fallback_response(user_message, context_data)

        except Exception as e:
            logger.error(f"Error generating response with Ollama: {str(e)}")
            return self._get_fallback_response(user_message, context_data)

    async def _call_ollama_api(self, prompt):
        """
        Call Ollama API for text generation
        """
        try:
            payload = {
                "model": self.current_model,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": 0.7,
                    "top_p": 0.9,
                    "num_predict": self.max_tokens,
                    "stop": ["User:", "Human:", "\n\n"]
                }
            }

            logger.info(f"Calling Ollama API with model: {self.current_model}")

            response = requests.post(
                f"{self.ollama_api_url}/generate",
                json=payload,
                timeout=60  # 60 seconds timeout
            )

            if response.status_code == 200:
                result = response.json()
                generated_text = result.get('response', '').strip()

                if generated_text:
                    logger.info(f"Ollama API response received ({len(generated_text)} chars)")
                    return generated_text
                else:
                    logger.warning("Ollama API returned empty response")
                    return None
            else:
                logger.error(f"Ollama API error: {response.status_code} - {response.text}")
                return None

        except requests.exceptions.Timeout:
            logger.error("Ollama API request timed out")
            return None
        except requests.exceptions.RequestException as e:
            logger.error(f"Ollama API request failed: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error calling Ollama API: {str(e)}")
            return None

    def _prepare_prompt(self, user_message, context_data):
        """
        Prepare prompt with context for Ollama
        """
        prompt = "You are an intelligent AI assistant specialized in Jira analysis and general knowledge.\n\n"

        if context_data and context_data.get('latest_analysis'):
            analysis = context_data['latest_analysis']
            prompt += f"Current Jira Analysis Context:\n"
            prompt += f"- File: {analysis.get('file_name', 'Unknown')}\n"
            prompt += f"- Total Issues: {analysis.get('issue_count', 0)}\n"
            prompt += f"- Clients: {context_data.get('data_summary', {}).get('total_clients', 0)}\n"

            if context_data.get('sentiment'):
                sentiment_summary = context_data.get('data_summary', {}).get('sentiment_summary', 'N/A')
                prompt += f"- Sentiment: {sentiment_summary}\n"

            prompt += "\n"

        prompt += f"User: {user_message}\n\nAssistant:"
        return prompt

    def _clean_response(self, response, user_message):
        """
        Clean and format the Ollama response
        """
        # Remove any repetition of the user message
        if user_message.lower() in response.lower():
            response = response.replace(user_message, "").strip()

        # Remove common artifacts
        response = response.replace("User:", "").replace("Assistant:", "").strip()

        # Remove any system prompts that might leak through
        response = response.replace("You are an intelligent AI assistant", "").strip()

        # Limit response length
        if len(response) > 1500:
            response = response[:1500] + "..."

        return response.strip()

    def _get_fallback_response(self, user_message, context_data):
        """
        Provide fallback response when Ollama model is not available
        """
        # Import the existing fallback system
        try:
            from .opensource_llm_integration import OpenSourceLLMHandler
            fallback_handler = OpenSourceLLMHandler()
            return fallback_handler._get_fallback_response(user_message, context_data)
        except Exception as e:
            logger.error(f"Fallback system error: {str(e)}")
            return "I'm currently experiencing technical difficulties. Please try again later or rephrase your question."

    def get_model_info(self):
        """
        Get information about the current Ollama model
        """
        return {
            "name": self.current_model or "Not loaded",
            "type": self.model_type or "Unknown",
            "is_loaded": self.is_loaded,
            "ollama_available": self.ollama_available,
            "method": "ollama_api" if self.ollama_available else "fallback",
            "base_url": self.ollama_base_url
        }

    def is_model_ready(self):
        """
        Check if the Ollama model is ready for inference
        """
        return self.ollama_available and self.is_loaded
