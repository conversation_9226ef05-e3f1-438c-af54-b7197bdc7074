import logging
import torch
from dotenv import load_dotenv

# Set up logging
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

class DeepSeekLLMHandler:
    """
    Enhanced LLM handler using DeepSeek-R1 model for local inference
    """
    
    def __init__(self):
        self.model = None
        self.tokenizer = None
        self.pipeline = None

        # Use CPU-compatible models based on device availability
        self.device = "cuda" if torch.cuda.is_available() else "cpu"

        if self.device == "cpu":
            # Use smaller, CPU-compatible model for CPU-only systems
            self.model_name = "microsoft/DialoGPT-medium"
            logger.info("Using CPU-compatible model: microsoft/DialoGPT-medium")
        else:
            # Use DeepSeek for GPU systems
            self.model_name = "deepseek-ai/DeepSeek-R1-0528"
            logger.info("Using GPU-optimized model: deepseek-ai/DeepSeek-R1-0528")

        self.max_length = 2048
        self.is_loaded = False

        # Try to load the model
        self._load_model()

    def _load_model(self):
        """
        Load the appropriate model with CPU/GPU compatibility
        """
        try:
            logger.info(f"Loading model: {self.model_name}")
            logger.info(f"Using device: {self.device}")

            # Try pipeline approach first
            from transformers import pipeline

            # Configure based on model and device
            pipeline_kwargs = {
                "model": self.model_name,
                "torch_dtype": torch.float32,
                "device": -1 if self.device == "cpu" else 0,  # -1 for CPU, 0 for GPU
            }

            # Add trust_remote_code only for DeepSeek models
            if "deepseek" in self.model_name.lower():
                pipeline_kwargs["trust_remote_code"] = True

            # GPU-specific optimizations
            if self.device == "cuda":
                pipeline_kwargs["torch_dtype"] = torch.float16
                pipeline_kwargs["device_map"] = "auto"

            self.pipeline = pipeline("text-generation", **pipeline_kwargs)

            self.is_loaded = True
            logger.info(f"✅ Model loaded successfully using pipeline: {self.model_name}")

        except Exception as e:
            logger.warning(f"Pipeline loading failed: {str(e)}")

            # Fallback to direct model loading
            try:
                from transformers import AutoTokenizer, AutoModelForCausalLM

                logger.info("Trying direct model loading...")

                # Configure tokenizer
                tokenizer_kwargs = {}
                if "deepseek" in self.model_name.lower():
                    tokenizer_kwargs["trust_remote_code"] = True

                self.tokenizer = AutoTokenizer.from_pretrained(
                    self.model_name,
                    **tokenizer_kwargs
                )

                # Configure model
                model_kwargs = {
                    "torch_dtype": torch.float32,
                    "low_cpu_mem_usage": True,
                }

                if "deepseek" in self.model_name.lower():
                    model_kwargs["trust_remote_code"] = True

                # GPU-specific optimizations
                if self.device == "cuda":
                    model_kwargs["device_map"] = "auto"
                    model_kwargs["torch_dtype"] = torch.float16

                self.model = AutoModelForCausalLM.from_pretrained(
                    self.model_name,
                    **model_kwargs
                )

                # Move to device after loading
                self.model = self.model.to(self.device)

                self.is_loaded = True
                logger.info(f"✅ Model loaded successfully using direct loading: {self.model_name}")

            except Exception as e2:
                logger.error(f"Failed to load model {self.model_name}: {str(e2)}")
                logger.info("Will use fallback LLM system")
                self.is_loaded = False

    async def generate_response(self, user_message, conversation_id=None, context_data=None):
        """
        Generate response using DeepSeek-R1 model
        """
        if not self.is_loaded:
            logger.warning("DeepSeek model not loaded, using fallback")
            return self._get_fallback_response(user_message, context_data)
        
        try:
            # Prepare conversation messages in the required format
            messages = self._prepare_conversation_messages(user_message, context_data)
            
            # Generate response
            if self.pipeline:
                response = await self._generate_with_pipeline(messages)
            else:
                response = await self._generate_with_model(messages)
            
            if response and len(response.strip()) > 10:
                return self._clean_response(response, user_message)
            else:
                return self._get_fallback_response(user_message, context_data)
                
        except Exception as e:
            logger.error(f"Error generating response with DeepSeek: {str(e)}")
            return self._get_fallback_response(user_message, context_data)

    def _prepare_conversation_messages(self, user_message, context_data):
        """
        Prepare conversation messages in the format expected by DeepSeek-R1
        """
        messages = []
        
        # System message with context
        system_content = "You are an intelligent AI assistant specialized in Jira analysis and general knowledge. "
        
        if context_data and context_data.get('latest_analysis'):
            analysis = context_data['latest_analysis']
            system_content += f"\n\nCurrent Jira Analysis Context:\n"
            system_content += f"- File: {analysis.get('file_name', 'Unknown')}\n"
            system_content += f"- Total Issues: {analysis.get('issue_count', 0)}\n"
            system_content += f"- Clients: {context_data.get('data_summary', {}).get('total_clients', 0)}\n"
            
            if context_data.get('sentiment'):
                sentiment_summary = context_data.get('data_summary', {}).get('sentiment_summary', 'N/A')
                system_content += f"- Sentiment: {sentiment_summary}\n"
        
        system_content += "\nProvide helpful, accurate, and specific responses based on the available data."
        
        messages.append({"role": "system", "content": system_content})
        messages.append({"role": "user", "content": user_message})
        
        return messages

    async def _generate_with_pipeline(self, messages):
        """
        Generate response using the pipeline approach
        """
        try:
            # Convert messages to text format for pipeline
            conversation_text = self._messages_to_text(messages)
            
            # Generate response
            outputs = self.pipeline(
                conversation_text,
                max_new_tokens=512,
                temperature=0.7,
                do_sample=True,
                pad_token_id=self.pipeline.tokenizer.eos_token_id,
                return_full_text=False
            )
            
            if outputs and len(outputs) > 0:
                return outputs[0]['generated_text'].strip()
            
        except Exception as e:
            logger.error(f"Pipeline generation error: {str(e)}")
            
        return None

    async def _generate_with_model(self, messages):
        """
        Generate response using direct model approach
        """
        try:
            # Convert messages to text format
            conversation_text = self._messages_to_text(messages)
            
            # Tokenize input
            inputs = self.tokenizer.encode(conversation_text, return_tensors="pt")
            inputs = inputs.to(self.device)
            
            # Generate response
            with torch.no_grad():
                outputs = self.model.generate(
                    inputs,
                    max_new_tokens=512,
                    temperature=0.7,
                    do_sample=True,
                    pad_token_id=self.tokenizer.eos_token_id,
                    attention_mask=torch.ones_like(inputs)
                )
            
            # Decode response
            response = self.tokenizer.decode(outputs[0][inputs.shape[1]:], skip_special_tokens=True)
            return response.strip()
            
        except Exception as e:
            logger.error(f"Direct model generation error: {str(e)}")
            
        return None

    def _messages_to_text(self, messages):
        """
        Convert messages format to text format for the model
        """
        conversation_text = ""
        for message in messages:
            role = message["role"]
            content = message["content"]
            
            if role == "system":
                conversation_text += f"System: {content}\n\n"
            elif role == "user":
                conversation_text += f"User: {content}\n\n"
            elif role == "assistant":
                conversation_text += f"Assistant: {content}\n\n"
        
        conversation_text += "Assistant:"
        return conversation_text

    def _clean_response(self, response, user_message):
        """
        Clean and format the model response
        """
        # Remove any repetition of the user message
        if user_message.lower() in response.lower():
            response = response.replace(user_message, "").strip()
        
        # Remove common artifacts
        response = response.replace("User:", "").replace("Assistant:", "").strip()
        
        # Limit response length
        if len(response) > 1000:
            response = response[:1000] + "..."
        
        return response.strip()

    def _get_fallback_response(self, user_message, context_data):
        """
        Provide fallback response when DeepSeek model is not available
        """
        # Import the existing fallback system
        try:
            from .opensource_llm_integration import OpenSourceLLMHandler
            fallback_handler = OpenSourceLLMHandler()
            return fallback_handler._get_fallback_response(user_message, context_data)
        except Exception as e:
            logger.error(f"Fallback system error: {str(e)}")
            return "I'm currently experiencing technical difficulties. Please try again later or rephrase your question."

    def get_model_info(self):
        """
        Get information about the current model
        """
        return {
            "name": self.model_name,
            "device": self.device,
            "is_loaded": self.is_loaded,
            "method": "pipeline" if self.pipeline else "direct" if self.model else "fallback",
            "cuda_available": torch.cuda.is_available()
        }

    def is_model_ready(self):
        """
        Check if the model is ready for inference
        """
        return self.is_loaded
