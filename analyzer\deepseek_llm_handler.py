import logging
import torch
from dotenv import load_dotenv

# Set up logging
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

class DeepSeekLLMHandler:
    """
    Enhanced LLM handler using DeepSeek-R1 model for local inference
    """
    
    def __init__(self):
        self.model = None
        self.tokenizer = None
        self.pipeline = None

        # Enhanced model selection with local DeepSeek support
        self.device = "cuda" if torch.cuda.is_available() else "cpu"

        # Priority order: Local DeepSeek > Cached DeepSeek > CPU-compatible fallback
        self.model_candidates = [
            {
                "name": "deepseek-ai/DeepSeek-R1-0528",
                "type": "deepseek",
                "description": "DeepSeek R1 (cached)",
                "cpu_compatible": True  # We'll configure it for CPU
            },
            {
                "name": "deepseek-ai/deepseek-coder-1.3b-base",
                "type": "deepseek_coder",
                "description": "DeepSeek Coder 1.3B",
                "cpu_compatible": True
            },
            {
                "name": "microsoft/DialoGPT-medium",
                "type": "dialogpt",
                "description": "DialoGPT Medium (fallback)",
                "cpu_compatible": True
            }
        ]

        self.model_name = None
        self.model_type = None
        self.max_length = 2048
        self.is_loaded = False

        # Try to load the best available model
        self._load_best_model()

    def _load_best_model(self):
        """
        Try to load the best available model from candidates
        """
        for candidate in self.model_candidates:
            if self.device == "cpu" and not candidate["cpu_compatible"]:
                continue

            logger.info(f"Attempting to load: {candidate['description']}")

            try:
                self.model_name = candidate["name"]
                self.model_type = candidate["type"]

                if self._load_specific_model(candidate):
                    logger.info(f"✅ Successfully loaded: {candidate['description']}")
                    return

            except Exception as e:
                logger.warning(f"Failed to load {candidate['description']}: {str(e)}")
                continue

        logger.error("Failed to load any model")
        self.is_loaded = False

    def _load_specific_model(self, candidate):
        """
        Load a specific model with optimized configuration
        """
        try:
            from transformers import pipeline, AutoTokenizer, AutoModelForCausalLM

            # Try pipeline approach first
            pipeline_kwargs = {
                "model": candidate["name"],
                "torch_dtype": torch.float32,
                "device": -1 if self.device == "cpu" else 0,
                "max_length": 512,  # Reduced for better performance
            }

            # Add trust_remote_code for DeepSeek models
            if candidate["type"] in ["deepseek", "deepseek_coder"]:
                pipeline_kwargs["trust_remote_code"] = True

                # Special CPU configuration for DeepSeek models
                if self.device == "cpu":
                    pipeline_kwargs.update({
                        "torch_dtype": torch.float32,
                        "low_cpu_mem_usage": True,
                    })

            self.pipeline = pipeline("text-generation", **pipeline_kwargs)
            self.is_loaded = True
            return True

        except Exception as e:
            logger.warning(f"Pipeline loading failed for {candidate['name']}: {str(e)}")

            # Try direct loading as fallback
            try:
                tokenizer_kwargs = {}
                model_kwargs = {
                    "torch_dtype": torch.float32,
                    "low_cpu_mem_usage": True,
                }

                if candidate["type"] in ["deepseek", "deepseek_coder"]:
                    tokenizer_kwargs["trust_remote_code"] = True
                    model_kwargs["trust_remote_code"] = True

                self.tokenizer = AutoTokenizer.from_pretrained(
                    candidate["name"], **tokenizer_kwargs
                )

                self.model = AutoModelForCausalLM.from_pretrained(
                    candidate["name"], **model_kwargs
                )

                self.model = self.model.to(self.device)
                self.is_loaded = True
                return True

            except Exception as e2:
                logger.warning(f"Direct loading failed for {candidate['name']}: {str(e2)}")
                return False



    async def generate_response(self, user_message, conversation_id=None, context_data=None):
        """
        Generate response using DeepSeek-R1 model
        """
        if not self.is_loaded:
            logger.warning("DeepSeek model not loaded, using fallback")
            return self._get_fallback_response(user_message, context_data)
        
        try:
            # Prepare conversation messages in the required format
            messages = self._prepare_conversation_messages(user_message, context_data)
            
            # Generate response
            if self.pipeline:
                response = await self._generate_with_pipeline(messages)
            else:
                response = await self._generate_with_model(messages)
            
            if response and len(response.strip()) > 10:
                return self._clean_response(response, user_message)
            else:
                return self._get_fallback_response(user_message, context_data)
                
        except Exception as e:
            logger.error(f"Error generating response with DeepSeek: {str(e)}")
            return self._get_fallback_response(user_message, context_data)

    def _prepare_conversation_messages(self, user_message, context_data):
        """
        Prepare conversation messages in the format expected by DeepSeek-R1
        """
        messages = []
        
        # System message with context
        system_content = "You are an intelligent AI assistant specialized in Jira analysis and general knowledge. "
        
        if context_data and context_data.get('latest_analysis'):
            analysis = context_data['latest_analysis']
            system_content += f"\n\nCurrent Jira Analysis Context:\n"
            system_content += f"- File: {analysis.get('file_name', 'Unknown')}\n"
            system_content += f"- Total Issues: {analysis.get('issue_count', 0)}\n"
            system_content += f"- Clients: {context_data.get('data_summary', {}).get('total_clients', 0)}\n"
            
            if context_data.get('sentiment'):
                sentiment_summary = context_data.get('data_summary', {}).get('sentiment_summary', 'N/A')
                system_content += f"- Sentiment: {sentiment_summary}\n"
        
        system_content += "\nProvide helpful, accurate, and specific responses based on the available data."
        
        messages.append({"role": "system", "content": system_content})
        messages.append({"role": "user", "content": user_message})
        
        return messages

    async def _generate_with_pipeline(self, messages):
        """
        Generate response using the pipeline approach
        """
        try:
            # Convert messages to text format for pipeline
            conversation_text = self._messages_to_text(messages)
            
            # Generate response
            outputs = self.pipeline(
                conversation_text,
                max_new_tokens=512,
                temperature=0.7,
                do_sample=True,
                pad_token_id=self.pipeline.tokenizer.eos_token_id,
                return_full_text=False
            )
            
            if outputs and len(outputs) > 0:
                return outputs[0]['generated_text'].strip()
            
        except Exception as e:
            logger.error(f"Pipeline generation error: {str(e)}")
            
        return None

    async def _generate_with_model(self, messages):
        """
        Generate response using direct model approach
        """
        try:
            # Convert messages to text format
            conversation_text = self._messages_to_text(messages)
            
            # Tokenize input
            inputs = self.tokenizer.encode(conversation_text, return_tensors="pt")
            inputs = inputs.to(self.device)
            
            # Generate response
            with torch.no_grad():
                outputs = self.model.generate(
                    inputs,
                    max_new_tokens=512,
                    temperature=0.7,
                    do_sample=True,
                    pad_token_id=self.tokenizer.eos_token_id,
                    attention_mask=torch.ones_like(inputs)
                )
            
            # Decode response
            response = self.tokenizer.decode(outputs[0][inputs.shape[1]:], skip_special_tokens=True)
            return response.strip()
            
        except Exception as e:
            logger.error(f"Direct model generation error: {str(e)}")
            
        return None

    def _messages_to_text(self, messages):
        """
        Convert messages format to text format for the model
        """
        conversation_text = ""
        for message in messages:
            role = message["role"]
            content = message["content"]
            
            if role == "system":
                conversation_text += f"System: {content}\n\n"
            elif role == "user":
                conversation_text += f"User: {content}\n\n"
            elif role == "assistant":
                conversation_text += f"Assistant: {content}\n\n"
        
        conversation_text += "Assistant:"
        return conversation_text

    def _clean_response(self, response, user_message):
        """
        Clean and format the model response
        """
        # Remove any repetition of the user message
        if user_message.lower() in response.lower():
            response = response.replace(user_message, "").strip()
        
        # Remove common artifacts
        response = response.replace("User:", "").replace("Assistant:", "").strip()
        
        # Limit response length
        if len(response) > 1000:
            response = response[:1000] + "..."
        
        return response.strip()

    def _get_fallback_response(self, user_message, context_data):
        """
        Provide fallback response when DeepSeek model is not available
        """
        # Import the existing fallback system
        try:
            from .opensource_llm_integration import OpenSourceLLMHandler
            fallback_handler = OpenSourceLLMHandler()
            return fallback_handler._get_fallback_response(user_message, context_data)
        except Exception as e:
            logger.error(f"Fallback system error: {str(e)}")
            return "I'm currently experiencing technical difficulties. Please try again later or rephrase your question."

    def get_model_info(self):
        """
        Get information about the current model
        """
        return {
            "name": self.model_name,
            "device": self.device,
            "is_loaded": self.is_loaded,
            "method": "pipeline" if self.pipeline else "direct" if self.model else "fallback",
            "cuda_available": torch.cuda.is_available()
        }

    def is_model_ready(self):
        """
        Check if the model is ready for inference
        """
        return self.is_loaded
