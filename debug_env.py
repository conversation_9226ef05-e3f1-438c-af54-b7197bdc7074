#!/usr/bin/env python
"""
Debug environment variable loading
"""
import os
from dotenv import load_dotenv

print("🔍 Environment Variable Debug")
print("=" * 40)

# Check current working directory
print(f"Current working directory: {os.getcwd()}")

# Check if .env file exists
env_file_path = ".env"
env_exists = os.path.exists(env_file_path)
print(f".env file exists: {env_exists}")

if env_exists:
    # Read .env file content
    with open(env_file_path, 'r') as f:
        content = f.read()
    print(f".env file content:\n{content}")

# Load environment variables
print("\nLoading environment variables...")
load_dotenv()

# Check what's actually loaded
api_key = os.getenv('OPENAI_API_KEY')
print(f"Loaded API key: {api_key}")

if api_key:
    print(f"API key length: {len(api_key)}")
    print(f"API key starts with 'sk-': {api_key.startswith('sk-')}")
    print(f"First 20 chars: {api_key[:20]}")
    print(f"Last 20 chars: {api_key[-20:]}")

# Try loading with explicit path
print("\nTrying explicit path loading...")
load_dotenv(dotenv_path=".env", override=True)
api_key_explicit = os.getenv('OPENAI_API_KEY')
print(f"Explicitly loaded API key: {api_key_explicit}")

# Check all environment variables containing 'OPENAI'
print("\nAll OPENAI environment variables:")
for key, value in os.environ.items():
    if 'OPENAI' in key:
        print(f"{key}: {value[:20]}...{value[-20:] if len(value) > 40 else value}")
