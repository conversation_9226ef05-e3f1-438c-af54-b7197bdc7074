#!/usr/bin/env python
"""
Test Open Source LLM integration
"""
import os
import sys
import django
from django.conf import settings

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'jira_analyzer.settings')
django.setup()

from django.contrib.auth.models import User
from analyzer.chat_handler import ChatHandler
from analyzer.opensource_llm_integration import OpenSourceLLMHandler
import asyncio
import nest_asyncio

# Apply nest_asyncio to handle nested event loops
nest_asyncio.apply()

def test_llm_handler_initialization():
    """Test LLM Handler initialization"""
    print("🔧 Testing Open Source LLM Handler Initialization...")
    
    try:
        handler = OpenSourceLLMHandler()
        print("✅ LLM Handler initialized successfully")
        print(f"Available models: {len(handler.available_models)}")
        for i, model in enumerate(handler.available_models):
            print(f"   {i+1}. {model['name']} ({model['type']})")
        return handler
    except Exception as e:
        print(f"❌ LLM Handler initialization failed: {str(e)}")
        return None

async def test_direct_llm_response(handler):
    """Test direct LLM response generation"""
    print("\n🌐 Testing Direct LLM Response...")
    
    if not handler:
        print("❌ No handler available for testing")
        return False
    
    try:
        # Test with a simple message
        test_message = "Hello! Can you help me analyze Jira tickets?"
        print(f"📤 Sending: '{test_message}'")
        
        response = await handler.generate_response(test_message)
        
        print("✅ LLM response received!")
        print(f"📥 Response: {response}")
        
        # Check model info
        model_info = handler.get_model_info()
        print(f"🤖 Model used: {model_info['name']} ({model_info['provider']})")
        
        return True
        
    except Exception as e:
        print(f"❌ Direct LLM test failed: {str(e)}")
        return False

def test_chat_handler():
    """Test Chat Handler with new LLM"""
    print("\n🔗 Testing Chat Handler Integration...")
    
    try:
        # Get or create a test user
        user, created = User.objects.get_or_create(
            username='test_user',
            defaults={'email': '<EMAIL>'}
        )
        
        handler = ChatHandler(user)
        print("✅ Chat Handler initialized successfully")
        return handler, user
    except Exception as e:
        print(f"❌ Chat Handler initialization failed: {str(e)}")
        return None, None

async def test_full_integration(chat_handler):
    """Test the full integration end-to-end"""
    print("\n🎯 Testing Full Integration...")
    
    if not chat_handler:
        print("❌ No chat handler available for testing")
        return False
    
    try:
        # Test different types of messages
        test_messages = [
            "Hello! I'm new here.",
            "Can you help me analyze my Jira data?",
            "What trends should I look for in ticket data?",
            "How can I improve client satisfaction?"
        ]
        
        for i, message in enumerate(test_messages, 1):
            print(f"\n📤 Test {i}: '{message}'")
            
            result = await chat_handler.process_message(message)
            
            print(f"📥 Status: {result['status']}")
            print(f"📥 Response: {result['response'][:100]}...")
            print(f"📥 Conversation ID: {result['conversation_id']}")
            
            if result['status'] != 'success':
                print(f"⚠️  Warning: Status is {result['status']}")
        
        print("\n✅ Full integration test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Full integration test failed: {str(e)}")
        return False

async def main():
    """Run all tests"""
    print("🧪 Open Source LLM Integration Tests")
    print("=" * 60)
    
    # Test 1: LLM Handler Initialization
    llm_handler = test_llm_handler_initialization()
    
    # Test 2: Direct LLM Response
    direct_success = await test_direct_llm_response(llm_handler) if llm_handler else False
    
    # Test 3: Chat Handler Integration
    chat_handler, user = test_chat_handler()
    
    # Test 4: Full Integration
    integration_success = await test_full_integration(chat_handler) if chat_handler else False
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Summary:")
    print(f"LLM Handler Init: {'✅ PASS' if llm_handler else '❌ FAIL'}")
    print(f"Direct LLM Response: {'✅ PASS' if direct_success else '❌ FAIL'}")
    print(f"Chat Handler Init: {'✅ PASS' if chat_handler else '❌ FAIL'}")
    print(f"Full Integration: {'✅ PASS' if integration_success else '❌ FAIL'}")
    
    if all([llm_handler, direct_success, chat_handler, integration_success]):
        print("\n🎉 All tests passed! Open Source LLM integration is working!")
        print("\n📋 Benefits of the new system:")
        print("   ✅ No API costs or quota limitations")
        print("   ✅ Multiple fallback models for reliability")
        print("   ✅ Works without internet (fallback responses)")
        print("   ✅ Privacy-friendly (no data sent to paid services)")
        print("   ✅ Fully open source and transparent")
    else:
        print("\n⚠️  Some tests failed, but the system has fallback capabilities.")
        print("   The AI Agent will still work with rule-based responses.")
        
    print("\n🔧 Next Steps:")
    print("   1. Restart the Django server")
    print("   2. Test the AI Agent in the web interface")
    print("   3. Optional: Get a free Hugging Face token for better rate limits")

if __name__ == "__main__":
    asyncio.run(main())
