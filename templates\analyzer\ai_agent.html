{% extends 'base.html' %}

{% block title %}AI Agent - Vermeg Insights{% endblock %}

{% block extra_css %}
<style>
    .chat-container {
        height: 60vh;
        overflow-y: auto;
        padding: 1rem;
        background-color: var(--light);
        border-radius: var(--border-radius);
        margin-bottom: 1rem;
    }

    .message {
        margin-bottom: 1rem;
        max-width: 80%;
        display: flex;
        flex-direction: column;
    }

    .message.user-message {
        margin-left: auto;
        align-items: flex-end;
    }

    .message.ai-message {
        margin-right: auto;
        align-items: flex-start;
    }

    .message-content {
        padding: 0.75rem 1rem;
        border-radius: var(--border-radius);
        position: relative;
    }

    .user-message .message-content {
        background-color: var(--primary);
        color: var(--white);
    }

    .ai-message .message-content {
        background-color: var(--white);
        border: 1px solid var(--gray-medium);
    }

    .message-timestamp {
        font-size: 0.8rem;
        color: var(--text-light);
        margin-top: 0.25rem;
    }

    .chat-input-container {
        background-color: var(--white);
        border-top: 1px solid var(--gray-medium);
        padding: 1rem;
        position: relative;
    }

    .loading-indicator {
        display: none;
        text-align: center;
        padding: 1rem;
        color: var(--text-light);
    }

    .loading-indicator.active {
        display: block;
    }

    .suggestions {
        margin-top: 1rem;
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .suggestion-chip {
        background-color: var(--light);
        border: 1px solid var(--gray-medium);
        border-radius: 1rem;
        padding: 0.25rem 0.75rem;
        font-size: 0.875rem;
        cursor: pointer;
        transition: all 0.2s;
    }

    .suggestion-chip:hover {
        background-color: var(--gray-medium);
    }

    .ai-message .message-content {
        max-width: 100%;
    }

    .ai-message .message-content pre {
        background-color: var(--light);
        padding: 1rem;
        border-radius: var(--border-radius);
        overflow-x: auto;
        margin: 0.5rem 0;
    }

    .ai-message .message-content code {
        background-color: var(--light);
        padding: 0.2rem 0.4rem;
        border-radius: 0.25rem;
    }

    .error-message {
        color: var(--danger);
        background-color: var(--danger-light);
        padding: 0.75rem 1rem;
        border-radius: var(--border-radius);
        margin-bottom: 1rem;
        display: none;
    }

    .error-message.active {
        display: block;
    }
</style>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/default.min.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/marked/lib/marked.min.css">
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <h1 class="page-header">AI Agent</h1>
            <div class="error-message"></div>
            <div class="chat-container" id="chatContainer"></div>
            <div class="loading-indicator">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p>Processing your request...</p>
            </div>
            <div class="chat-input-container">
                <form id="chatForm" class="d-flex gap-2">
                    <input type="text" class="form-control" id="messageInput" placeholder="Type your message..." required>
                    <button type="submit" class="btn btn-primary">Send</button>
                </form>
                <div class="suggestions">
                    <div class="suggestion-chip">Show latest analysis</div>
                    <div class="suggestion-chip">Compare with previous month</div>
                    <div class="suggestion-chip">Show client metrics</div>
                    <div class="suggestion-chip">Identify trends</div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/highlight.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
<script>
const chatContainer = document.getElementById('chatContainer');
const chatForm = document.getElementById('chatForm');
const messageInput = document.getElementById('messageInput');
const loadingIndicator = document.querySelector('.loading-indicator');
const errorMessage = document.querySelector('.error-message');
const suggestionChips = document.querySelectorAll('.suggestion-chip');

let currentConversationId = null;

// Configure marked options
marked.setOptions({
    highlight: function(code, lang) {
        if (lang && hljs.getLanguage(lang)) {
            return hljs.highlight(code, { language: lang }).value;
        }
        return hljs.highlightAuto(code).value;
    },
    breaks: true
});

function showError(message) {
    errorMessage.textContent = message;
    errorMessage.classList.add('active');
    setTimeout(() => {
        errorMessage.classList.remove('active');
    }, 5000);
}

function appendMessage(content, isUser = false) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${isUser ? 'user-message' : 'ai-message'}`;

    const messageContent = document.createElement('div');
    messageContent.className = 'message-content';

    if (isUser) {
        messageContent.textContent = content;
    } else {
        // Parse markdown and render HTML for AI responses
        messageContent.innerHTML = marked.parse(content);
        // Initialize syntax highlighting for code blocks
        messageContent.querySelectorAll('pre code').forEach((block) => {
            hljs.highlightBlock(block);
        });
    }

    const timestamp = document.createElement('div');
    timestamp.className = 'message-timestamp';
    timestamp.textContent = new Date().toLocaleTimeString();

    messageDiv.appendChild(messageContent);
    messageDiv.appendChild(timestamp);
    chatContainer.appendChild(messageDiv);
    chatContainer.scrollTop = chatContainer.scrollHeight;
}

async function sendMessage(message) {
    loadingIndicator.classList.add('active');
    errorMessage.classList.remove('active');

    try {
        const response = await fetch('/ai-agent/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            },
            body: JSON.stringify({
                message: message,
                conversation_id: currentConversationId
            })
        });

        const data = await response.json();

        if (!response.ok) {
            throw new Error(data.error || 'Failed to get response');
        }

        currentConversationId = data.conversation_id;
        appendMessage(data.response, false);
    } catch (error) {
        showError(error.message);
    } finally {
        loadingIndicator.classList.remove('active');
    }
}

chatForm.addEventListener('submit', async (e) => {
    e.preventDefault();
    const message = messageInput.value.trim();
    if (!message) return;

    appendMessage(message, true);
    messageInput.value = '';
    await sendMessage(message);
});

suggestionChips.forEach(chip => {
    chip.addEventListener('click', async () => {
        const message = chip.textContent;
        appendMessage(message, true);
        await sendMessage(message);
    });
});

// Initial greeting
appendMessage("Hello! I'm your AI assistant. I can help you analyze Jira tickets, identify trends, and provide insights. What would you like to know?", false);
</script>
{% endblock %}