#!/usr/bin/env python
"""
Test AI Agent with Open Source LLM integration
"""
import os
import sys
import django
from django.conf import settings

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'jira_analyzer.settings')
django.setup()

from django.contrib.auth.models import User
from analyzer.opensource_llm_integration import OpenSourceLLMHandler
import asyncio

async def test_ai_agent_functionality():
    """Test the AI Agent with open source LLM"""
    print("🤖 AI Agent Open Source LLM Test")
    print("=" * 50)
    
    try:
        # Test LLM Handler directly
        print("1. Testing LLM Handler...")
        handler = OpenSourceLLMHandler()
        
        # Test different types of messages
        test_messages = [
            "Hello! I'm new to Jira analysis.",
            "Can you help me analyze my data?",
            "What trends should I look for?",
            "How can I improve client satisfaction?",
            "What about priority management?",
            "Give me some recommendations."
        ]
        
        print("\n📤 Testing various message types:")
        for i, message in enumerate(test_messages, 1):
            print(f"\n{i}. Message: '{message}'")
            
            response = await handler.generate_response(message)
            model_info = handler.get_model_info()
            
            print(f"   Response: {response[:100]}...")
            print(f"   Model: {model_info['name']} ({model_info['provider']})")
        
        # Test with context data
        print("\n2. Testing with context data...")
        context_data = {
            'latest_analysis': {
                'issue_count': 150,
                'ticket_types': {'Bug': 45, 'Feature': 30, 'Task': 75},
                'priority_distribution': {'High': 25, 'Medium': 100, 'Low': 25}
            },
            'client_metrics': {
                'Client A': {'tickets': 50},
                'Client B': {'tickets': 30}
            }
        }
        
        context_message = "Can you analyze my latest data?"
        print(f"\nMessage with context: '{context_message}'")
        
        response = await handler.generate_response(context_message, context_data=context_data)
        print(f"Response: {response}")
        
        print("\n✅ AI Agent is working with open source LLM!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        return False

def main():
    """Run the test"""
    print("🧪 AI Agent Open Source Integration Test")
    print("=" * 60)
    
    success = asyncio.run(test_ai_agent_functionality())
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 SUCCESS! AI Agent is working with open source LLM!")
        print("\n📋 What's working:")
        print("   ✅ Open source LLM integration")
        print("   ✅ Intelligent fallback responses")
        print("   ✅ Context-aware responses")
        print("   ✅ Multiple message type handling")
        print("   ✅ No API costs or quota limits")
        print("   ✅ Privacy-friendly local processing")
        
        print("\n🌐 Test the web interface:")
        print("   1. Go to: http://127.0.0.1:8000/ai-agent/")
        print("   2. Try sending messages like:")
        print("      • 'Hello!'")
        print("      • 'Help me analyze my Jira data'")
        print("      • 'What trends should I look for?'")
        print("      • 'Give me recommendations'")
        
        print("\n💡 Optional improvements:")
        print("   • Get a free Hugging Face token for better LLM access")
        print("   • Add more sophisticated fallback responses")
        print("   • Integrate with local LLM models (Ollama, etc.)")
        
    else:
        print("❌ Test failed. Check the error messages above.")

if __name__ == "__main__":
    main()
