#!/usr/bin/env python
"""
Test script for AI Agent functionality
"""
import os
import sys
import django
from django.conf import settings

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'jira_analyzer.settings')
django.setup()

from django.contrib.auth.models import User
from analyzer.chat_handler import ChatHandler
from analyzer.gpt_integration import GP<PERSON><PERSON>ler
import asyncio

def test_gpt_handler():
    """Test GPT Handler initialization"""
    print("Testing GPT Handler initialization...")
    try:
        handler = GPTHandler()
        print("✅ GPT Handler initialized successfully")
        print(f"API Key configured: {'Yes' if handler.api_key else 'No'}")
        return True
    except Exception as e:
        print(f"❌ GPT Handler initialization failed: {str(e)}")
        return False

def test_chat_handler():
    """Test Chat Handler functionality"""
    print("\nTesting Chat Handler...")
    try:
        # Get or create a test user
        user, created = User.objects.get_or_create(
            username='test_user',
            defaults={'email': '<EMAIL>'}
        )
        
        handler = ChatHandler(user)
        print("✅ Chat Handler initialized successfully")
        return True, user
    except Exception as e:
        print(f"❌ Chat Handler initialization failed: {str(e)}")
        return False, None

async def test_message_processing(user):
    """Test message processing"""
    print("\nTesting message processing...")
    try:
        handler = ChatHandler(user)
        
        # Test with a simple message
        test_message = "Hello, can you help me analyze my Jira data?"
        result = await handler.process_message(test_message)
        
        print(f"✅ Message processed successfully")
        print(f"Response: {result['response'][:100]}...")
        print(f"Conversation ID: {result['conversation_id']}")
        print(f"Status: {result['status']}")
        return True
    except Exception as e:
        print(f"❌ Message processing failed: {str(e)}")
        return False

def main():
    """Run all tests"""
    print("🧪 AI Agent Diagnostic Tests")
    print("=" * 50)
    
    # Test 1: GPT Handler
    gpt_ok = test_gpt_handler()
    
    # Test 2: Chat Handler
    chat_ok, user = test_chat_handler()
    
    # Test 3: Message Processing (only if previous tests pass)
    if gpt_ok and chat_ok and user:
        try:
            message_ok = asyncio.run(test_message_processing(user))
        except Exception as e:
            print(f"❌ Async message processing failed: {str(e)}")
            message_ok = False
    else:
        message_ok = False
        print("\n⏭️  Skipping message processing test due to previous failures")
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Summary:")
    print(f"GPT Handler: {'✅ PASS' if gpt_ok else '❌ FAIL'}")
    print(f"Chat Handler: {'✅ PASS' if chat_ok else '❌ FAIL'}")
    print(f"Message Processing: {'✅ PASS' if message_ok else '❌ FAIL'}")
    
    if all([gpt_ok, chat_ok, message_ok]):
        print("\n🎉 All tests passed! AI Agent should be working correctly.")
    else:
        print("\n⚠️  Some tests failed. Please check the configuration.")
        
        if not gpt_ok:
            print("\n💡 To fix GPT Handler issues:")
            print("   1. Make sure you have a valid OpenAI API key")
            print("   2. Add it to your .env file: OPENAI_API_KEY=your_key_here")
            print("   3. Restart the Django server")

if __name__ == "__main__":
    main()
