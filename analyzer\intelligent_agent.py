import os
import requests
import logging
import re
from datetime import datetime
from dotenv import load_dotenv
from .deepseek_llm_handler import DeepSeekLL<PERSON>Handler

# Set up logging
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

class IntelligentAgent:
    """
    Enhanced AI Agent with LLM brain, memory, and web access capabilities
    """
    
    def __init__(self, user):
        self.user = user
        self.hf_token = os.getenv('HUGGINGFACE_TOKEN', None)

        # Memory system
        self.conversation_memory = {}
        self.database_memory = {}
        self.web_search_cache = {}

        # Enhanced LLM configuration with DeepSeek-R1
        self.deepseek_handler = None
        self.enable_deepseek = os.getenv('ENABLE_DEEPSEEK_MODEL', 'True').lower() == 'true'

        # Initialize DeepSeek-R1 model
        if self.enable_deepseek:
            try:
                self.deepseek_handler = DeepSeekLLMHandler()
                logger.info("✅ DeepSeek-R1 handler initialized")
            except Exception as e:
                logger.warning(f"Failed to initialize DeepSeek-R1: {str(e)}")
                self.deepseek_handler = None

        # Fallback LLM models (Hugging Face API)
        self.llm_models = [
            {
                "name": "microsoft/DialoGPT-large",
                "api_url": "https://api-inference.huggingface.co/models/microsoft/DialoGPT-large",
                "type": "conversational"
            },
            {
                "name": "google/flan-t5-large",
                "api_url": "https://api-inference.huggingface.co/models/google/flan-t5-large",
                "type": "text-generation"
            }
        ]

        self.current_model = None
        
        # System prompt for intelligent responses
        self.system_prompt = """You are an advanced AI assistant with access to Jira analysis data and web search capabilities. 
        You can:
        1. Analyze Jira ticket data and provide specific insights
        2. Search the web for additional information when needed
        3. Remember previous conversations and context
        4. Provide intelligent, data-driven recommendations
        5. Answer general questions using web search when appropriate
        
        Always be helpful, accurate, and provide specific insights when possible.
        When you don't have information in the database, use web search to find relevant answers.
        """

    async def process_intelligent_query(self, user_message, conversation_id=None, context_data=None):
        """
        Process user queries with enhanced intelligence, memory, and web access
        """
        try:
            # Update memory with current context
            await self._update_memory(conversation_id, context_data)
            
            # Analyze query intent
            query_intent = self._analyze_query_intent(user_message)
            
            # Generate response based on intent
            if query_intent['type'] == 'jira_analysis':
                response = await self._handle_jira_query(user_message, context_data, query_intent)
            elif query_intent['type'] == 'web_search':
                response = await self._handle_web_search_query(user_message, query_intent)
            elif query_intent['type'] == 'general_conversation':
                response = await self._handle_general_query(user_message, conversation_id)
            else:
                response = await self._handle_hybrid_query(user_message, context_data, query_intent)
            
            # Store response in memory
            await self._store_in_memory(conversation_id, user_message, response)
            
            return response
            
        except Exception as e:
            logger.error(f"Error in intelligent query processing: {str(e)}")
            return "I encountered an error while processing your request. Please try again or rephrase your question."

    def _analyze_query_intent(self, message):
        """
        Analyze user message to determine intent and required capabilities
        """
        message_lower = message.lower()
        
        # Jira analysis keywords
        jira_keywords = ['jira', 'ticket', 'issue', 'client', 'priority', 'sentiment', 'analysis', 'data', 'trend']
        
        # Web search keywords
        web_keywords = ['what is', 'how to', 'explain', 'define', 'search', 'find', 'latest news', 'current']
        
        # General conversation keywords
        general_keywords = ['hello', 'hi', 'help', 'thank', 'bye']
        
        jira_score = sum(1 for keyword in jira_keywords if keyword in message_lower)
        web_score = sum(1 for keyword in web_keywords if keyword in message_lower)
        general_score = sum(1 for keyword in general_keywords if keyword in message_lower)
        
        # Determine primary intent
        if jira_score > web_score and jira_score > general_score:
            intent_type = 'jira_analysis'
        elif web_score > jira_score and web_score > general_score:
            intent_type = 'web_search'
        elif general_score > 0:
            intent_type = 'general_conversation'
        else:
            intent_type = 'hybrid'
        
        return {
            'type': intent_type,
            'jira_score': jira_score,
            'web_score': web_score,
            'general_score': general_score,
            'keywords': {
                'jira': [kw for kw in jira_keywords if kw in message_lower],
                'web': [kw for kw in web_keywords if kw in message_lower],
                'general': [kw for kw in general_keywords if kw in message_lower]
            }
        }

    async def _handle_jira_query(self, message, context_data, intent):
        """
        Handle Jira-specific queries using database context and DeepSeek-R1
        """
        if not context_data or not context_data.get('latest_analysis'):
            return "I don't have any Jira analysis data available. Please upload and analyze some Jira files first."

        # Try Ollama DeepSeek model first
        if self.deepseek_handler and self.deepseek_handler.is_model_ready():
            try:
                logger.info("Using Ollama DeepSeek for Jira query")
                response = await self.deepseek_handler.generate_response(
                    message,
                    context_data=context_data
                )
                if response and len(response.strip()) > 50:
                    return f"🧠 **Ollama DeepSeek Analysis:**\n\n{response}"
            except Exception as e:
                logger.warning(f"Ollama DeepSeek failed: {str(e)}")

        # Fallback to open source LLM integration
        try:
            response = await self._try_llm_response(message, context_data)
            if response and len(response.strip()) > 50:
                return response
        except Exception as e:
            logger.warning(f"LLM integration failed: {str(e)}")

        # Final fallback to intelligent responses
        from .opensource_llm_integration import OpenSourceLLMHandler
        llm_handler = OpenSourceLLMHandler()
        return llm_handler._get_fallback_response(message, context_data)

    async def _handle_web_search_query(self, message, intent):
        """
        Handle queries that require web search
        """
        try:
            # Extract search query from message
            search_query = self._extract_search_query(message)
            
            # Check cache first
            if search_query in self.web_search_cache:
                cached_result = self.web_search_cache[search_query]
                if (datetime.now() - cached_result['timestamp']).seconds < 3600:  # 1 hour cache
                    return self._format_web_search_response(cached_result['data'], search_query)
            
            # Perform web search
            search_results = await self._perform_web_search(search_query)
            
            if search_results:
                # Cache results
                self.web_search_cache[search_query] = {
                    'data': search_results,
                    'timestamp': datetime.now()
                }
                
                return self._format_web_search_response(search_results, search_query)
            else:
                return f"I couldn't find reliable information about '{search_query}' at the moment. Could you try rephrasing your question or being more specific?"
                
        except Exception as e:
            logger.error(f"Web search error: {str(e)}")
            return "I'm having trouble accessing web search right now. Please try again later or ask about your Jira data instead."

    async def _handle_general_query(self, message, conversation_id):
        """
        Handle general conversation queries
        """
        message_lower = message.lower()
        
        # Get conversation history for context
        history = await self._get_conversation_history(conversation_id)
        
        if any(word in message_lower for word in ['hello', 'hi', 'hey']):
            return "Hello! I'm your intelligent Jira analysis assistant with web search capabilities. I can help you analyze your Jira data, search for information online, and answer various questions. What would you like to know?"
        
        elif any(word in message_lower for word in ['help', 'what can you do']):
            return """🤖 **I'm your intelligent AI assistant with these capabilities:**

🔍 **Jira Analysis**: 
• Analyze your uploaded Jira data
• Provide client insights and trends
• Generate recommendations

🌐 **Web Search**: 
• Search for current information
• Explain concepts and definitions
• Find latest news and updates

🧠 **Memory & Context**: 
• Remember our conversation
• Maintain context across queries
• Learn from your data patterns

💡 **Ask me anything like:**
• "Analyze my latest Jira data"
• "What is agile methodology?"
• "How to improve client satisfaction?"
• "Search for latest Jira best practices"
"""
        
        elif any(word in message_lower for word in ['thank', 'thanks']):
            return "You're welcome! I'm here to help with your Jira analysis and any other questions you might have. Feel free to ask me anything!"
        
        else:
            # Try to provide a helpful response or suggest web search
            return f"I understand you're asking about something general. Would you like me to search the web for information about '{message}' or would you prefer to ask about your Jira data?"

    async def _handle_hybrid_query(self, message, context_data, intent):
        """
        Handle queries that might need both Jira data and web search
        """
        response_parts = []
        
        # First, check if we have relevant Jira data
        if context_data and context_data.get('latest_analysis'):
            jira_response = await self._handle_jira_query(message, context_data, intent)
            if jira_response and "don't have any Jira" not in jira_response:
                response_parts.append("📊 **From Your Jira Data:**\n" + jira_response)
        
        # Then, try web search for additional context
        try:
            web_response = await self._handle_web_search_query(message, intent)
            if web_response and "couldn't find" not in web_response and "having trouble" not in web_response:
                response_parts.append("🌐 **Additional Information:**\n" + web_response)
        except:
            pass
        
        if response_parts:
            return "\n\n".join(response_parts)
        else:
            return "I can help you with both Jira analysis and web search. Could you be more specific about what you're looking for?"

    def _extract_search_query(self, message):
        """
        Extract search query from user message
        """
        # Remove common question words and extract the core query
        message = re.sub(r'^(what is|how to|explain|define|search for|find|tell me about)\s+', '', message.lower())
        message = re.sub(r'\?$', '', message)
        return message.strip()

    async def _perform_web_search(self, query):
        """
        Perform web search using multiple search APIs
        """
        # Try Google Custom Search API first (if available)
        google_result = await self._try_google_search(query)
        if google_result:
            return google_result

        # Try DuckDuckGo Instant Answer API
        try:
            url = f"https://api.duckduckgo.com/"
            params = {
                'q': query,
                'format': 'json',
                'no_html': '1',
                'skip_disambig': '1'
            }

            response = requests.get(url, params=params, timeout=10)

            if response.status_code == 200:
                data = response.json()

                # Extract relevant information
                result = {
                    'abstract': data.get('Abstract', ''),
                    'definition': data.get('Definition', ''),
                    'answer': data.get('Answer', ''),
                    'related_topics': data.get('RelatedTopics', [])[:3],
                    'source': data.get('AbstractSource', 'DuckDuckGo')
                }

                if any(result.values()):  # If we got any useful data
                    return result

        except Exception as e:
            logger.error(f"DuckDuckGo search failed: {str(e)}")

        # Fallback: Use built-in knowledge base
        try:
            return await self._get_definition_fallback(query)
        except Exception as e:
            logger.error(f"Fallback search failed: {str(e)}")
            return None

    async def _try_google_search(self, query):
        """
        Try Google Custom Search API with improved error handling and configuration
        """
        try:
            google_api_key = os.getenv('GOOGLE_API_KEY', 'AIzaSyDLS7KjnBml_w1PJEMvtnrLyNwhQXJQpc0')

            # Use a working Custom Search Engine ID or create a general web search
            google_cse_id = os.getenv('GOOGLE_CSE_ID', '017576662512468239146:omuauf_lfve')

            if not google_api_key:
                logger.warning("Google API key not available")
                return None

            logger.info(f"Attempting Google search for: {query}")

            url = "https://www.googleapis.com/customsearch/v1"
            params = {
                'key': google_api_key,
                'cx': google_cse_id,
                'q': query,
                'num': 5,  # Get more results
                'safe': 'active'
            }

            response = requests.get(url, params=params, timeout=15)

            logger.info(f"Google API response status: {response.status_code}")

            if response.status_code == 200:
                data = response.json()
                items = data.get('items', [])

                logger.info(f"Google search returned {len(items)} results")

                if items:
                    # Format the first result
                    first_result = items[0]
                    result = {
                        'abstract': first_result.get('snippet', ''),
                        'title': first_result.get('title', ''),
                        'link': first_result.get('link', ''),
                        'source': 'Google Search',
                        'query': query,
                        'total_results': len(items),
                        'additional_results': [
                            {
                                'title': item.get('title', ''),
                                'snippet': item.get('snippet', ''),
                                'link': item.get('link', '')
                            } for item in items[1:4]  # Get 3 additional results
                        ]
                    }

                    logger.info(f"Successfully formatted Google search result for: {query}")
                    return result
                else:
                    logger.warning(f"No search results found for: {query}")
            else:
                error_data = response.json() if response.content else {}
                logger.error(f"Google API error {response.status_code}: {error_data}")

                # Check for specific API errors
                if response.status_code == 403:
                    logger.error("Google API quota exceeded or invalid API key")
                elif response.status_code == 400:
                    logger.error("Invalid search parameters")

        except requests.exceptions.Timeout:
            logger.error("Google search request timed out")
        except requests.exceptions.RequestException as e:
            logger.error(f"Google search request failed: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected Google search error: {str(e)}")

        return None

    async def _get_definition_fallback(self, query):
        """
        Fallback method to provide definitions and explanations
        """
        # Enhanced knowledge base for common terms
        knowledge_base = {
            'agile': {
                'definition': 'Agile is a project management methodology that emphasizes iterative development, collaboration, and flexibility.',
                'details': 'Agile methodologies include Scrum, Kanban, and XP, focusing on delivering working software in short iterations with continuous feedback and adaptation.'
            },
            'scrum': {
                'definition': 'Scrum is an agile framework for managing product development with roles, events, and artifacts.',
                'details': 'Key roles include Product Owner, Scrum Master, and Development Team. Events include Sprint Planning, Daily Standups, Sprint Review, and Retrospective.'
            },
            'kanban': {
                'definition': 'Kanban is a visual workflow management method that helps teams visualize work and optimize flow.',
                'details': 'Uses boards with columns representing workflow stages and cards representing work items. Focuses on limiting work in progress (WIP).'
            },
            'jira': {
                'definition': 'Jira is a project management and issue tracking software developed by Atlassian.',
                'details': 'Widely used for bug tracking, agile project management, and software development workflows. Supports Scrum and Kanban methodologies.'
            },
            'devops': {
                'definition': 'DevOps is a set of practices that combines software development and IT operations.',
                'details': 'Aims to shorten the development lifecycle and provide continuous delivery with high software quality through automation and monitoring.'
            },
            'ci/cd': {
                'definition': 'CI/CD stands for Continuous Integration and Continuous Deployment/Delivery.',
                'details': 'CI involves automatically testing code changes, while CD automates the deployment process to production environments.'
            },
            'sprint': {
                'definition': 'A Sprint is a time-boxed iteration in Scrum, typically lasting 1-4 weeks.',
                'details': 'During a sprint, the team works to complete a set of planned work items and deliver a potentially shippable product increment.'
            },
            'backlog': {
                'definition': 'A backlog is a prioritized list of features, user stories, or tasks to be completed.',
                'details': 'In Scrum, the Product Backlog contains all desired work on the project, prioritized by the Product Owner.'
            },
            'user story': {
                'definition': 'A user story is a short description of a feature from the perspective of the end user.',
                'details': 'Typically follows the format: "As a [user type], I want [functionality] so that [benefit]".'
            },
            'epic': {
                'definition': 'An epic is a large user story that can be broken down into smaller stories.',
                'details': 'Epics are used to organize work and track progress on larger features or initiatives.'
            }
        }

        query_lower = query.lower()
        for term, info in knowledge_base.items():
            if term in query_lower:
                return {
                    'definition': info['definition'],
                    'abstract': info['details'],
                    'source': 'Built-in Knowledge Base'
                }

        return None

    def _format_web_search_response(self, search_data, query):
        """
        Format web search results into a readable response
        """
        if not search_data:
            return f"I couldn't find specific information about '{query}'."

        response = f"🔍 **Search Results for '{query}':**\n\n"

        # Handle Google search results
        if search_data.get('source') == 'Google Search':
            if search_data.get('title'):
                response += f"📰 **{search_data['title']}**\n"

            if search_data.get('abstract'):
                response += f"📝 {search_data['abstract']}\n\n"

            if search_data.get('link'):
                response += f"🔗 **Link**: {search_data['link']}\n\n"

            # Add additional results
            if search_data.get('additional_results'):
                response += "📚 **Related Results**:\n"
                for result in search_data['additional_results']:
                    if result.get('title') and result.get('snippet'):
                        response += f"• **{result['title']}**: {result['snippet'][:100]}...\n"
                response += "\n"

        # Handle DuckDuckGo results
        else:
            # Add definition if available
            if search_data.get('definition'):
                response += f"📖 **Definition**: {search_data['definition']}\n\n"

            # Add abstract/summary
            if search_data.get('abstract'):
                response += f"📝 **Summary**: {search_data['abstract']}\n\n"

            # Add direct answer if available
            if search_data.get('answer'):
                response += f"💡 **Answer**: {search_data['answer']}\n\n"

        # Add source
        if search_data.get('source'):
            response += f"📚 **Source**: {search_data['source']}\n"

        return response.strip()

    async def _try_llm_response(self, message, context_data):
        """
        Try to get response from LLM models
        """
        for model_info in self.llm_models:
            try:
                response = await self._make_llm_request(model_info, message, context_data)
                if response and len(response.strip()) > 20:
                    self.current_model = model_info
                    return response
            except Exception as e:
                logger.warning(f"LLM model {model_info['name']} failed: {str(e)}")
                continue

        return None

    async def _make_llm_request(self, model_info, message, context_data):
        """
        Make request to LLM model
        """
        headers = {"Content-Type": "application/json"}
        if self.hf_token:
            headers["Authorization"] = f"Bearer {self.hf_token}"

        # Prepare context-aware prompt
        prompt = self._prepare_intelligent_prompt(message, context_data)

        payload = {
            "inputs": prompt,
            "parameters": {
                "max_new_tokens": 300,
                "temperature": 0.7,
                "do_sample": True,
                "return_full_text": False
            }
        }

        response = requests.post(model_info["api_url"], headers=headers, json=payload, timeout=30)

        if response.status_code == 200:
            result = response.json()
            if isinstance(result, list) and len(result) > 0:
                return result[0].get("generated_text", "").strip()

        return None

    def _prepare_intelligent_prompt(self, message, context_data):
        """
        Prepare an intelligent prompt with context
        """
        prompt = f"{self.system_prompt}\n\n"

        # Add database context if available
        if context_data and context_data.get('latest_analysis'):
            analysis = context_data['latest_analysis']
            prompt += f"Current Jira Analysis Context:\n"
            prompt += f"- File: {analysis.get('file_name', 'Unknown')}\n"
            prompt += f"- Total Issues: {analysis.get('issue_count', 0)}\n"
            prompt += f"- Top Priority: {context_data.get('data_summary', {}).get('top_priority', 'N/A')}\n"
            prompt += f"- Clients: {context_data.get('data_summary', {}).get('total_clients', 0)}\n\n"

        prompt += f"User Question: {message}\n"
        prompt += f"Assistant Response:"

        return prompt

    async def _update_memory(self, conversation_id, context_data):
        """
        Update memory with current context and database state
        """
        if conversation_id:
            self.conversation_memory[conversation_id] = {
                'last_updated': datetime.now(),
                'context_data': context_data,
                'query_count': self.conversation_memory.get(conversation_id, {}).get('query_count', 0) + 1
            }

        # Update database memory
        if context_data and context_data.get('latest_analysis'):
            analysis = context_data['latest_analysis']
            self.database_memory['latest_analysis'] = {
                'file_name': analysis.get('file_name'),
                'issue_count': analysis.get('issue_count'),
                'analysis_date': analysis.get('analysis_date'),
                'last_accessed': datetime.now()
            }

    async def _store_in_memory(self, conversation_id, user_message, response):
        """
        Store conversation in memory for context
        """
        if conversation_id and conversation_id in self.conversation_memory:
            if 'messages' not in self.conversation_memory[conversation_id]:
                self.conversation_memory[conversation_id]['messages'] = []

            self.conversation_memory[conversation_id]['messages'].append({
                'user': user_message,
                'assistant': response,
                'timestamp': datetime.now()
            })

            # Keep only last 10 messages to manage memory
            if len(self.conversation_memory[conversation_id]['messages']) > 10:
                self.conversation_memory[conversation_id]['messages'] = \
                    self.conversation_memory[conversation_id]['messages'][-10:]

    async def _get_conversation_history(self, conversation_id):
        """
        Get conversation history from memory
        """
        if conversation_id and conversation_id in self.conversation_memory:
            return self.conversation_memory[conversation_id].get('messages', [])
        return []

    def get_memory_status(self):
        """
        Get current memory status for debugging
        """
        deepseek_status = "Not Available"
        if self.deepseek_handler:
            if self.deepseek_handler.is_model_ready():
                model_info = self.deepseek_handler.get_model_info()
                deepseek_status = f"Ready ({model_info.get('method', 'unknown')})"
            else:
                deepseek_status = "Failed to Load"

        return {
            'active_conversations': len(self.conversation_memory),
            'database_memory': bool(self.database_memory.get('latest_analysis')),
            'web_cache_size': len(self.web_search_cache),
            'deepseek_model': deepseek_status,
            'current_model': self.current_model['name'] if self.current_model else 'Fallback',
            'google_search': bool(os.getenv('GOOGLE_API_KEY'))
        }
