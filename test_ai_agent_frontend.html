<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Agent Frontend Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .pass { background-color: #d4edda; color: #155724; }
        .fail { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>AI Agent Frontend Test</h1>
    <div id="test-results"></div>

    <!-- Simulate the AI Agent HTML structure -->
    <div style="display: none;">
        <div id="chatContainer"></div>
        <form id="chatForm">
            <input type="hidden" name="csrfmiddlewaretoken" value="test-token">
            <input type="text" id="messageInput" value="test message">
            <button type="submit">Send</button>
        </form>
        <div class="loading-indicator"></div>
        <div class="error-message"></div>
        <div class="suggestion-chip">Test suggestion</div>
    </div>

    <script>
        function runTests() {
            const results = document.getElementById('test-results');
            let testCount = 0;
            let passCount = 0;

            function addResult(testName, passed, message) {
                testCount++;
                if (passed) passCount++;
                
                const div = document.createElement('div');
                div.className = `test-result ${passed ? 'pass' : 'fail'}`;
                div.innerHTML = `<strong>${testName}:</strong> ${passed ? 'PASS' : 'FAIL'} - ${message}`;
                results.appendChild(div);
            }

            // Test 1: DOM Elements Exist
            const chatContainer = document.getElementById('chatContainer');
            const chatForm = document.getElementById('chatForm');
            const messageInput = document.getElementById('messageInput');
            const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]');
            
            addResult('DOM Elements', 
                chatContainer && chatForm && messageInput && csrfToken, 
                'All essential elements found');

            // Test 2: CSRF Token Access
            let csrfValue = null;
            try {
                csrfValue = csrfToken ? csrfToken.value : null;
                addResult('CSRF Token Access', 
                    csrfValue !== null, 
                    `Token value: ${csrfValue}`);
            } catch (e) {
                addResult('CSRF Token Access', false, `Error: ${e.message}`);
            }

            // Test 3: Message Input Value Access
            let inputValue = null;
            try {
                inputValue = messageInput ? messageInput.value : null;
                addResult('Message Input Access', 
                    inputValue !== null, 
                    `Input value: "${inputValue}"`);
            } catch (e) {
                addResult('Message Input Access', false, `Error: ${e.message}`);
            }

            // Test 4: Null Safety Check
            const nullElement = document.getElementById('nonexistent');
            let nullSafe = true;
            try {
                if (nullElement && nullElement.value) {
                    // This should not execute
                    nullSafe = false;
                }
                addResult('Null Safety', nullSafe, 'Null checks working correctly');
            } catch (e) {
                addResult('Null Safety', false, `Error: ${e.message}`);
            }

            // Summary
            const summary = document.createElement('div');
            summary.className = 'test-result info';
            summary.innerHTML = `<strong>Summary:</strong> ${passCount}/${testCount} tests passed`;
            results.appendChild(summary);

            if (passCount === testCount) {
                const success = document.createElement('div');
                success.className = 'test-result pass';
                success.innerHTML = '<strong>🎉 All tests passed! The AI Agent frontend should work correctly.</strong>';
                results.appendChild(success);
            }
        }

        // Run tests when page loads
        document.addEventListener('DOMContentLoaded', runTests);
    </script>
</body>
</html>
