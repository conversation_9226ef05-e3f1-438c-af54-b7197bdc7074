import os
import requests
import logging
import json
from datetime import datetime, timedelta
from django.conf import settings
from dotenv import load_dotenv
from .chat_models import ChatMessage

# Set up logging
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

class OpenSourceLLMHandler:
    def __init__(self):
        """Initialize the Open Source LLM Handler with multiple fallback options"""
        
        # Try to get Hugging Face token (optional, but increases rate limits)
        self.hf_token = os.getenv('HUGGINGFACE_TOKEN', None)
        
        # Define available free models (in order of preference)
        self.available_models = [
            {
                "name": "microsoft/DialoGPT-large",
                "api_url": "https://api-inference.huggingface.co/models/microsoft/DialoGPT-large",
                "type": "conversational"
            },
            {
                "name": "microsoft/DialoGPT-medium", 
                "api_url": "https://api-inference.huggingface.co/models/microsoft/DialoGPT-medium",
                "type": "conversational"
            },
            {
                "name": "facebook/blenderbot-400M-distill",
                "api_url": "https://api-inference.huggingface.co/models/facebook/blenderbot-400M-distill",
                "type": "conversational"
            },
            {
                "name": "google/flan-t5-large",
                "api_url": "https://api-inference.huggingface.co/models/google/flan-t5-large", 
                "type": "text-generation"
            }
        ]
        
        # Set current model (will be determined dynamically)
        self.current_model = None
        
        # Define system prompt for Jira ticket analysis context
        self.system_prompt = """You are an AI assistant specialized in analyzing Jira ticket data and providing insights. 
        You help users understand:
        - Ticket metrics and distributions
        - Client-specific data and trends  
        - Sentiment analysis results
        - Historical patterns and trends
        - Best practices for project management
        
        Provide clear, actionable insights backed by data when possible.
        Keep responses concise but helpful."""

    def _make_api_request(self, model_info, prompt, max_retries=2):
        """Make API request to Hugging Face Inference API"""
        headers = {
            "Content-Type": "application/json"
        }
        
        # Add authorization header if token is available
        if self.hf_token:
            headers["Authorization"] = f"Bearer {self.hf_token}"
        
        # Prepare payload based on model type
        if model_info["type"] == "conversational":
            payload = {
                "inputs": {
                    "past_user_inputs": [],
                    "generated_responses": [],
                    "text": prompt
                },
                "parameters": {
                    "max_length": 500,
                    "temperature": 0.7,
                    "do_sample": True
                }
            }
        else:  # text-generation
            payload = {
                "inputs": prompt,
                "parameters": {
                    "max_new_tokens": 300,
                    "temperature": 0.7,
                    "do_sample": True,
                    "return_full_text": False
                }
            }
        
        for attempt in range(max_retries + 1):
            try:
                response = requests.post(
                    model_info["api_url"],
                    headers=headers,
                    json=payload,
                    timeout=30
                )
                
                if response.status_code == 200:
                    result = response.json()
                    
                    # Extract response based on model type
                    if model_info["type"] == "conversational":
                        if isinstance(result, dict) and "generated_text" in result:
                            return result["generated_text"]
                        elif isinstance(result, list) and len(result) > 0:
                            return result[0].get("generated_text", "")
                    else:  # text-generation
                        if isinstance(result, list) and len(result) > 0:
                            return result[0].get("generated_text", "")
                        elif isinstance(result, dict) and "generated_text" in result:
                            return result["generated_text"]
                    
                    return str(result)  # Fallback
                    
                elif response.status_code == 503:
                    # Model is loading, wait and retry
                    if attempt < max_retries:
                        logger.info(f"Model {model_info['name']} is loading, retrying in 5 seconds...")
                        import time
                        time.sleep(5)
                        continue
                    else:
                        return None
                        
                else:
                    logger.error(f"API request failed with status {response.status_code}: {response.text}")
                    return None
                    
            except requests.exceptions.RequestException as e:
                logger.error(f"Request exception for model {model_info['name']}: {str(e)}")
                if attempt < max_retries:
                    continue
                return None
        
        return None

    async def generate_response(self, user_message, conversation_id=None, context_data=None):
        """Generate a response using free open-source LLMs"""
        try:
            # Prepare the prompt with context
            prompt = self._prepare_prompt(user_message, context_data)
            
            # Try each model until we get a successful response
            for model_info in self.available_models:
                logger.info(f"Trying model: {model_info['name']}")
                
                response = self._make_api_request(model_info, prompt)
                
                if response and response.strip():
                    self.current_model = model_info
                    logger.info(f"Successfully got response from {model_info['name']}")
                    
                    # Clean up the response
                    cleaned_response = self._clean_response(response, user_message)
                    return cleaned_response
                    
                logger.warning(f"Model {model_info['name']} failed or returned empty response")
            
            # If all models fail, return a helpful fallback response
            return self._get_fallback_response(user_message, context_data)
            
        except Exception as e:
            logger.error(f"Unexpected error in LLM integration: {str(e)}")
            return self._get_fallback_response(user_message, context_data)

    def _prepare_prompt(self, user_message, context_data=None):
        """Prepare the prompt with system context and user message"""
        prompt = f"{self.system_prompt}\n\n"
        
        # Add context data if available
        if context_data:
            prompt += self._format_context_data(context_data) + "\n\n"
        
        prompt += f"User: {user_message}\nAssistant:"
        return prompt

    def _format_context_data(self, context_data):
        """Format context data into a structured message"""
        context_message = "Context Information:\n"
        
        # Add basic analysis data
        if context_data.get('latest_analysis'):
            analysis = context_data['latest_analysis']
            context_message += f"- Total Issues: {analysis.get('issue_count', 'N/A')}\n"
            context_message += f"- Ticket Types: {analysis.get('ticket_types', {})}\n"
            context_message += f"- Priority Distribution: {analysis.get('priority_distribution', {})}\n"
        
        # Add client metrics if available
        if context_data.get('client_metrics'):
            context_message += f"- Client Metrics Available: {len(context_data['client_metrics'])} clients\n"
        
        # Add sentiment data if available
        if context_data.get('sentiment'):
            sentiment = context_data['sentiment']
            context_message += f"- Overall Sentiment: {sentiment.get('overall', 'N/A')}\n"
            
        return context_message

    def _clean_response(self, response, user_message):
        """Clean and format the LLM response"""
        # Remove any repetition of the user message
        if user_message.lower() in response.lower():
            response = response.replace(user_message, "").strip()
        
        # Remove common prefixes
        prefixes_to_remove = ["Assistant:", "AI:", "Response:", "Answer:"]
        for prefix in prefixes_to_remove:
            if response.startswith(prefix):
                response = response[len(prefix):].strip()
        
        # Ensure response is not empty
        if not response.strip():
            return "I understand you're asking about Jira analysis. Could you please provide more specific details about what you'd like to know?"
        
        # Limit response length
        if len(response) > 1000:
            response = response[:1000] + "..."
        
        return response.strip()

    def _get_fallback_response(self, user_message, context_data=None):
        """Provide a helpful fallback response when LLMs are unavailable"""

        # Analyze the user message for keywords to provide relevant responses
        message_lower = user_message.lower()

        if any(word in message_lower for word in ['hello', 'hi', 'hey', 'greetings']):
            return "Hello! I'm your Jira analysis assistant powered by open-source AI. I can help you understand ticket metrics, client trends, and project insights. What would you like to analyze?"

        elif any(word in message_lower for word in ['analysis', 'analyze', 'data']):
            if context_data and context_data.get('latest_analysis'):
                analysis = context_data['latest_analysis']
                ticket_types = list(analysis.get('ticket_types', {}).keys())
                ticket_types_str = ', '.join(ticket_types[:3]) + ('...' if len(ticket_types) > 3 else '')
                return f"📊 Based on your latest analysis:\n• Total issues: {analysis.get('issue_count', 'N/A')}\n• Main ticket types: {ticket_types_str}\n• Priority distribution available\n\nWould you like me to explain any specific aspect or provide recommendations?"
            else:
                return "📈 I can help you analyze your Jira data! Please upload some Jira files first, then I can provide insights about:\n• Ticket distributions and patterns\n• Priority and status analysis\n• Client-specific metrics\n• Trend identification\n\nWhat type of analysis interests you most?"

        elif any(word in message_lower for word in ['client', 'customer']):
            if context_data and context_data.get('client_metrics'):
                client_count = len(context_data['client_metrics'])
                return f"👥 I can analyze metrics for {client_count} clients in your data, including:\n• Ticket volumes per client\n• Resolution times and patterns\n• Satisfaction trends\n• Priority distributions\n\nWhich client would you like to focus on, or would you prefer a comparative analysis?"
            else:
                return "👥 I can help you analyze client-specific metrics including:\n• Ticket volumes and types\n• Resolution times and efficiency\n• Satisfaction trends\n• Priority patterns\n\nDo you have a specific client you'd like to focus on?"

        elif any(word in message_lower for word in ['trend', 'pattern', 'historical']):
            return "📈 I can identify trends in your Jira data including:\n• Ticket volume changes over time\n• Priority distribution shifts\n• Resolution time patterns\n• Seasonal or cyclical patterns\n• Client satisfaction trends\n\nWhat time period are you interested in analyzing? (e.g., last month, quarter, year)"

        elif any(word in message_lower for word in ['help', 'what', 'how', 'can you']):
            return "🤖 I'm your open-source AI assistant for Jira analysis! I can help with:\n\n📊 **Data Analysis:**\n• Ticket metrics and distributions\n• Priority and status breakdowns\n• Resolution time analysis\n\n👥 **Client Insights:**\n• Client-specific performance\n• Satisfaction trends\n• Comparative analysis\n\n📈 **Trend Analysis:**\n• Historical patterns\n• Seasonal variations\n• Performance improvements\n\nWhat would you like to explore first?"

        elif any(word in message_lower for word in ['priority', 'urgent', 'critical']):
            if context_data and context_data.get('latest_analysis'):
                priorities = context_data['latest_analysis'].get('priority_distribution', {})
                if priorities:
                    priority_summary = ', '.join([f"{k}: {v}" for k, v in list(priorities.items())[:3]])
                    return f"🚨 Priority Analysis:\n{priority_summary}\n\nI can help you:\n• Identify high-priority trends\n• Analyze resolution times by priority\n• Suggest priority optimization strategies\n\nWhat specific priority insights do you need?"
            return "🚨 I can analyze priority distributions in your tickets:\n• Critical vs. high vs. medium priorities\n• Resolution time by priority level\n• Priority trends over time\n• Client-specific priority patterns\n\nWhat priority analysis would be most helpful?"

        elif any(word in message_lower for word in ['improve', 'recommendation', 'suggest']):
            return "💡 I can provide recommendations for:\n• Improving ticket resolution times\n• Optimizing priority management\n• Enhancing client satisfaction\n• Streamlining workflow processes\n• Identifying bottlenecks\n\nBased on your data, what area would you like to improve first?"

        else:
            return "🤖 I'm here to help with Jira ticket analysis using open-source AI! I can provide insights on:\n\n📊 Ticket metrics and patterns\n👥 Client-specific analysis\n📈 Historical trends\n🚨 Priority optimization\n💡 Improvement recommendations\n\nWhat specific aspect would you like to explore? Feel free to ask about any data you've uploaded or general Jira best practices!"

    def get_model_info(self):
        """Get information about the currently used model"""
        if self.current_model:
            return {
                "name": self.current_model["name"],
                "type": self.current_model["type"],
                "provider": "Hugging Face (Free)"
            }
        return {
            "name": "Fallback Response System",
            "type": "rule-based",
            "provider": "Local"
        }
