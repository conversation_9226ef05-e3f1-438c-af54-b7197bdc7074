from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import login_required
from django.views.decorators.csrf import ensure_csrf_cookie
import json

from .chat_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>

@login_required
@require_http_methods(["POST"])
@ensure_csrf_cookie
def chat_message(request):
    """Handle incoming chat messages and return AI responses."""
    try:
        data = json.loads(request.body)
        message = data.get('message')
        
        if not message:
            return JsonResponse({
                'error': 'Message content is required'
            }, status=400)

        # Initialize chat handler and process message
        handler = <PERSON><PERSON><PERSON><PERSON><PERSON>(request.user)
        result = handler.process_message(message)

        return JsonResponse({
            'response': result['response'],
            'conversation_id': str(result['conversation_id'])
        })

    except json.JSONDecodeError:
        return JsonResponse({
            'error': 'Invalid JSON data'
        }, status=400)
    except Exception as e:
        return JsonResponse({
            'error': str(e)
        }, status=500)