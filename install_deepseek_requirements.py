#!/usr/bin/env python
"""
Install required dependencies for DeepSeek-R1 model integration
"""
import subprocess
import sys

def install_requirements():
    """Install the required packages for DeepSeek-R1 model"""
    print("🔧 Installing DeepSeek-R1 Model Dependencies")
    print("=" * 60)
    
    requirements = [
        "transformers>=4.36.0",
        "torch>=2.0.0",
        "accelerate>=0.20.0",
        "sentencepiece>=0.1.99",
        "protobuf>=3.20.0",
        "tokenizers>=0.15.0"
    ]
    
    for requirement in requirements:
        print(f"📦 Installing {requirement}...")
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", requirement
            ])
            print(f"✅ Successfully installed {requirement}")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install {requirement}: {e}")
            return False
    
    print("\n🎉 All dependencies installed successfully!")
    print("\n📋 Installed packages:")
    for req in requirements:
        print(f"   ✅ {req}")
    
    print("\n🚀 Ready to use DeepSeek-R1 model!")
    return True

if __name__ == "__main__":
    success = install_requirements()
    if not success:
        print("\n❌ Installation failed. Please check the error messages above.")
        sys.exit(1)
    else:
        print("\n✅ Installation completed successfully!")
