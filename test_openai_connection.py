#!/usr/bin/env python
"""
Test OpenAI API connection and diagnose issues
"""
import os
import sys
import django
from django.conf import settings

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'jira_analyzer.settings')
django.setup()

import openai
from dotenv import load_dotenv
import asyncio

def test_environment_variables():
    """Test if environment variables are loaded correctly"""
    print("🔍 Testing Environment Variables...")
    
    # Load environment variables
    load_dotenv()
    
    api_key = os.getenv('OPENAI_API_KEY')
    print(f"API Key loaded: {'Yes' if api_key else 'No'}")
    
    if api_key:
        # Show first and last 10 characters for security
        masked_key = f"{api_key[:10]}...{api_key[-10:]}"
        print(f"API Key (masked): {masked_key}")
        print(f"API Key length: {len(api_key)} characters")
        print(f"API Key starts with 'sk-': {'Yes' if api_key.startswith('sk-') else 'No'}")
    
    return api_key

def test_openai_client_initialization():
    """Test OpenAI client initialization"""
    print("\n🔧 Testing OpenAI Client Initialization...")
    
    try:
        api_key = os.getenv('OPENAI_API_KEY')
        if not api_key:
            print("❌ No API key found")
            return None
            
        client = openai.OpenAI(api_key=api_key)
        print("✅ OpenAI client initialized successfully")
        return client
    except Exception as e:
        print(f"❌ Failed to initialize OpenAI client: {str(e)}")
        return None

async def test_openai_api_call(client):
    """Test actual API call to OpenAI"""
    print("\n🌐 Testing OpenAI API Call...")
    
    if not client:
        print("❌ No client available for testing")
        return False
    
    try:
        # Test with a simple completion
        response = await client.chat.completions.create(
            model="gpt-3.5-turbo",  # Use accessible model for testing
            messages=[
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": "Say 'Hello, API test successful!' if you can read this."}
            ],
            max_tokens=50,
            temperature=0.1
        )
        
        print("✅ API call successful!")
        print(f"Response: {response.choices[0].message.content}")
        return True
        
    except openai.AuthenticationError as e:
        print(f"❌ Authentication Error: {str(e)}")
        print("💡 This usually means the API key is invalid or expired")
        return False
    except openai.RateLimitError as e:
        print(f"❌ Rate Limit Error: {str(e)}")
        print("💡 You've exceeded your API rate limit")
        return False
    except openai.APIError as e:
        print(f"❌ API Error: {str(e)}")
        print("💡 There's an issue with the OpenAI service")
        return False
    except Exception as e:
        print(f"❌ Unexpected Error: {str(e)}")
        return False

def test_django_gpt_integration():
    """Test the Django GPT integration"""
    print("\n🔗 Testing Django GPT Integration...")
    
    try:
        from analyzer.gpt_integration import GPTHandler
        
        handler = GPTHandler()
        print("✅ GPTHandler initialized successfully")
        print(f"API Key configured in handler: {'Yes' if handler.api_key else 'No'}")
        
        return handler
    except Exception as e:
        print(f"❌ Failed to initialize GPTHandler: {str(e)}")
        return None

async def test_full_integration():
    """Test the full integration end-to-end"""
    print("\n🎯 Testing Full Integration...")
    
    try:
        from analyzer.gpt_integration import GPTHandler
        
        handler = GPTHandler()
        
        # Test the generate_response method
        response = await handler.generate_response(
            "Hello, this is a test message. Please respond with 'Integration test successful!'"
        )
        
        print("✅ Full integration test successful!")
        print(f"Response: {response}")
        return True
        
    except Exception as e:
        print(f"❌ Full integration test failed: {str(e)}")
        return False

async def main():
    """Run all tests"""
    print("🧪 OpenAI API Connection Diagnostic Tests")
    print("=" * 60)
    
    # Test 1: Environment Variables
    api_key = test_environment_variables()
    
    # Test 2: OpenAI Client
    client = test_openai_client_initialization()
    
    # Test 3: Direct API Call
    api_success = await test_openai_api_call(client) if client else False
    
    # Test 4: Django Integration
    handler = test_django_gpt_integration()
    
    # Test 5: Full Integration
    integration_success = await test_full_integration() if handler else False
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Summary:")
    print(f"Environment Variables: {'✅ PASS' if api_key else '❌ FAIL'}")
    print(f"OpenAI Client Init: {'✅ PASS' if client else '❌ FAIL'}")
    print(f"Direct API Call: {'✅ PASS' if api_success else '❌ FAIL'}")
    print(f"Django Integration: {'✅ PASS' if handler else '❌ FAIL'}")
    print(f"Full Integration: {'✅ PASS' if integration_success else '❌ FAIL'}")
    
    if all([api_key, client, api_success, handler, integration_success]):
        print("\n🎉 All tests passed! OpenAI integration should be working.")
    else:
        print("\n⚠️  Some tests failed. Check the issues above.")
        
        # Provide specific troubleshooting advice
        if not api_key:
            print("\n💡 Troubleshooting: API Key Issues")
            print("   1. Check if .env file exists in project root")
            print("   2. Verify OPENAI_API_KEY is set correctly")
            print("   3. Restart Django server after changing .env")
        
        if api_key and not api_success:
            print("\n💡 Troubleshooting: API Connection Issues")
            print("   1. Verify API key is valid and active")
            print("   2. Check OpenAI account billing status")
            print("   3. Verify internet connection")
            print("   4. Check for rate limiting")

if __name__ == "__main__":
    asyncio.run(main())
