#!/usr/bin/env python
"""
Test the Enhanced Intelligent AI Agent with LLM brain, memory, and web access
"""
import os
import sys
import django
from django.conf import settings

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'jira_analyzer.settings')
django.setup()

from django.contrib.auth.models import User
from analyzer.chat_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>
import asyncio
from asgiref.sync import sync_to_async

async def test_intelligent_agent():
    """Test the enhanced AI Agent with LLM brain, memory, and web access"""
    print("🧠 Enhanced Intelligent AI Agent Test")
    print("=" * 80)
    
    try:
        # Get the user with actual data (Hamada)
        user = await sync_to_async(User.objects.get)(username='<PERSON><PERSON>')
        print(f"✅ Testing with user: {user.username}")
        
        # Initialize chat handler
        handler = <PERSON><PERSON><PERSON><PERSON><PERSON>(user)
        print("✅ Chat handler with intelligent agent initialized")
        
        # Test various types of queries
        test_queries = [
            # Jira analysis queries
            {
                'category': 'Jira Analysis',
                'queries': [
                    "Analyze my latest Jira data",
                    "Show me client performance metrics",
                    "What are the priority distributions in my tickets?"
                ]
            },
            # Web search queries
            {
                'category': 'Web Search & Knowledge',
                'queries': [
                    "What is agile methodology?",
                    "Explain scrum framework",
                    "How to improve team productivity?"
                ]
            },
            # Hybrid queries (Jira + Web)
            {
                'category': 'Hybrid Intelligence',
                'queries': [
                    "How can I improve my Jira workflow using agile best practices?",
                    "What are the latest trends in project management for my type of issues?",
                    "Compare my client satisfaction with industry standards"
                ]
            },
            # Memory and conversation
            {
                'category': 'Memory & Conversation',
                'queries': [
                    "Hello! I'm new to project management",
                    "Remember my previous question about agile",
                    "What did we discuss about my Jira data?"
                ]
            }
        ]
        
        conversation_id = None
        
        print(f"\n🧪 Testing Enhanced AI Agent Capabilities:")
        print("=" * 80)
        
        for category_data in test_queries:
            category = category_data['category']
            queries = category_data['queries']
            
            print(f"\n📂 **{category}** ({len(queries)} queries)")
            print("-" * 60)
            
            for i, query in enumerate(queries, 1):
                print(f"\n{i}. Query: '{query}'")
                print("." * 40)
                
                # Process the message
                result = await handler.process_message(query, conversation_id)
                
                # Use the same conversation ID for memory testing
                if not conversation_id:
                    conversation_id = result['conversation_id']
                
                # Display the response
                response = result['response']
                print(f"🤖 Response:\n{response}")
                
                # Show metadata
                print(f"\n📊 Metadata:")
                print(f"   Status: {result['status']}")
                print(f"   Response Length: {len(response)} characters")
                print(f"   Conversation ID: {result['conversation_id']}")
                
                # Check memory status
                memory_status = handler.intelligent_agent.get_memory_status()
                print(f"   Memory Status: {memory_status}")
                
                print("\n" + "=" * 60)
        
        print("\n🎉 All intelligent agent tests completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run the intelligent AI Agent test"""
    print("🧪 Enhanced Intelligent AI Agent Test")
    print("=" * 100)
    
    success = asyncio.run(test_intelligent_agent())
    
    print("\n" + "=" * 100)
    if success:
        print("🎉 SUCCESS! Enhanced Intelligent AI Agent is working perfectly!")
        print("\n🧠 **Key Capabilities Demonstrated:**")
        print("   ✅ LLM Brain - Advanced language understanding and generation")
        print("   ✅ Memory System - Remembers conversations and database context")
        print("   ✅ Web Search - Access to real-time information from the internet")
        print("   ✅ Jira Analysis - Deep insights from your actual data")
        print("   ✅ Hybrid Intelligence - Combines multiple data sources")
        print("   ✅ Intent Recognition - Understands different types of queries")
        print("   ✅ Context Awareness - Maintains conversation flow")
        
        print("\n🌐 **Web Search Capabilities:**")
        print("   • Google Custom Search API (if configured)")
        print("   • DuckDuckGo Instant Answers")
        print("   • Built-in knowledge base for common terms")
        print("   • Intelligent fallback responses")
        
        print("\n🧠 **Memory Features:**")
        print("   • Conversation history tracking")
        print("   • Database state memory")
        print("   • Web search result caching")
        print("   • Context preservation across queries")
        
        print("\n🔧 **Setup Instructions for Enhanced Features:**")
        print("   1. For Google Search (optional):")
        print("      - Get Google Custom Search API key")
        print("      - Set GOOGLE_API_KEY and GOOGLE_CSE_ID in .env")
        print("   2. For better LLM responses:")
        print("      - Get Hugging Face token")
        print("      - Set HUGGINGFACE_TOKEN in .env")
        
        print("\n🌐 Test in the web interface:")
        print("   1. Go to: http://127.0.0.1:8000/ai-agent/")
        print("   2. Login as 'Hamada' (or create account)")
        print("   3. Try these enhanced queries:")
        print("      • 'Analyze my Jira data and suggest improvements'")
        print("      • 'What is DevOps and how can it help my team?'")
        print("      • 'Compare my client satisfaction with industry best practices'")
        print("      • 'Remember my previous analysis and show trends'")
        
        print("\n💡 **The AI Agent now has:**")
        print("   • Advanced reasoning capabilities")
        print("   • Real-time web access for current information")
        print("   • Persistent memory across conversations")
        print("   • Multi-modal intelligence (Jira + Web + Knowledge)")
        print("   • Professional-grade responses with citations")
        
    else:
        print("❌ Test failed. Check the error messages above.")
        print("\n🔧 Troubleshooting:")
        print("   • Ensure Django is properly configured")
        print("   • Check that user 'Hamada' exists with Jira data")
        print("   • Verify internet connection for web search")
        print("   • Check .env file for API keys (optional)")

if __name__ == "__main__":
    main()
