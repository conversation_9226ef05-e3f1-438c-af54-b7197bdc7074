Issue Type,Key,Priority,Summary,Creator,Created,Date of First Response,Redeclared,Description.1,cleaned_description,sentiment,Resolution Time (Days),days_old,temporal_decay,sentiment_impact,decayed_ticket_count,ticket_impact,priority_impact,Issue_Type_impact,urgency_score
Incident,OEKB-6250,Critical,M12-I5 - MIGR: error when changing Entitlement Fixing Date,Automation test,2024-08-30 11:13:00,2024-08-30 11:33:00,YES,"Event PROX00063516 is a migrated event from M10 PROD into M12 TEST. 

To test instructions I had to change the entitlement fixing date of one of the three existing events. After changing this date and clicking on SAVE I got one warning (which is obviously wrong but accepted also in M10 PROD): 
!image-2024-08-30-11-02-57-246.png! 

but also following error: 
!image-2024-08-30-11-03-17-790.png! 

Due to this error, testing SRD II events after migration is very complicated (see Oussemas suggustion how this could be tested). 

Please check what is the problem here. 

Thanks, 
stefan 

[This ticket was automatically created]",event prox migrated event prod test test instruction change entitlement fixing date one three existing event changing date clicking save got one warning obviously wrong accepted also prod imagepng also following error imagepng due error testing srd ii event migration complicated see oussemas suggustion could tested please check problem thanks stefan ticket automatically created,-0.19817975163459778,0.0,276,0.010051835744633586,0.29954493790864944,1.***************,0.2,0.15,0.15,69.93%
Information Request,OEKB-6249,Blocker,M12-I5: DVCA - wrong tax calculation,Automation test,2024-08-29 14:43:00,2024-08-29 15:06:00,NO,"Event DVCA0000002383 

The amount per share is EUR 10, the withholding tax rate is 10%, we have one client with 920 shares which should lead to a dividend of gross EUR 9200, a tax rate of EUR 920 and a net amount of EUR 8280. Instead of that, MegaCor calculates the following amounts in the entitlements: 

!image-2024-08-29-14-21-02-485.png! 

Please check that as normal dividends have been calculated correctly in so so many cases - why is here an other taxable amount? 

BR, stefan 


[This ticket was automatically created]",event dvca amount per share eur withholding tax rate one client share lead dividend gross eur tax rate eur net amount eur instead megacor calculates following amount entitlement imagepng please check normal dividend calculated correctly many case taxable amount br stefan ticket automatically created,0.0024287328124046326,0.0,277,0.009885693513350894,0.*****************,1.***************,0.2,0.14,0.06,47.41%
Requirement,OEKB-6248,Medium,M12-ASOC: M12-ASOC: Incorrect quantities in movements,Stefan RIBISCH,2024-08-29 13:20:00,2024-08-29 13:28:00,NO,"Event EXRI0000002366, account 221700 

After opening an event and the options/movement screen there are different values: 

First error: Instr. Quantity: 

For option 001/EXER there are following instructions existing: 
!image-2024-08-28-16-03-35-507.png! 

Although the status of the instruction for 1.234 shares is Rejected, the following is shown at option 001: 
!image-2024-08-28-16-02-59-931.png! 

This is wrong - the correct quantity is here 380 shares. 

  

Second error: Quantity: 

For option 003/OVER there are following instructions existing: 

!image-2024-08-28-16-07-09-911.png! 

So the 1.001 shs at Instr. Quantity is correct: 
!image-2024-08-28-16-07-52-391.png! 

BUT the different quantities in the different movements are definitively not correct and show at every try another result: 

First try: 
!image-2024-08-28-16-08-36-869.png! 

Second try: 
!image-2024-08-28-16-09-39-385.png! 

Third try: 
!image-2024-08-28-16-11-26-943.png! 

It is absolutely not understandable why ASOC shows on every call a different result. It seems random for us. 

To summarize: 
- Instr. Quantity must not include Rejected instructions 
- the Quantity and all dependent calculations within the different movements must match to the Instr. Quantity and include all instructions, not only one of them and not only a random other one. 

Please check and analyze that. 

Thanks, 
stefan",event exri account opening event optionsmovement screen different value first error instr quantity option exer following instruction existing imagepng although status instruction share rejected following shown option imagepng wrong correct quantity share second error quantity option following instruction existing imagepng shs instr quantity correct imagepng different quantity different movement definitively correct show every try another result first try imagepng second try imagepng third try imagepng absolutely understandable asoc show every call different result seems random u summarize instr quantity must include rejected instruction quantity dependent calculation within different movement must match instr quantity include instruction one random one please check analyze thanks stefan,-0.023591838777065277,0.0,277,0.009885693513350894,0.****************,0.*****************,0.*****************,0.1,0.09,20.01%
Requirement,OEKB-6247,Medium,M12-ASOC: M12-ASOC: Different views for same option,Stefan RIBISCH,2024-08-29 10:19:00,2024-09-19 17:55:00,NO,"Event EXRI0000002366 and EXRI0000002371 

For both events there are existing instructions for Option 003/OVER, but when you open the event and the options/movements both contents look different: 

The following is displayed at event EXRI0000002371: 

!image-2024-08-28-16-40-37-970.png! 

This is how event EXRI0000002366 looks like. There are some more fields displayed: 

!image-2024-08-28-16-39-00-211.png! 

As it is the same option and both events are EXRIs - why are the movement blocks filled in so different? For one event the quantities are included and some amounts are calculated, in the other event these details are missing. 

We also do not know if these differences could occur also for other events - for these events we noticed the differences just for this option 003/OVER. 

Attached in the Word-file (from OEKB IT) you find the result ASOC received from MegaCor for these two events. 

Please analyse why ASOC receives different content for two things which should look the same. 

Thanks, 
stefan",event exri exri event existing instruction option open event optionsmovements content look different following displayed event exri imagepng event exri look like field displayed imagepng option event exris movement block filled different one event quantity included amount calculated event detail missing also know difference could occur also event event noticed difference option attached wordfile oekb find result asoc received megacor two event please analyse asoc receives different content two thing look thanks stefan,-0.02629982680082321,21.0,277,0.009885693513350894,0.2565749567002058,0.*****************,0.*****************,0.1,0.09,20.11%
Incident,OEKB-6246,Blocker,M12-ASOC: M12-ASOC: Quantity-calculation not correct (Instr. quantity),Stefan RIBISCH,2024-08-28 17:40:00,2024-09-02 23:17:00,NO,"In addition to OEKB-6245 we noticed that the amount of instr. quantity is not correct. 

The event shows following amount: 2615 shares 
!image-2024-08-28-15-34-37-979.png! 

This consists of the following two amounts: 
!image-2024-08-28-16-39-56-915.png! 
!image-2024-08-28-16-39-21-153.png! 

  

So far so good, but if I look deeper into the different instructions, the following instructions are found: 
!image-2024-08-28-17-38-19-544.png! 

As you see, the instruction of 1.234 shares is Rejected, so in our opinion this should not count to the Instr. quantity. 

Please check why these shares count to instr. quantity although they are rejected. 

Thanks, 
stefan",addition oekb noticed amount instr quantity correct event show following amount share imagepng consists following two amount imagepng imagepng far good look deeper different instruction following instruction found imagepng see instruction share rejected opinion count instr quantity please check share count instr quantity although rejected thanks stefan,-0.03408992663025856,5.0,277,0.009885693513350894,0.25852248165756464,0.*****************,0.*****************,0.14,0.15,35.4%
Incident,OEKB-6245,Blocker,M12-ASOC: M12-ASOC: Quantity-calculation not correct (general),Stefan RIBISCH,2024-08-28 17:27:00,2024-09-02 23:17:00,NO,"When opening an event, the calculated quantities are not correct. 

Although there are exsting instructions, the remaining quantity is equal to the eligible quantity. In our opinion, the remaining quantity should be reduced from the eligible quantity by the instructed quantity: 

!image-2024-08-28-15-34-37-979.png! 

OEKB IT confirmed what they receive from MegaCor: 
!image-2024-08-28-16-37-28-032.png! 

!image-2024-08-28-16-39-56-915.png! 
!image-2024-08-28-16-40-31-155.png! 
!image-2024-08-28-16-39-21-153.png! 

Please check why there is no correct calculation of the remaining quantity. 

Thanks, 
stefan",opening event calculated quantity correct although exsting instruction remaining quantity equal eligible quantity opinion remaining quantity reduced eligible quantity instructed quantity imagepng oekb confirmed receive megacor imagepng imagepng imagepng imagepng please check correct calculation remaining quantity thanks stefan,-0.10765309631824493,5.0,277,0.009885693513350894,0.27691327407956123,0.*****************,0.*****************,0.14,0.15,38.16%
Incident,OEKB-6244,Cosmetic,M12: Exercice instead of Exercise,Automation test,2024-08-28 17:13:00,2024-10-07 09:28:00,NO,"Hello, 

in several points in the system there is the typing mistake EXERCICE instead of EXERCISE: 

!image-2024-08-28-16-53-59-286.png! 

As yesterday I noticed that this is in ASOC also: Please correct this on all necessary points, including ASOC: 

!image-2024-08-28-16-54-17-751.png! 

Thanks, 
stefan 


[This ticket was automatically created]",hello several point system typing mistake exercice instead exercise imagepng yesterday noticed asoc also please correct necessary point including asoc imagepng thanks stefan ticket automatically created,-0.0058927983045578,39.0,277,0.009885693513350894,0.25147319957613945,1.***************,0.2,0.1,0.15,55.22%
Incident,OEKB-6243,Major,M12-ASOC: Messages and instructions are not correctly found,Stefan RIBISCH,2024-08-28 15:18:00,2024-09-02 23:17:00,YES,"As discussed in our meeting today! 

Event EXRI0000002366 (but the same result for every event) 

Today I entered a C/A instruction in ASOC which was not found with following search: 
!image-2024-08-28-11-10-44-040.png! 

When I adapt the search and change the Creation Date to to tomorrow, the instruction is found: 
!image-2024-08-28-11-11-12-685.png! 

The same result for Breakdown instructions and also for messages. Following messages were received until today: 

!image-2024-08-28-15-12-13-849.png! 

But when I search for messages, the ones from today are not found with the following search: 
!image-2024-08-28-15-13-09-401.png! 

Here is the search which is used from OEKB IT: 
_Json-Object at.oekb.casa.swcodegen.megacor.model.InstructionSearchParams; {""pageSize"":100000,""client"":[_ 

_{""value"":""221700""}_ 

_],""pageNumber"":1,""creationDateFrom"":""2024-08-19T22:00:00.000+00:00"",""creationDateTo"":""{color:#ff0000}2024-08-27T21:59:59.000+00:00{color}"",""mainReference"":""EXRI0000002366"",""forValidation"":false}_ 

_This means 23:59:59: CEST und would be okay but MegaCor doesn't come back with the correct results._ 

  

Please check why it seems that ""today"" is filtered out in these searches. 

Thanks, 
stefan",discussed meeting today event exri result every event today entered ca instruction asoc found following search imagepng adapt search change creation date tomorrow instruction found imagepng result breakdown instruction also message following message received today imagepng search message one today found following search imagepng search used oekb jsonobject atoekbcasaswcodegenmegacormodelinstructionsearchparams pagesizeclient value pagenumbercreationdatefromtcreationdatetocolorfftcolormainreferenceexriforvalidationfalse mean cest und would okay megacor doesnt come back correct result please check seems today filtered search thanks stefan,0.01577363908290863,5.0,278,0.00972229737161013,0.24605659022927284,0.*****************,0.*****************,0.13,0.15,32.03%
Incident,OEKB-6242,Major,M12-I2: SHPR Event - Cancellation of Market Claim,Automation test,2024-08-27 16:13:00,2024-08-27 16:22:00,NO,"test with SHPR0000002335 

Hello, 

I have now cancelled two claims that were in status ‘Sent’ in M12.  

The cancellation was successful. 

However, we have noticed that the payments can no longer be found under ‘List Market Claims Payments’, but only under ‘List Client Payment’ with the status ‘cancelled’. 

It would be good if we could also find them under ‘List Market Claims Payments’ with status ‘cancelled’. 

  

Thank you and BR, Dalibor 

  


[This ticket was automatically created]",test shpr hello cancelled two claim status sent cancellation successful however noticed payment longer found list market claim payment list client payment status cancelled would good could also find list market claim payment status cancelled thank br dalibor ticket automatically created,-0.6734911762177944,0.0,279,0.009561601930543505,0.4183727940544486,1.***************,0.2,0.13,0.15,84.76%
Incident,OEKB-6241,Major,M12-I2: MT566 OUT missing XDTE and Name,Automation test,2024-08-27 15:13:00,2024-08-27 15:49:00,NO,"test with SHPR0000002335 and DVCA0000002334 

Hello,  

The XDTE and the name of the security are missing in the outgoing MT566 messages. This information is available in MT564REPE.  

All the Information about XDTE, RD etc. which is mentioned in the MT564 should be also listed in the outgoing MT566. 

  

Thank you and BR, Dalibor 

  


[This ticket was automatically created]",test shpr dvca hello xdte name security missing outgoing mt message information available mtrepe information xdte rd etc mentioned mt also listed outgoing mt thank br dalibor ticket automatically created,-0.11008080281317234,0.0,279,0.009561601930543505,0.2775202007032931,1.***************,0.2,0.13,0.15,63.63%
Information Request,OEKB-6239,Major,M12-I2: CAPD0000000868 - Payment Cancellation,Automation test,2024-08-26 10:13:00,2024-08-26 10:31:00,NO,"test with CAPD0000000868 

Hello,  

the above event is in status ‘Confirmed’. I have now tried to cancel the ‘Client Payment’. 

The client debit is now booked in SAP, but the status in M12 is still ‘WaitingCashBookingCancellation’. 

  

Please check! 

  

Thank you and BR, Dalibor 


[This ticket was automatically created]",test capd hello event status confirmed tried cancel client payment client debit booked sap status still waitingcashbookingcancellation please check thank br dalibor ticket automatically created,-0.09144880436360836,0.0,280,0.009403562551495206,0.2728622010909021,1.***************,0.2,0.13,0.06,49.43%
Incident,OEKB-6238,Blocker,M12 PROD/TEST - Alert IJ031070: Transaction cannot proceed: STATUS_MARKED_ROLLBACK,Valdes ELIZABEHT,2024-08-23 13:22:00,2024-08-23 15:06:00,NO,"Dear All,  

the business Users wanted to test some Claims - and therefore we needed pending trades.  

We created a pending trade file for TEST and I loaded it manuelly- but they are failing with following Alert:  

{color:#de350b}Event Code : NOTIFICATION_IN_FAILURE{color} 
{color:#de350b}Description : Failure while treating an incoming message{color} 
{color:#de350b}IJ031070: Transaction cannot proceed: STATUS_MARKED_ROLLBACK{color} 

{color:#172b4d}Under Interface in Monitoring : {color} 

{color:#de350b}!image-2024-08-23-13-22-00-084.png!{color} 

  

{color:#172b4d}Please check and revert. {color} 

{color:#172b4d}Thanks & KR; {color} 

{color:#172b4d}Eli{color}",dear business user wanted test claim therefore needed pending trade created pending trade file test loaded manuelly failing following alert colordebevent code notificationinfailurecolor colordebdescription failure treating incoming messagecolor colordebij transaction proceed statusmarkedrollbackcolor colorbdunder interface monitoring color colordebimagepngcolor colorbdplease check revert color colorbdthanks kr color colorbdelicolor,-0.5089676957577467,0.0,283,0.008944945394115252,0.3772419239394367,0.*****************,0.012503434490811616,0.14,0.15,51.96%
Incident,OEKB-6237,Critical,M12-I5: PARI0000002316 - No MT564 sent to CCSYS (DEF003) and 3i (DEF005),Automation test,2024-08-23 11:43:00,2024-08-23 12:44:00,NO,"PARI0000002316 
When trying to test transformation process in CCSYS, I noticed that no MT564 has been generated/sent for DEF003 (receiver CCSYATXXXXXX). 

Moreover, also no MT564 to DEF005 (3i) has been generated/sent. 

Could you please check! 
Thanks, Bert 

[This ticket was automatically created]",pari trying test transformation process ccsys noticed mt generatedsent def receiver ccsyatxxxxxx moreover also mt def generatedsent could please check thanks bert ticket automatically created,0.04097510874271393,0.0,283,0.008944945394115252,0.23975622281432152,1.***************,0.2,0.15,0.15,60.96%
Information Request,OEKB-6236,Critical,M12 - SWIFTs to CCSYS for Transformations,Valdes ELIZABEHT,2024-08-23 11:41:00,2024-08-23 11:51:00,NO,"Dear All, 

OeKB IT have noticed that the filling of the MT564 from the M12 QAS differs from the M10 production.  

Example:  

MT564 from M12QAS  :{*}98E::{*}PREP//20240822113110/02 

MT564 from M10Prod :{*}{color:#de350b}98C{color}{*}::PREP//20240822151626 

=> can you check when this was amended or requested from our side? 

I think that OeKB IT has to amend this behaviour as we inform our clients vie E-Format!  

Do you agree? 

Thanks & KR,  

Eli 

 ",dear oekb noticed filling mt qas differs production example mt mqas eprep mt mprod colordebccolorprep check amended requested side think oekb amend behaviour inform client vie eformat agree thanks kr eli,0.30746996868401766,0.0,283,0.008944945394115252,0.17313250782899559,0.*****************,0.012503434490811616,0.15,0.06,9.35%
Incident,OEKB-6234,Critical,M12-I5 - MIGR: Vote Instruction Types not migrated,Automation test,2024-08-22 16:13:00,2024-08-22 16:18:00,NO,"Event PROX00062805 

Maybe there is a connection to OEKB-6210? 

We noticed that for above event the whole Vote Instruction Types were not migrated. 

E.g. resolution ""4"": 

M10 PROD: 
!image-2024-08-22-15-35-58-174.png! 

M12 TEST: 
!image-2024-08-22-15-37-13-083.png! 

Please make sure that all events are migrated completely with all existing data. 

Thanks, 
stefan 


[This ticket was automatically created]",event prox maybe connection oekb noticed event whole vote instruction type migrated eg resolution prod imagepng test imagepng please make sure event migrated completely existing data thanks stefan ticket automatically created,0.018732648342847824,0.0,284,0.008797098451105509,0.24531683791428804,1.***************,0.2,0.15,0.15,61.8%
Incident,OEKB-6233,Medium,M12-I5 - MIGR: wrong PAYD in MT566 REVR,Automation test,2024-08-22 14:13:00,2024-08-22 15:23:00,YES,"Event TEND00018102 

Payment Dates of the options in this event are set on ONGO. Although this, the following is included in the outgoing MT566 REVR (you find the whole SWIFT also attached): 

!image-2024-08-22-13-51-16-291.png! 

  

Please check, as no PAYD should be sent out when no PAYD is given (see also example of MT566 NEWM of M10 also attached). 

Thanks, 
stefan 


[This ticket was automatically created]",event tend payment date option event set ongo although following included outgoing mt revr find whole swift also attached imagepng please check payd sent payd given see also example mt newm also attached thanks stefan ticket automatically created,-0.013127662241458893,0.0,284,0.008797098451105509,0.2532819155603647,1.***************,0.2,0.1,0.15,55.49%
Incident,OEKB-6232,Critical,M12-I5: ASOC - EXRI0000002307 - Client Instruction rejected / Comment not saved,Automation test,2024-08-22 14:13:00,2024-08-27 15:20:00,YES,"EXRI0000002307 
Instruction for OVER was entered in ASOC including instruction comment => 

!image-2024-08-22-14-01-26-077.png|thumbnail! 

However, instruction was rejected in M12 due to the following error => 

!image-2024-08-22-14-02-18-963.png|thumbnail! 

When checking the instruction in ASOC, I noticed that the comment was obviously not saved by the system => 

!image-2024-08-22-14-03-17-361.png|thumbnail! 


Could you please check? 
Thanks, Bert 






[This ticket was automatically created]",exri instruction entered asoc including instruction comment imagepngthumbnail however instruction rejected due following error imagepngthumbnail checking instruction asoc noticed comment obviously saved system imagepngthumbnail could please check thanks bert ticket automatically created,-0.18323362059891224,5.0,284,0.008797098451105509,0.29580840514972806,1.***************,0.2,0.15,0.15,69.37%
Incident,OEKB-6231,Major,M12-ASOC: wrong UTC time indicator is sent from MegaCor,Stefan RIBISCH,2024-08-22 13:05:00,2024-08-23 15:15:00,YES,"As discussed in the today's ASOC-meeting with Hafedh: MegaCor sends for all deadlines the following format: ""2024-05-28T22:00:00.000+00:00"" 

So MegaCor sends UTC-Time for every deadline which is not correct. 

MegaCor should send the correct UTC-time difference --> in the moment +02:00, in winter +01:00 

Thanks, 
Stefan",discussed today asocmeeting hafedh megacor sends deadline following format megacor sends utctime every deadline correct megacor send correct utctime difference moment winter thanks stefan,0.015566963702440262,1.0,284,0.008797098451105509,0.24610825907438993,0.*****************,0.*****************,0.13,0.15,32.04%
Incident,OEKB-6230,Major,M12-I2: DVCA Event - Claim - CSD Hold,Automation test,2024-08-22 11:43:00,2024-08-26 13:55:00,NO,"test with DVCA0000001992 - AT0000A05J15 

Hello,  

Claims-relevant trades were created in CCSYS for the above-mentioned ISIN. 

Claims were also generated that still have CSD Hold on them even though the event is paid and confirmed. 

In M12, the ‘Client Payment’ for these claims has the status ‘WaitingSettlementConfirmation’. 

Please check! 

  

Thank you and BR, Dalibor 

  


[This ticket was automatically created]",test dvca ataj hello claimsrelevant trade created ccsys abovementioned isin claim also generated still csd hold even though event paid confirmed client payment claim status waitingsettlementconfirmation please check thank br dalibor ticket automatically created,0.0037944242358207703,4.0,284,0.008797098451105509,0.2490513939410448,1.***************,0.2,0.13,0.15,59.36%
Incident,OEKB-6229,Major,M12-I5: ASOC Cancellation Client Instruction failed,Automation test,2024-08-21 16:43:00,2024-08-21 17:57:00,YES,"TEND0000002299 
Client Instruction was entered/validated via ASOC and is in the correct status ""Created"". 
When trying to request cancellation via => 

!image-2024-08-21-16-38-45-149.png|thumbnail! 

!image-2024-08-21-16-39-07-178.png|thumbnail! 

an error occured => 

!image-2024-08-21-16-39-57-083.png|thumbnail! 


However, when checking the instruction, I noticed that the Cancellation Request was executed => 

!image-2024-08-21-16-41-39-348.png|thumbnail! 

but the Request cannot be validated by a 2nd user!? 


Could you please check? 
Thanks, Bert 




[This ticket was automatically created]",tend client instruction enteredvalidated via asoc correct status created trying request cancellation via imagepngthumbnail imagepngthumbnail error occured imagepngthumbnail however checking instruction noticed cancellation request executed imagepngthumbnail request validated nd user could please check thanks bert ticket automatically created,-0.35015255585312843,0.0,285,0.008651695203120634,0.3375381389632821,1.***************,0.2,0.13,0.15,72.63%
Information Request,OEKB-6227,Major,M12-I2: REDM0000001380 - List breakdown Instructions,Automation test,2024-08-21 14:43:00,2024-08-21 15:01:00,NO,"Hello,  

I have entered a Breakdown Instruction in the above-mentioned event via ASOC. 

The instruction could be found in the menu item ‘List Breakdown Instructions’ and also in the event itself under Client Instructions => List Breakdown Instructions. 

I have cancelled the instructions in ASOC (CANCELDRAFT)  

The instruction is now in M12 (REDM0000001380 => List Breakdown Instructions) in CancelDraft status but it can no longer be found in the ‘List Breakdown Instructions’ menu item. 

Please check! 

  

Thank you and BR, Dalibor 

  


[This ticket was automatically created]",hello entered breakdown instruction abovementioned event via asoc instruction could found menu item list breakdown instruction also event client instruction list breakdown instruction cancelled instruction asoc canceldraft instruction redm list breakdown instruction canceldraft status longer found list breakdown instruction menu item please check thank br dalibor ticket automatically created,-0.19428098388016224,0.0,285,0.008651695203120634,0.29857024597004056,1.***************,0.2,0.13,0.06,53.29%
Incident,OEKB-6226,Critical,M12-I5: EXRI0000002095 - BUYA - MP incorrect,Automation test,2024-08-21 12:13:00,2024-08-27 14:57:00,YES,"EXRI0000002095 
BUYA Market Payments have been created as follows: 

- Single TRAD instruction (RVP) against Broker 221700, which is correct 
- however, also a ""2nd"" MP was created (232100 receives against 9232100) which is not correct, as now 2321 received the payments twice. 

Could you please check? 
Thanks, 
Bert 




[This ticket was automatically created]",exri buya market payment created follows single trad instruction rvp broker correct however also nd mp created receives correct received payment twice could please check thanks bert ticket automatically created,0.03178851865231991,6.0,285,0.008651695203120634,0.24205287033692002,1.***************,0.2,0.15,0.15,61.31%
Incident,OEKB-6223,Major,M12-I2: PRED0000002286 - Update Issue,Automation test,2024-08-20 16:13:00,2024-08-23 16:03:00,NO,"Hello,  

The above event has been updated manually. I have changed the currency from USD to EUR. 

However, this update is not displayed on the ‘Compare’ screen.  

When trying to vaidate the update, an error message appears. 

Please check! 

  

Thank you and BR, Dalibor 

[This ticket was automatically created]",hello event updated manually changed currency usd eur however update displayed compare screen trying vaidate update error message appears please check thank br dalibor ticket automatically created,-0.05616859346628189,2.0,286,0.008508695259434564,0.2640421483665705,1.***************,0.2,0.13,0.15,61.61%
Information Request,OEKB-6221,Major,"M12-I2: INTR0000001868 - Event in Status ""Notified"" and not ""Waiting Payment""",Automation test,2024-08-19 13:13:00,2024-08-19 15:20:00,NO,"INTR0000001868 

the event mentioned above is in status ‘Activated’ ‘Notified’ instead of status ‘Waiting payment’. No entitlements have been calculated. 

  

Please check! 

  

Thank you and BR, Dalibor 


[This ticket was automatically created]",intr event mentioned status activated notified instead status waiting payment entitlement calculated please check thank br dalibor ticket automatically created,-0.009666906669735909,0.0,287,0.008368058896921217,0.252416726667434,1.***************,0.2,0.13,0.06,46.36%
Information Request,OEKB-6220,Major,M12-I5: EXWA0000002239 - Market Payments in wrong status,Automation test,2024-08-19 13:13:00,2024-08-19 15:51:00,NO,"EXWA0000002239 
Pay Dates for all movements are set to ONGO. 
Client Payments were booked correctly with value date 19/08. 

When checking Market Payments, I noticed that those are in status ""WaitingValueDate"", although Value Date and Business Date is 19/08 (today) => 

!image-2024-08-19-12-41-14-998.png|thumbnail! 

Could you please check? 

(To proceed with the test, I forced ""ValueDate"" via the ""To-do-list""-alert) 

Thanks, 
Bert 


[This ticket was automatically created]",exwa pay date movement set ongo client payment booked correctly value date checking market payment noticed status waitingvaluedate although value date business date today imagepngthumbnail could please check proceed test forced valuedate via todolistalert thanks bert ticket automatically created,0.0252015870064497,0.0,287,0.008368058896921217,0.24369960324838758,1.***************,0.2,0.13,0.06,45.05%
Information Request,OEKB-6218,Major,M12-I5 - MIGR: wrong POST and VALU in outgoing MT566,Automation test,2024-08-19 12:45:00,2024-08-19 14:29:00,NO,"Event DVCA00098607 

in outgoing SWIFT MT566 the wrong POSTing and VALUe date has been used: 

!DEC08A2B.jpg! 

although the business date seems to be correct today: 
!85072630.jpg! 

Please check why a wrong/future date is used in the outgoing MT566. 

Thanks, 
stefan 


[This ticket was automatically created]",event dvca outgoing swift mt wrong posting value date used decabjpg although business date seems correct today jpg please check wrongfuture date used outgoing mt thanks stefan ticket automatically created,-0.04197445325553417,0.0,287,0.008368058896921217,0.26049361331388354,1.***************,0.2,0.13,0.06,47.57%
Incident,OEKB-6217,Medium,M12-I5 : unknown Market Payment created,Automation test,2024-08-19 12:14:00,2024-08-19 12:32:00,YES,"Event DVCA00098607 

After resubmitting the Market Payment in status WaitingForValueDate (see bug OEKB-6215), the status changed to WaitingSettlementConfirmation which is OK. I could finalize this event. 

Unfortunately, about 45 minutes later after resubmitting, a second MarketPaymnet appeared in the screen: 

!image-2024-08-19-12-04-30-702.png! 

Where does it come from? Some details are missing, the status immediately changed to Created and no Client payments were generated although the status was Created. 

Please check what happened here. 

Thanks, 
stefan 


[This ticket was automatically created]",event dvca resubmitting market payment status waitingforvaluedate see bug oekb status changed waitingsettlementconfirmation ok could finalize event unfortunately minute later resubmitting second marketpaymnet appeared screen imagepng come detail missing status immediately changed created client payment generated although status created please check happened thanks stefan ticket automatically created,0.02181825414299965,0.0,287,0.008368058896921217,0.2445454364642501,1.***************,0.2,0.1,0.15,54.18%
Incident,OEKB-6216,Major,M12-I5: EXWA0000002239 - Sending of MT564 failed,Automation test,2024-08-19 12:13:00,2024-08-19 15:53:00,NO,"EXWA0000002239 
I manually generated notifications, but when trying to send via SEND ALL (and also via SEND), the following error popped up => 

!image-2024-08-19-11-43-37-193.png|thumbnail! 

When trying to send via ""SendByCA"" (Global View), the following info popped up => 

!image-2024-08-19-11-52-55-986.png|thumbnail! 


However, still no MT564 were sent at all => 

!image-2024-08-19-11-53-42-816.png|thumbnail! 


Could you please check? 
Thanks, Bert 


[This ticket was automatically created]",exwa manually generated notification trying send via send also via send following error popped imagepngthumbnail trying send via sendbyca global view following info popped imagepngthumbnail however still mt sent imagepngthumbnail could please check thanks bert ticket automatically created,-0.028953488916158676,0.0,287,0.008368058896921217,0.25723837222903967,1.***************,0.2,0.13,0.15,60.59%
Information Request,OEKB-6215,Medium,M12-I5 - MIGR: incorrect status and no alert for future payment,Automation test,2024-08-19 11:13:00,2024-08-19 11:51:00,NO,"Event DVCA00098607 

On 13/08 I imported an MT566. Payment Date of the event is 13/08. 

Unfortunately the Business Date was wrong on 13/08, it was just the 12/08 so the market payment got status WatingForValueDate. After changing the business date from 12/08 to 13/08 it was unchanged. 

Today I checked that the status is still WaitingForValueDate. Why didn't the status of the market payment change? 

!image-2024-08-19-10-47-36-112.png! 

Further we noticed that noticed that no alert has been created for this payment: 
!image-2024-08-19-10-48-01-508.png! 

Shouldn't there have been generated an alert for that? And why did the status not change after reaching the value date? 

Please check these things. 

Thanks, stefan 


[This ticket was automatically created]",event dvca imported mt payment date event unfortunately business date wrong market payment got status watingforvaluedate changing business date unchanged today checked status still waitingforvaluedate didnt status market payment change imagepng noticed noticed alert created payment imagepng shouldnt generated alert status change reaching value date please check thing thanks stefan ticket automatically created,0.030512947589159012,0.0,287,0.008368058896921217,0.*****************,1.***************,0.2,0.1,0.06,40.36%
Information Request,OEKB-6214,Major,M12 QAS Position Interface File --Full load of 16.08.2024 missing,Valdes ELIZABEHT,2024-08-19 11:13:00,2024-08-19 12:17:00,NO,"Dear All,  

we are facing an Issue in M12 QAS, as we have received wrong position interface Files.  

On 16.08 we had problems with MQ routing in the test environment an the position full load was created and sent on 17.8.2024 to MC.  

Following batches failed.  

Can you please advise the steps to be done.  

!image-2024-08-19-11-12-10-312.png! 

Thanks & KR,  

Eli",dear facing issue qas received wrong position interface file problem mq routing test environment position full load created sent mc following batch failed please advise step done imagepng thanks kr eli,-0.****************,0.0,287,0.008368058896921217,0.*****************,0.*****************,0.012503434490811616,0.13,0.06,47.84%
Information Request,OEKB-6213,Major,M12-I2: DVCA Event in foreign market- MCP WaitingCashBookingConfirmation,Automation test,2024-08-15 13:13:00,2024-08-15 15:55:00,NO,"test with DVCA0000002140 

Hello,  

please check why the ‘Market Payment’ is still in the ‘WaitingCashBookingConfirmation’ Status. 

Thank you and BR, Dalibor 

  

  


[This ticket was automatically created]",test dvca hello please check market payment still waitingcashbookingconfirmation status thank br dalibor ticket automatically created,0.058099089190363884,0.0,291,0.007828377549225773,0.*****************,1.***************,0.2,0.13,0.06,43.82%
Incident,OEKB-6211,Major,M12-I5: MT566 OUT - ADDINFO,Automation test,2024-08-14 13:13:00,2024-08-14 13:17:00,NO,"test with CAPD0000002193 

Hello, 

It looks as if additional text is displayed even though no additional text was specified in the event. 

Please only display the additional text if it has also been created in the event as ‘MT566 Outgoing Comments’. This also applies to all other events. 

  

Thank you and BR, Dalibor 


[This ticket was automatically created]",test capd hello look additional text displayed even though additional text specified event please display additional text also created event mt outgoing comment also applies event thank br dalibor ticket automatically created,-0.03324775770306587,0.0,292,0.007698985849401584,0.25831193942576647,1.***************,0.2,0.13,0.15,60.75%
Information Request,OEKB-6209,Major,M12-I5 - MIGR: seev.006 failed in MegaBroker,Automation test,2024-08-13 18:13:00,2024-08-14 11:33:00,YES,"_emphasized text_Event PROX00062805 

After rejection of the incoming seev.004 (see separate ticket) a seev.006 has been created immediately but when I opened it in MegaCor, the message was only this: 
!image-2024-08-13-17-51-42-541.png! 

In MegaBroker I saw that the Generation of the seev.006 failed due to following error: 
!image-2024-08-13-17-50-51-934.png! 

  

Please check why this has not been generated. 

Thanks, stefan 


[This ticket was automatically created]",emphasized textevent prox rejection incoming seev see separate ticket seev created immediately opened megacor message imagepng megabroker saw generation seev failed due following error imagepng please check generated thanks stefan ticket automatically created,-0.04955176264047623,0.0,292,0.007698985849401584,0.26238794066011906,1.***************,0.2,0.13,0.06,47.86%
Incident,OEKB-6208,Major,M12 TEST : 70E is overtaken from FlowIn to FlowOut,Automation test,2024-08-13 17:13:00,2024-08-14 11:00:00,NO,"Event DVCA00097605 

In the incoming MT566 an additional text in 70E was included, unfortunately it has been overtaken into the outgoing MT566 which should not be and which works in M12 QAS and also in PROD. 

Here the two SWIFTs: 

!image-2024-08-13-16-46-48-630.png! 

You find them also attached - please check that. 

Thanks, 
stefan 


[This ticket was automatically created]",event dvca incoming mt additional text e included unfortunately overtaken outgoing mt work qas also prod two swift imagepng find also attached please check thanks stefan ticket automatically created,0.01511014811694622,0.0,292,0.007698985849401584,0.24622246297076344,1.***************,0.2,0.13,0.15,58.93%
Information Request,OEKB-6207,Cosmetic,M12-I5 - MIGR: Two flow outs for onw flow in,Automation test,2024-08-13 16:13:00,2024-08-14 11:36:00,YES,"Event DVCA00098607 

We sent in an MT564 REPE. In MegaBroker it is still in Status: 

!image-2024-08-13-16-03-06-857.png! 

because of the two FlowOuts included: 
!image-2024-08-13-16-03-47-472.png! 

And although they have not yet been validated in MegaBroker the MT564 REPE is already visible in MegaCor: 
!image-2024-08-13-16-04-40-678.png! 

Can you explain that? 

Please advise. 

Thanks, stefan 


[This ticket was automatically created]",event dvca sent mt repe megabroker still status imagepng two flowouts included imagepng although yet validated megabroker mt repe already visible megacor imagepng explain please advise thanks stefan ticket automatically created,0.05267356149852276,0.0,293,0.*****************,0.2368316096253693,1.***************,0.2,0.1,0.06,39.52%
Incident,OEKB-6206,Major,M12-I2: - DVCA0000001992 - List Market Claims Entitlements - Missing Columns,Automation test,2024-08-12 17:43:00,2024-08-12 17:58:00,NO,"Hello, 

The following columns are missing for ""List Market Claims Entitlement"" 

Main Reference 

Entitled ISIN 

Impact Type 

Net Amount 

CA Currency 

The claims should ideally be coloured orange (OTC transactions) and blue (Vienna stock exchange transactions) as in M10. 

  

Thank you and BR, Dalibor 


[This ticket was automatically created]",hello following column missing list market claim entitlement main reference entitled isin impact type net amount ca currency claim ideally coloured orange otc transaction blue vienna stock exchange transaction thank br dalibor ticket automatically created,0.*****************,0.0,293,0.*****************,0.*****************,1.***************,0.2,0.13,0.15,58.95%
Incident,OEKB-6205,Major,M12-I2: - DVCA0000001992 - Client Payment - Missing Columns,Automation test,2024-08-12 17:13:00,2024-08-12 17:25:00,NO,"Hello, 

The following columns are missing for ""Client payments"" 

AccountSvcrTxID (SEME Ref.)  

Client Reference 

Trade Date 

If there are market claims, they should ideally be coloured orange (OTC transactions) and blue (Vienna stock exchange transactions) as in M10. 

  

Thank you and BR, Dalibor 


[This ticket was automatically created]",hello following column missing client payment accountsvcrtxid seme ref client reference trade date market claim ideally coloured orange otc transaction blue vienna stock exchange transaction thank br dalibor ticket automatically created,0.00020324811339378357,0.0,293,0.*****************,0.*****************,1.***************,0.2,0.13,0.15,59.49%
Information Request,OEKB-6204,Major,M12-I5 - MIGR: No MT566 OUT visible,Automation test,2024-08-12 17:13:00,2024-08-12 17:29:00,NO,"Event SPLF00006601 

Event is in status confirmed but no entitlements are found (see separate bug). 

Concerning notifications, MT564 OUT are visible but no MT566 OUT: 

!image-2024-08-12-16-58-05-839.png! 

although in M10 PROD MT566 OUT were sent already in July. 

Please check why there are no MT566 OUT in M12 TEST. 

Thanks, stefan 


[This ticket was automatically created]",event splf event status confirmed entitlement found see separate bug concerning notification mt visible mt imagepng although prod mt sent already july please check mt test thanks stefan ticket automatically created,-0.021414708346128464,0.0,293,0.*****************,0.2553536770865321,1.***************,0.2,0.13,0.06,46.8%
Information Request,OEKB-6203,Major,M12-I5 - MIGR: no Entitlements in M12 TEST,Automation test,2024-08-12 17:13:00,2024-08-12 17:40:00,NO,"Event SPLF00006601 

For migration tests I checked above event and saw that CA Entitlement Notifications were sent (in M10) and it is also seen in M12 TEST but no entitlements are generated in M12 TEST. Neither Client nor Market Entitlements. 

For other events they have been generated (e.g. event DVCA00097605). Can you explain why sometimes they are generated and sometimes not? What is the logic behind that? 

Thanks, stefan 


[This ticket was automatically created]",event splf migration test checked event saw ca entitlement notification sent also seen test entitlement generated test neither client market entitlement event generated eg event dvca explain sometimes generated sometimes logic behind thanks stefan ticket automatically created,-0.014144942164421082,0.0,293,0.*****************,0.253536***********,1.***************,0.2,0.13,0.06,46.53%
Incident,OEKB-6202,Major,M12-I2: Menu Item - List Client Payment - MarketClaim,Automation test,2024-08-12 16:43:00,2024-08-12 16:51:00,YES,"Hello,  

When I make a query in the menu item ""List Client Payment"" => Client Movement = Origin = MarketClaim I have noticed the following things: 

Market claims should ideally be coloured orange (OTC transactions) and blue (vienna stock exchange transactions) as in M10. 

The following columns are also missing: 

AccountSvcrTxID (SEME Ref.)  

Client Reference 

Trade Date 

  

Please check! 

  

Thank you and BR, Dalibor 


[This ticket was automatically created]",hello make query menu item list client payment client movement origin marketclaim noticed following thing market claim ideally coloured orange otc transaction blue vienna stock exchange transaction following column also missing accountsvcrtxid seme ref client reference trade date please check thank br dalibor ticket automatically created,-0.*****************,0.0,294,0.007446583070924338,0.****************,1.***************,0.2,0.13,0.15,59.89%
Incident,OEKB-6201,Blocker,M12-I2: DVCA0000002085 - Reconciliation Mismatch with MCP,Automation test,2024-08-12 15:45:00,2024-08-26 11:19:00,YES,"DVCA0000002085 

Hello,  

For above Event  DVCA0000002085 no Client Payment has been created.  

I see that there is a ""Reconciliation Mismatch with Market Cash Payment"" dashboard alert. 

There is a cent difference of EUR 0.04 between collection letter and the market entitlement. 

In MegaCor there is normally a tolerance limit of 0.30 EUR but i am not sure. 

Can you please check this case and set the tolerance limit as in M10 (if that is the problem here)? 

  

Thank you and BR, Dalibor 

  


[This ticket was automatically created]",dvca hello event dvca client payment created see reconciliation mismatch market cash payment dashboard alert cent difference eur collection letter market entitlement megacor normally tolerance limit eur sure please check case set tolerance limit problem thank br dalibor ticket automatically created,-0.02128135785460472,13.0,294,0.007446583070924338,0.2553203394636512,1.***************,0.2,0.14,0.15,61.8%
Incident,OEKB-6200,Critical,M12-I5: ODLT0000002155 - MT566 for Option 001 SLLE contains incorrect :19B::GRSS and :19B::TAXR,Automation test,2024-08-12 14:13:00,2024-08-26 14:08:00,NO,"ODLT0000002155 
was created via Super Event VOLU with several options without any tax movement. 
Instructions were received for Option 001 SLLE, Client Entitlements have been calculated/updated accordingly => 

!image-2024-08-12-14-03-54-460.png|thumbnail! 

However, when checking MT566 OUT, I noticed that 
:19B::GRSS and :19B::TAXR tags have been populated for unknown reasons => 

!image-2024-08-12-14-00-48-870.png|thumbnail! 

Could you please check! 
Thanks, Bert 


[This ticket was automatically created]",odlt created via super event volu several option without tax movement instruction received option slle client entitlement calculatedupdated accordingly imagepngthumbnail however checking mt noticed bgrss btaxr tag populated unknown reason imagepngthumbnail could please check thanks bert ticket automatically created,0.009281227365136147,13.0,294,0.007446583070924338,0.24767969315871596,1.***************,0.2,0.15,0.15,62.15%
Incident,OEKB-6199,Major,M12-ASOC: free text is cut and not imported completely,Stefan RIBISCH,2024-08-12 13:04:00,2024-08-21 15:38:00,NO,"Event TEND0000002090 

For this event, a long free text exists which is correctly displayed in MegaCor and therefore the clients received two MT568 (see attached SWIFTs). 

Unfortunately in ASOC this text is cut: 

!image-2024-08-05-12-32-16-540.png! 

Here you find already the feedback from OEKB IT. 

Please check what is the problem - thanks! 

BR, stefan 

  

==================================================== 

This is exactly what we get back from Webservice: 

Json-Object at.oekb.casa.swcodegen.megacor.model.EventDetailSearchRequest; {""quantityType"":""UNIT"",""mainReference"":[ 

{""value"":""TEND0000002090""} 

],""secAccount"":""227200""} ================ 

Json-Object java.util.ArrayList; [{""Options"":[{""optionNumber"":""001"",""clientDeadline"":""2024-08-22T18:00:00.000+02:00"",""instructedQuantity"":0,""SecurityMovements"":[ 

{""impactType"":""Out"",""Entitlements"":[],""ongo"":true,""financialInstrumentDesc"":""ADDIKO BK AKT O.N."",""financialInstrument"":""AT000ADDIKO0"",""code"":""213956""} 

,{""impactType"":""in"",""Entitlements"":[],""parityIn"":""2"",""ongo"":true,""roundingIn"":""actual"",""parityOut"":""1"",""paymentDate"":""2024-08-30T02:00:00.000+02:00"",""mouvementCashFraction"":[ 

{""impactType"":""In"",""quotationMode"":""Amount"",""code"":""213955""} 

],""mouvementCashTax"":[],""financialInstrumentDesc"":""BAWAG GROUP AKT O.N."",""financialInstrument"":""AT0000BAWAG2"",""code"":""213957""}],""optionNarrative"":"""",""marketDeadline"":""2024-08-24T01:00:00.000+02:00"",""optionType"":""Security Option"",""electionEnd"":""2024-08-26T02:00:00.000+02:00"",""CashMovements"":[],""electionBegin"":""2024-08-05T02:00:00.000+02:00"",""isDefault"":false,""blockedQuantity"":0,""narrativeNeeded"":false,""code"":""213958""},\{""optionNumber"":""002"",""clientDeadline"":""2024-08-30T12:00:00.000+02:00"",""instructedQuantity"":501,""SecurityMovements"":[],""optionNarrative"":"""",""marketDeadline"":""2024-08-30T19:00:00.000+02:00"",""optionType"":""No Action"",""electionEnd"":""2024-08-30T02:00:00.000+02:00"",""CashMovements"":[],""electionBegin"":""2024-07-22T02:00:00.000+02:00"",""isDefault"":true,""blockedQuantity"":0,""narrativeNeeded"":false,""code"":""213954""}],""eventType"":""voluntary"",""status"":""Activated"",""client"":""227200"",""quantityType"":""unit"",""caStatus"":""Receiving Instruction"",""updateDate"":""2024-08-07T02:00:00.000+02:00"",""eventName"":""Tender Offer"",""referenceDate"":""2024-08-30T02:00:00.000+02:00"",""eligibleQuantity"":8349969,""eventDetailComment"":""https://www.addiko.com/takeover-offer/ | Agri Europe Cyprus Limited"",""announcementDate"":""2024-08-05T02:00:00.000+02:00"",""guaranteedParticipationDate"":""2024-08-26T02:00:00.000+02:00"",""electionCounterpartyDeadline"":""2024-08-20T01:00:00.000+02:00"",""mainReference"":""TEND0000002090"",""secAccount"":""227200"",""coaf"":""TEND0000002557"","" 

{*}narrative"":""{*}+++ Additional Information +++ \nVOLUNTARY PARTIAL TENDER OFFER pursuant to Sections 4 et seqq of the Austrian Takeover Act (\""ATA\"") (\""Offer\"") made by Agri Europe Cyprus Limited THE OVAL,\nFlat/Office 502, Krinou 3, 4103 Agios Athanasios, Limassol, Cyprus (commercial registry number HE 283435) (\""Bidder\"") to the shareholders of Addiko Bank AG (\""Target Company\"").\n+++ Documentation {+}{{+}}{{+}}\nThe present partial public tender offer will be published on 16/05/2024 on the website of the Bidder ([https://agrieurope.com.cy/]), the Target Company (www.addiko.com) as well\nas the Austrian Takeover Commission (www.takeover.at). In addition, the Offer will be available in the form of a brochure at the registered office of the Target Company as well\nas at the Paying Agent. A notice pursuant to Sec 11 para 1a ATA will be published in the Elektronische Verlautbarungs- und Informationsplattform des Bundes (EVI - [https://www.evi.gv.at/]) on 16/05/2024.\n{{+}}{{+}}{+} Offer {+}{{+}}{{+}}\nThe Offer comprises the acquisition of up to 3.315.344 of no-par value bearer shares (corresponding to approximately 17,002 percent of the Share Capital) of the Target Company (ISIN AT000ADDIKO0)\nwhich are admitted to trading on the Vienna Stock Exchange (Prime Market) and which are not owned by the Bidder, any party acting in concert with the Bidder or the Target Company.\nAt the time of publication of this Offer Document, the Bidder holds 1.947.901 Shares, corresponding to approximately 9,99 percent of the Share Capital.\nAt no time the Bidder shall by virtue of acceptances of this Offer exceed 29,99 percent of the voting rights in the Target Company.\n{{+}}{{+}}{+} Offer Price {+}{{+}}{{+}}\nEUR 20,24 (gross) per no-par value bearer share of the Target Company cum dividend.\n{{+}}{{+}}{+} Acceptance Period {+}{{+}}{{+}}\nPeriod from (and including) 16/05/2024 to 27/06/2024, 17:00 local Vienna time, being six (6) weeks (\""Acceptance Period\"").\n{{+}}{{+}}{+} Allocation in the event of oversubscription {+}{{+}}{{+}}\nIf declarations of acceptance are submitted for more Shares than the Offer Shares, the declarations of acceptance shall be taken into account proportionately.\nIn such a case, pursuant to Sec 20 ATA, the declaration of acceptance of each shareholder shall be taken into account in proportion to the number of Offer Shares to the total number of Shares\nin respect of which declarations of acceptance have been received. If this allocation rule results in an obligation to accept fractions of Shares, the number of Shares will be rounded down to the next integral number.\n{{+}}{{+}}{+} Conditions Precedent {+}{{+}}{{+}}\nThe Offer is subject to a set of conditions precedent. According to the Offer Document\n(i) the Conditions set forth in Clauses 5.5 and 5.9, must be fulfilled until the end of the Acceptance Period,\n(ii) the banking regulatory clearance pursuant to Clause 5.1 must be fulfilled no later than on 17/02/2025,\n(iii) the banking regulatory clearance in CSEE pursuant to Clause 5.2 must be fulfilled no later than on 17/02/2025,\n(iv) the FDI Clearance Slovenia pursuant to Clause 5.3 must be obtained no later than on 17/02/2025 and\n(v) Clause 5.4 must be fulfilled prior to the Settlement Date.\n{{+}}{{+}}{+} Acceptance +++\nThe acceptance of this Offer must be declared exclusively to the Depository Bank of the respective Shareholder.\nThe acceptance of the Offer becomes effective upon receipt of the Declaration of Acceptance by the Depository Bank and is declared in due time if\n(a) the Declaration of Acceptance is received by the Depository Bank of the respective Shareholder within the Acceptance Period,\n(b) the Depository Bank of the respective Shareholder in turn declares acceptance of the Offer, stating the number of customer orders placed and the total number of Shares\nof those Declarations of Acceptance, received by the Depository Bank during the Acceptance Period and the total number of Shares submitted to it, to OeKB CSD via the custody\nchain and the corresponding total number of Shares was transferred to the Paying Agent, stating the corresponding total number of Shares {*}and the""{*}, 

""electionEnd"":""2024-08-30T02:00:00.000+02:00"",""remainingQuantity"":8349969,""entitledSecurity"":""AT000ADDIKO0"",""entitledSecurityDesc"":""ADDIKO BK AKT O.N."",""code"":""213959""}] ================ 

 ==============================",event tend event long free text exists correctly displayed megacor therefore client received two mt see attached swift unfortunately asoc text cut imagepng find already feedback oekb please check problem thanks br stefan exactly get back webservice jsonobject atoekbcasaswcodegenmegacormodeleventdetailsearchrequest quantitytypeunitmainreference valuetend secaccount jsonobject javautilarraylist optionsoptionnumberclientdeadlinetinstructedquantitysecuritymovements impacttypeoutentitlementsongotruefinancialinstrumentdescaddiko bk akt onfinancialinstrumentataddikocode impacttypeinentitlementsparityinongotrueroundinginactualparityoutpaymentdatetmouvementcashfraction impacttypeinquotationmodeamountcode mouvementcashtaxfinancialinstrumentdescbawag group akt onfinancialinstrumentatbawagcodeoptionnarrativemarketdeadlinetoptiontypesecurity optionelectionendtcashmovementselectionbegintisdefaultfalseblockedquantitynarrativeneededfalsecodeoptionnumberclientdeadlinetinstructedquantitysecuritymovementsoptionnarrativemarketdeadlinetoptiontypeno actionelectionendtcashmovementselectionbegintisdefaulttrueblockedquantitynarrativeneededfalsecodeeventtypevoluntarystatusactivatedclientquantitytypeunitcastatusreceiving instructionupdatedateteventnametender offerreferencedateteligiblequantityeventdetailcomment agri europe cyprus limitedannouncementdatetguaranteedparticipationdatetelectioncounterpartydeadlinetmainreferencetendsecaccountcoaftend narrative additional information nvoluntary partial tender offer pursuant section et seqq austrian takeover act ata offer made agri europe cyprus limited ovalnflatoffice krinou agio athanasios limassol cyprus commercial registry number bidder shareholder addiko bank ag target companyn documentation nthe present partial public tender offer published website bidder target company wwwaddikocom wellnas austrian takeover commission wwwtakeoverat addition offer available form brochure registered office target company wellnas paying agent notice pursuant sec para ata published elektronische verlautbarungs und informationsplattform de bundes evi n offer nthe offer comprises acquisition nopar value bearer share corresponding approximately percent share capital target company isin ataddikonwhich admitted trading vienna stock exchange prime market owned bidder party acting concert bidder target companynat time publication offer document bidder hold share corresponding approximately percent share capitalnat time bidder shall virtue acceptance offer exceed percent voting right target companyn offer price neur gross per nopar value bearer share target company cum dividendn acceptance period nperiod including local vienna time six week acceptance periodn allocation event oversubscription nif declaration acceptance submitted share offer share declaration acceptance shall taken account proportionatelynin case pursuant sec ata declaration acceptance shareholder shall taken account proportion number offer share total number sharesnin respect declaration acceptance received allocation rule result obligation accept fraction share number share rounded next integral numbern condition precedent nthe offer subject set condition precedent according offer documentni condition set forth clause must fulfilled end acceptance periodnii banking regulatory clearance pursuant clause must fulfilled later niii banking regulatory clearance csee pursuant clause must fulfilled later niv fdi clearance slovenia pursuant clause must obtained later andnv clause must fulfilled prior settlement daten acceptance nthe acceptance offer must declared exclusively depository bank respective shareholdernthe acceptance offer becomes effective upon receipt declaration acceptance depository bank declared due time ifna declaration acceptance received depository bank respective shareholder within acceptance periodnb depository bank respective shareholder turn declares acceptance offer stating number customer order placed total number sharesnof declaration acceptance received depository bank acceptance period total number share submitted oekb csd via custodynchain corresponding total number share transferred paying agent stating corresponding total number share electionendtremainingquantityentitledsecurityataddikoentitledsecuritydescaddiko bk akt oncode,0.022549808025360107,9.0,294,0.007446583070924338,0.*****************,0.*****************,0.*****************,0.13,0.15,31.78%
Incident,OEKB-6198,Major,M12-I5: EXRI0000002153 - MT564 REPL not generated,Stefan RIBISCH,2024-08-12 13:02:00,2024-08-22 15:32:00,YES,"EXRI0000002153 
I updated the outgoing comment, event was saved and validated successfully today at 10:48 => 

!image-2024-08-12-12-46-14-650.png! 

However, no MT564 REPL messages were generated until now, moreover, I'm also not able to generate REPL manually => 

!image-2024-08-12-12-47-38-500.png! 

Could you please check? 
Thanks, Bert",exri updated outgoing comment event saved validated successfully today imagepng however mt repl message generated moreover im also able generate repl manually imagepng could please check thanks bert,0.04211646877229214,10.0,294,0.007446583070924338,0.23947088280692697,0.*****************,0.*****************,0.13,0.15,31.04%
Incident,OEKB-6197,Major,M12-I5: EXRI0000002153 - MT564 OUT - :22F::OPTF//BOIS populated twice,Automation test,2024-08-12 10:43:00,2024-08-12 15:17:00,YES,"EXRI0000002153 
When checking MT564 OUT, I noticed that :22F::OPTF//BOIS was populated twice both in EXER and OVER option => 

!image-2024-08-12-10-17-36-649.png|thumbnail! 

!image-2024-08-12-10-18-04-346.png|thumbnail! 

Could you please check? 
Thanks, 
Bert 




[This ticket was automatically created]",exri checking mt noticed foptfbois populated twice exer option imagepngthumbnail imagepngthumbnail could please check thanks bert ticket automatically created,0.02709258906543255,0.0,294,0.007446583070924338,0.24322685273364186,1.***************,0.2,0.13,0.15,58.48%
Incident,OEKB-6196,Major,M12-I2: INTR - REPE message was not sent,Automation test,2024-08-09 15:13:00,2024-08-09 16:27:00,YES,"INTR0000002141 

Hello, 

REPE messages are not sent to all clients. Clients 222116, 222189 and 227300 have not received a REPE message.  

There is an breakdown instruction for these clients, but a REPE message should still be sent with the corresponding text. (Direct payment as per your instruction...) 

  

Please check! 

  

Thank you and BR, Dalibor 


[This ticket was automatically created]",intr hello repe message sent client client received repe message breakdown instruction client repe message still sent corresponding text direct payment per instruction please check thank br dalibor ticket automatically created,-0.009025873616337776,0.0,297,0.0070834089290521185,0.*****************,1.***************,0.2,0.13,0.15,59.84%
Incident,OEKB-6195,Major,M12-I2: INTR - List Market Entitlement not correct,Automation test,2024-08-09 15:13:00,2024-08-09 18:13:00,YES,"test with INTR0000002141 

Hello,  

In my opinion, the ""Market Entitlement"" in the above ISIN was not displayed correctly. 

The total holding here is 118.626.000. 

There are 65.901.000 of these in the blocked account. 

I have created a breakdown instruction for 118,604,000.  

This leaves 22,000 for the interest settlement. 

Nevertheless, the sum under ""List market entitlements"" (eligible position) should be 118.626.000 and not 52.725.000. 

  

Please check! 

  

Thank you and BR, Dalibor 


[This ticket was automatically created]",test intr hello opinion market entitlement isin displayed correctly total holding blocked account created breakdown instruction leaf interest settlement nevertheless sum list market entitlement eligible position please check thank br dalibor ticket automatically created,0.0068474821746349335,0.0,297,0.0070834089290521185,0.*****************,1.***************,0.2,0.13,0.15,59.24%
Information Request,OEKB-6194,Major,M12-I2: DVCA Event with FXRate - Client Payment not executed,Automation test,2024-08-09 12:45:00,2024-08-09 13:08:00,NO,"test with DVCA0000001478 

Hello, 

In the event mentioned above, there was a dashboard alert (Client Payment Waiting FX Rate) where you have to enter the FXRate.  
I did this, but nothing happened with the client payment. Client Payment is still in Status ""Waiting FXRate"" 

Please check! 

  

Thank you and BR, Dalibor 


[This ticket was automatically created]",test dvca hello event mentioned dashboard alert client payment waiting fx rate enter fxrate nothing happened client payment client payment still status waiting fxrate please check thank br dalibor ticket automatically created,0.00024716928601264954,0.0,297,0.0070834089290521185,0.24993820767849684,1.***************,0.2,0.13,0.06,45.99%
Incident,OEKB-6193,Critical,M12 QAS ASOC: Internal Error nach 'Create Instruction',Valdes ELIZABEHT,2024-08-08 13:57:00,2024-08-09 09:48:00,NO,"Dear All,  

we are facing an Issue when trying to create an instruction for:  

MainRef: EXRI0000002095 

ISIN: AT0000A3CYS2 

Depot: 223100 

!image-2024-08-08-13-49-39-371.png! 

  

OeKB IT analysis showed that the provided status was not inclueded in the documentation - therefore please advise to which status it shold be mapped in the portal (ASOC).  

The webservice response incluedes the status ""PartiallyConfirmed"" - which was not received before - that'S why no mapping is in place. 

There is a mapping matrix for the statuses so that they are displayed nicely. e.g: 

CA_STATUS_WaitingPayment=Waiting Payment 
CA_STATUS_PartiallyPaid=Waiting Payment 
CA_STATUS_Paid=Waiting Payment 
CA_STATUS_Instructed=Instructed 
CA_STATUS_Notified=Notified 
CA_STATUS_Executed=Notified 

  

can you please provide all status codes available.  

Thanks & KR,  

Eli",dear facing issue trying create instruction mainref exri isin atacys depot imagepng oekb analysis showed provided status inclueded documentation therefore please advise status shold mapped portal asoc webservice response incluedes status partiallyconfirmed received thats mapping place mapping matrix status displayed nicely eg castatuswaitingpaymentwaiting payment castatuspartiallypaidwaiting payment castatuspaidwaiting payment castatusinstructedinstructed castatusnotifiednotified castatusexecutednotified please provide status code available thanks kr eli,0.04103950038552284,0.0,298,0.006966330477467914,0.2397401249036193,0.*****************,0.012503434490811616,0.15,0.15,32.84%
Incident,OEKB-6191,Cosmetic,M12-I2: DVCA Event with FXRate - Client Payment,Automation test,2024-08-07 16:15:00,2024-08-07 17:37:00,NO,"test with DVCA0000001478 

Client Payment is in Status ""WaitingFXRate"" , which is ok.  
Is it possible to display the ""Client Payment"" in red colour like in M10? 

  

Thank you and BR, Dalibor 


[This ticket was automatically created]",test dvca client payment status waitingfxrate ok possible display client payment red colour like thank br dalibor ticket automatically created,0.017898984253406525,0.0,299,0.006851187162477213,0.*****************,1.***************,0.2,0.1,0.15,54.33%
Information Request,OEKB-6190,Major,M12-I5: PRIO0000002117 - No own fees calculated / booked,Automation test,2024-08-07 12:45:00,2024-08-07 12:57:00,NO,"PRIO0000002117 
Event was set up with Own Fees Flag set to true => 

!image-2024-08-07-12-24-33-073.png|thumbnail! 

Own fee account of client available => 

!image-2024-08-07-12-26-35-102.png|thumbnail! 


However, no own fees have been calculated => 

!image-2024-08-07-12-27-20-877.png|thumbnail! 

Could you please check? 
Thanks, Bert 


[This ticket was automatically created]",prio event set fee flag set true imagepngthumbnail fee account client available imagepngthumbnail however fee calculated imagepngthumbnail could please check thanks bert ticket automatically created,-0.009458182379603386,0.0,299,0.006851187162477213,0.*****************,1.***************,0.2,0.13,0.06,46.35%
Information Request,OEKB-6189,Critical,M12-I5: PRIO0000002121 - MT565 rejected in error!?,Automation test,2024-08-07 12:45:00,2024-08-07 13:05:00,YES,"PRIO0000002121 
ELIG position = 88 
SECU Ratio 1 for 7 
MT565 received (attached) for SECU Option including a 
""Quantity of Securities Instructed"" of 77 (:36B::QINS//UNIT/77,). 

However, instruction was rejected due to Insufficient Position. 
When checking the positions, I found out that the system is considering the instructed quantity (77) as ""Quantity To Be Received"" and the instructed quantity as 539 (77*7) => 

!image-2024-08-07-12-17-31-785.png|thumbnail! 

Could you please check? 
Thanks, Bert 




[This ticket was automatically created]",prio elig position secu ratio mt received attached secu option including quantity security instructed bqinsunit however instruction rejected due insufficient position checking position found system considering instructed quantity quantity received instructed quantity imagepngthumbnail could please check thanks bert ticket automatically created,-0.025582771748304367,0.0,299,0.006851187162477213,0.2563956929370761,1.***************,0.2,0.15,0.06,49.96%
Incident,OEKB-6188,Medium,M12-I5: PRIO0000002121 - Option Comment cannot be viewed in update screen,Automation test,2024-08-07 12:45:00,2024-09-26 18:54:00,NO,"PRIO0000002121 
When updating the event, I found out that the Option Comment (OVER Option) cannot be viewed => 

!image-2024-08-07-12-20-37-842.png|thumbnail! 

Could you please check? 
Thanks, Bert 


[This ticket was automatically created]",prio updating event found option comment option viewed imagepngthumbnail could please check thanks bert ticket automatically created,0.010110391303896904,50.0,299,0.006851187162477213,0.24747240217402577,1.***************,0.2,0.1,0.15,54.62%
Information Request,OEKB-6187,Major,M12-I5: INTR Event - Interest Rate Update issue,Automation test,2024-08-07 12:13:00,2024-08-07 12:24:00,YES,"test with INTR0000002109 

Hello,  

In the INTR Event mentioned above, the interest rate (Annual Interest Rate and Interest Rate for the Period (Price)) was changed manually twice. But if you look at the ""Show History"" => ""Show differences only""  you will only find the change of the ""annual interest rate"". Where do I see the change in the ""Interest rate for the period""? 

Please configure the settings as in the M10 so that you can see the changes. 

  

Thank you and BR, Dalibor 

  


[This ticket was automatically created]",test intr hello intr event mentioned interest rate annual interest rate interest rate period price changed manually twice look show history show difference find change annual interest rate see change interest rate period please configure setting see change thank br dalibor ticket automatically created,0.01346876472234726,0.0,299,0.006851187162477213,0.24663280881941319,1.***************,0.2,0.13,0.06,45.49%
Incident,OEKB-6186,Major,M12-I5: SRD II PROX - VoteExecutionConfirmation Indicator wrong in outgoing seev.004,Automation test,2024-08-06 18:13:00,2024-09-02 15:15:00,NO,"Event PROX0000002076 

two PV CI were received into MegaCor (I attached them for you to this bug too). One was sent with: 

!image-2024-08-06-18-06-50-074.png! 

the other with: 

!image-2024-08-06-18-07-20-919.png! 

  

Unfortunately in the outgoing seev.004 both show the same value in this indicator: 

!image-2024-08-06-18-09-16-228.png! 

!image-2024-08-06-18-08-41-789.png! 

  

Please check that as this indicator has to be forwarded correctly and not always with TRUE. 

Thanks, stefan 


[This ticket was automatically created]",event prox two pv ci received megacor attached bug one sent imagepng imagepng unfortunately outgoing seev show value indicator imagepng imagepng please check indicator forwarded correctly always true thanks stefan ticket automatically created,0.*****************,26.0,299,0.006851187162477213,0.*****************,1.***************,0.2,0.13,0.15,57.12%
Incident,OEKB-6185,Medium,M12-I5: SRD II PROX - logic of seev.006 creation,Automation test,2024-08-06 15:13:00,2024-08-06 15:54:00,YES,"Event PROX0000002073 

We found out that immediately after receiving a PV CI a seev.006 is sent out to the Account Holder with PACK and a second is sent automatically immediately after sending the PV MI to the custodian (see also separate bug OEKB-6175). 

  

Now we sent back seev.006 from the custodian to confirm our seev.004 and noticed the following: 

- neither receiving a seev.006 with PACK nor with other status like RCIS neither with REJT/NORE (Rejected with no specified reason) creates an alert and also no seev.006 to the client. 

- Receiving a seev.006 with PACK changes the status of the PV MI from SentToCustodian to AckedByCustodian. 

- Receiving a seev.006 with RCIS does not change the status of the PV MI from AckedByCustodian to an other value. 

- Receiving a seev.006 with REJT/NORE also does not change the status of the PV MI from SentToCustodian to an other value. 

  

Can you explain if this is the behaviour as it should be? 

  

We at least would have await: 
- an alert if we receive a rejection of our instruction seev.004. 
- a generation of seev.006 to the client if the processing status changed (from PACK to RCIS or from PACK to REJT) 
- cosmetical: the change of the status of the PV MI from SentToCustodian to Rejected (or anything like that) if a rejection has been received. 

  

Please explain the logic of the seev.006 handling in M12. 

Thanks, 
stefan 


[This ticket was automatically created]",event prox found immediately receiving pv ci seev sent account holder pack second sent automatically immediately sending pv mi custodian see also separate bug oekb sent back seev custodian confirm seev noticed following neither receiving seev pack status like rcis neither rejtnore rejected specified reason creates alert also seev client receiving seev pack change status pv mi senttocustodian ackedbycustodian receiving seev rcis change status pv mi ackedbycustodian value receiving seev rejtnore also change status pv mi senttocustodian value explain behaviour least would await alert receive rejection instruction seev generation seev client processing status changed pack rcis pack rejt cosmetical change status pv mi senttocustodian rejected anything like rejection received please explain logic seev handling thanks stefan ticket automatically created,-0.*****************,0.0,300,0.006737946999085467,0.*****************,1.***************,0.2,0.1,0.15,58.18%
Incident,OEKB-6184,Cosmetic,M12-I5: SRD II PROX - Function of the message CANC,Automation test,2024-08-06 14:43:00,,NO,"Event PROX0000002073 

I input a seev.006 from custodian to OEKB CSD, it has been impacted and changed the status of the PV Market Instruction - okay so far. 

But when I open the Meeting Instruction Status I noticed the following: 
!image-2024-08-06-14-30-52-537.png! 

Can you advise why this is CANC? We assume this means cancellation. Are there any consequences of this Msg Function CANC? 

Thanks, stefan 


[This ticket was automatically created]",event prox input seev custodian oekb csd impacted changed status pv market instruction okay far open meeting instruction status noticed following imagepng advise canc assume mean cancellation consequence msg function canc thanks stefan ticket automatically created,-0.39924317225813866,0.0,300,0.006737946999085467,0.34981079306453466,1.***************,0.2,0.1,0.15,69.97%
Incident,OEKB-6183,Major,M12-I5: CAPG0000002115 - Update Issue,Automation test,2024-08-06 14:43:00,2024-08-14 14:39:00,NO,"Hello, 

I have updated the event mentioned above (Income code), but when checking the update, I noticed that the entitled amount has also been updated.  

Please configure the settings so that only the updates that have been carried out are displayed. In my case, this is only the income code (from 06 to 36). 

  

Thank you and BR, Dalibor 


[This ticket was automatically created]",hello updated event mentioned income code checking update noticed entitled amount also updated please configure setting update carried displayed case income code thank br dalibor ticket automatically created,0.008256018161773682,7.0,300,0.006737946999085467,0.24793599545955658,1.***************,0.2,0.13,0.15,59.19%
Incident,OEKB-6182,Minor,"M12-I5: Errors on ""Notification - Incoming Messages"" screens",Automation test,2024-08-06 14:14:00,2024-10-07 09:28:00,NO,"Please find attached a document with errors on ""Notification - Incoming Messages"" screens 


[This ticket was automatically created]",please find attached document error notification incoming message screen ticket automatically created,-0.26393859274685383,61.0,300,0.006737946999085467,0.31598464818671346,1.***************,0.2,0.07,0.15,60.4%
Incident,OEKB-6181,Major,M12-I5: SRD II PROX - Wrong meeting-time in outgoing seev.006,Automation test,2024-08-06 12:46:00,2024-08-22 16:42:00,NO,"Event PROX0000002073 

Meeting Date/Time in incoming seev.001 was the following: 
!image-2024-08-06-12-34-11-976.png! 

In MegaCor it was mapped accordingly: 
!image-2024-08-06-12-30-35-415.png! 

  

So far - everything correct. 

Unfortunately it is shown as follows in the outgoing seev.006: 

!image-2024-08-06-12-32-27-125.png! 

We had these problems already - please check that his does not happen anymore - in all date/time fields and all seev.messages - thanks. 

BR, stefan 


[This ticket was automatically created]",event prox meeting datetime incoming seev following imagepng megacor mapped accordingly imagepng far everything correct unfortunately shown follows outgoing seev imagepng problem already please check happen anymore datetime field seevmessages thanks br stefan ticket automatically created,-0.0015746057033538818,16.0,300,0.006737946999085467,0.25039365142583847,1.***************,0.2,0.13,0.15,59.56%
Incident,OEKB-6180,Cosmetic,"M12: Cosmetic bug ""Set PROX as reliable""",Automation test,2024-08-06 11:44:00,2024-08-06 12:43:00,NO,"Please adapt also this description as it doesn't fit together: 

!image-2024-08-06-11-41-04-695.png! 

I want to set a PROX event as reliable and the headline says I can crosscheck a disclosure request - please adapt it. 

  

Thanks, 

stefan 


[This ticket was automatically created]",please adapt also description doesnt fit together imagepng want set prox event reliable headline say crosscheck disclosure request please adapt thanks stefan ticket automatically created,0.12423806730657816,0.0,300,0.006737946999085467,0.21894048317335546,1.***************,0.2,0.1,0.15,50.34%
Incident,OEKB-6179,Cosmetic,"M12: Cosmetic bug ""Failed WM Event Interface""",Automation test,2024-08-06 11:44:00,2024-08-06 12:37:00,NO,"Hi, 

please adapt this screen as this cannot be the correct description: 

!image-2024-08-06-11-31-32-284.png! 

  

Thanks, 

stefan 


[This ticket was automatically created]",hi please adapt screen correct description imagepng thanks stefan ticket automatically created,0.07650760654360056,0.0,300,0.006737946999085467,0.23087309836409986,1.***************,0.2,0.1,0.15,52.13%
Incident,OEKB-6178,Medium,M12: ToDoList - error when opening 'Event with incoming comments',Automation test,2024-08-05 16:44:00,2024-08-05 17:29:00,NO,"After clicking on 'Events with incoming comments' under section 'Corporate actions manipulation' the following error occurs: 

!image-2024-08-05-16-25-26-433.png! 

As this error was noticed by random: Can you check if such an error also occurs at other categories (without clicking on every single line)? 

Please check that - thanks. 

BR, stefan 


[This ticket was automatically created]",clicking event incoming comment section corporate action manipulation following error occurs imagepng error noticed random check error also occurs category without clicking every single line please check thanks br stefan ticket automatically created,-0.18699251115322113,0.0,300,0.006737946999085467,0.2967481277883053,1.***************,0.2,0.1,0.15,62.01%
Information Request,OEKB-6177,Medium,M12 (TEST)- Pending Trades Alert,Valdes ELIZABEHT,2024-08-02 15:30:00,2024-08-02 16:32:00,NO,"Dear All,  

during the manuel load of the pendingtrades in M12TEST for the rehearsal I noticed that the email alerts is not specific enough.  

Following Alert was received:  

Event Code : Failed_PendTrades 
Description : The Pending Trades interface could not be processed completly. 

For further details please referr to ""Interfaces > Incoming Interfaces > Failed Pending Trade Interface"". 
Pending Trades Interface Loaded with 28 are not processed 

  

Does that really mean, that 28 rows of this interface file were not processed - and remain in the temporary table? 

KR,  

Eli 

  

 ",dear manuel load pendingtrades mtest rehearsal noticed email alert specific enough following alert received event code failedpendtrades description pending trade interface could processed completly detail please referr interface incoming interface failed pending trade interface pending trade interface loaded processed really mean row interface file processed remain temporary table kr eli,-0.3060624171048403,0.0,304,0.006303396482417283,0.32651560427621007,0.*****************,0.012503434490811616,0.1,0.06,24.85%
Information Request,OEKB-6176,Major,M12-I2: SHPR Event - Creating the event via 3i,Automation test,2024-08-02 14:43:00,2024-08-02 15:09:00,NO,"Hello, 

We created the SHPR event in 3i and expected the SHPR event to be created in M12. 

The Swift message arrived in the M12 but was automatically ignored! 

I have previously tested the same with DVCA Event (DVCA0000002085) and it worked without any problems.  

Please check! 

  

Thank you and BR, Dalibor 

  


[This ticket was automatically created]",hello created shpr event expected shpr event created swift message arrived automatically ignored previously tested dvca event dvca worked without problem please check thank br dalibor ticket automatically created,-0.0041384585201740265,0.0,304,0.006303396482417283,0.****************,1.***************,0.2,0.13,0.06,46.16%
Incident,OEKB-6175,Medium,M12-I5: SRD II PROX - Wrong ProcessingStatus in outgoing seev.006,Automation test,2024-08-02 13:13:00,2024-08-06 10:29:00,NO,"Event PROX0000002073 

For incoming seev.004 the system automatically created a seev.006 with PrcgSts PACK which seems to be okay. 

After sending the seev.004 to custodian a second seev.006 with PrcgSts PACK is sent to the account holder. 

In our opinion, two seev.006 are not necessary but if the system sends out two automatically, then the PrcgSts must be different, e.g. FRWD for the second one. 

Don't you agree? Can you clarify this logic here? 

Thanks, stefan 


[This ticket was automatically created]",event prox incoming seev system automatically created seev prcgsts pack seems okay sending seev custodian second seev prcgsts pack sent account holder opinion two seev necessary system sends two automatically prcgsts must different eg frwd second one dont agree clarify logic thanks stefan ticket automatically created,0.*****************,3.0,304,0.006303396482417283,0.****************,1.***************,0.2,0.1,0.15,52.59%
Incident,OEKB-6174,Medium,M12-I5: SRD II PROX - Wrong ParticipationStatus,Automation test,2024-08-02 12:13:00,2024-08-02 12:18:00,NO,"Event PROX0000002081 

For this event, two participationmethods are available: 

!image-2024-08-02-11-44-13-789.png! 

An incoming seev.004 has been received with following content: 
!image-2024-08-02-11-44-48-762.png! 

unfortunately in the outgoing seev.004 this has changed to the following: 
!image-2024-08-02-11-45-41-463.png! 

Please check why this has not been forwarded and why this even has been used although this is no valid participation method. 

Thanks, stefan 


[This ticket was automatically created]",event prox event two participationmethods available imagepng incoming seev received following content imagepng unfortunately outgoing seev changed following imagepng please check forwarded even used although valid participation method thanks stefan ticket automatically created,0.0068704113364219666,0.0,304,0.006303396482417283,0.2482823971658945,1.***************,0.2,0.1,0.15,54.74%
Information Request,OEKB-6173,Medium,M12-I5: SRD II PROX - Resolution Status 'Withdrawn',Automation test,2024-08-01 16:19:00,2024-08-01 16:32:00,NO,"Event PROX0000002059 

I instructed for resolution point 4.b. with attached seev.004 message. 

Although this resolution number is in Resolution status withdrawn: 
!image-2024-08-01-15-36-44-070.png! 

this message has been accepted: 
!image-2024-08-01-15-37-29-132.png! 

Can you explain why this seev.004 has not been rejected? How should the system react if an instruction is received for a withdrawn resolution point? 

Thanks for your support, 
stefan 


[This ticket was automatically created]",event prox instructed resolution point b attached seev message although resolution number resolution status withdrawn imagepng message accepted imagepng explain seev rejected system react instruction received withdrawn resolution point thanks support stefan ticket automatically created,-0.289397232234478,0.0,305,0.006199210502576996,0.3223493080586195,1.***************,0.2,0.1,0.06,52.35%
Incident,OEKB-6172,Medium,M12-I5: SRD II PROX - Status PossibleRejected,Automation test,2024-08-01 16:19:00,2024-08-01 18:54:00,NO,"Event PROX0000002059 

I sent an instruction seev.004 for the whole amount of 140 shares of client 220500 for three resolution points. This instruction was accepted (in error - see separate bug OEKB-6173). 

Afterwards I sent another seev.004 for the same amount of 140 shares of this client but for different resolution points. These instructions got the following status: 
!image-2024-08-01-15-43-11-985.png! 

In our opinion, the ideal solution would be that MegaCor checks the instructed resolution points and accepts the seev004 if for these points no instruction has been received yet. 

But if such a check does not exist we would await that there is a possibility to process a PossibleRejected instruction. Unfortunately we did not found any dashboard alert nor a menu point to process these instructions. 

Can you clarify this for us? 

Thanks, 
stefan 

[This ticket was automatically created]",event prox sent instruction seev whole amount share client three resolution point instruction accepted error see separate bug oekb afterwards sent another seev amount share client different resolution point instruction got following status imagepng opinion ideal solution would megacor check instructed resolution point accepts seev point instruction received yet check exist would await possibility process possiblerejected instruction unfortunately found dashboard alert menu point process instruction clarify u thanks stefan ticket automatically created,-0.05710246413946152,0.0,305,0.006199210502576996,0.2642756160348654,1.***************,0.2,0.1,0.15,57.14%
Incident,OEKB-6171,Minor,"M12-I5: Errors on ""Notification - Outgoing Messages"" screens",Automation test,2024-08-01 14:13:00,2024-10-07 09:29:00,NO,"Please find attached a document with errors on ""Notification - Outgoing Messages"" screens 


[This ticket was automatically created]",please find attached document error notification outgoing message screen ticket automatically created,-0.4662064500153065,66.0,305,0.006199210502576996,0.3665516125038266,1.***************,0.2,0.07,0.15,67.98%
Information Request,OEKB-6170,Cosmetic,M12-I5: SRD II PROX - wrong fillings in the columns,Automation test,2024-08-01 12:45:00,2024-08-05 11:11:00,NO,"Event PROX0000002059 

I sent the attached instruction to MegaCor and it has been received accordingly into MegaCor. Unfortunately for us it is not clear what the system is mapping in which fields from the SWIFT into the system. 

Here the instruction in MegaCor: 

!image-2024-08-01-12-04-37-002.png! 
* Why is client reference and main reference the same? 
* Shouldn't the ""SEME"" of the message (so the BizMsgIdr) also be visible in one of all these columns? 

  

Please advise the logic - thanks! 

BR, stefan 


[This ticket was automatically created]",event prox sent attached instruction megacor received accordingly megacor unfortunately u clear system mapping field swift system instruction megacor imagepng client reference main reference shouldnt seme message bizmsgidr also visible one column please advise logic thanks br stefan ticket automatically created,0.014753855764865875,3.0,305,0.006199210502576996,0.24631153605878353,1.***************,0.2,0.1,0.06,40.95%
Incident,OEKB-6168,Medium,"M12-I5: SRD II PROX - MC adds ""0"" to Resolution Numbers",Automation test,2024-07-31 16:16:00,2024-08-01 16:07:00,NO,"Event PROX0000002059 

Above event has been created with attached seev.001 and as you can see, MegaCor changed some of the resolution numbers with adding one or two zeros (""0"") at the beginning of the number: 

!image-2024-07-31-16-11-21-598.png! 

  

Please check why this happened - the numbers should not be adapted by MegaCor as they also cannot be adapted afterwards manually anymore. 

Thanks, stefan 


[This ticket was automatically created]",event prox event created attached seev see megacor changed resolution number adding one two zero beginning number imagepng please check happened number adapted megacor also adapted afterwards manually anymore thanks stefan ticket automatically created,-0.0005137063562870026,0.0,306,0.006096746565515638,0.25012842658907175,1.***************,0.2,0.1,0.15,55.02%
Information Request,OEKB-6167,Critical,M12 ECMS: incoming payment from 3i failed in MegaBroker,Automation test,2024-07-31 11:13:00,2024-07-31 11:46:00,YES,"Hello, 

attached payment was sent from 3i to MegaCor but unfortunately the FlowOut Generation of Id 12330043 failed due to following error: 

!image-2024-07-31-10-59-45-402.png! 

  

Please check what is the problem here, as this is no ECMS-specific topic but occured during EMCS-tests with OENB. 

Another case from today (FlowIn ID 12350097) for a DVCA Event worked without problems. 

Thanks for your support. 

BR, stefan 


[This ticket was automatically created]",hello attached payment sent megacor unfortunately flowout generation id failed due following error imagepng please check problem ecmsspecific topic occured emcstests oenb another case today flowin id dvca event worked without problem thanks support br stefan ticket automatically created,-0.07470601797103882,0.0,306,0.006096746565515638,0.2686765044927597,1.***************,0.2,0.15,0.06,51.8%
Incident,OEKB-6166,Major,M12-I5: RHDI0000001988 - MT566 to 3i failed,Automation test,2024-07-31 10:43:00,2024-08-02 15:42:00,NO,"RHDI0000001988 
MT566 confirmations were sent to clients successfully. 
However, MT566 to 3i failed in MegaBroker => 

!image-2024-07-31-10-31-03-301.png|thumbnail! 

Could you please check? 
Thanks, Bert 


[This ticket was automatically created]",rhdi mt confirmation sent client successfully however mt failed megabroker imagepngthumbnail could please check thanks bert ticket automatically created,0.10738205444067717,2.0,306,0.006096746565515638,0.2231544863898307,1.***************,0.2,0.13,0.15,55.47%
Incident,OEKB-6165,Major,M12-I5: INTR0000000450 incl. CLAIM Testing - CA Status PartiallyConfirmed,Automation test,2024-07-30 14:43:00,2024-08-26 14:39:00,NO,"test with INTR0000000450 - AT0000A1FAM2 

Hello, 

I have created two trades for the above ISIN, which have resulted in two claims. 

However, I have marked the instruction with ‘Party Hold’ and therefore the instruction has the status ‘Pending’. 

In M12 the status is ""WaitingSettlementConfirmation"". 

The MT566 confirmation for the regular interest payment has already been sent but the event is now in status ‘Partially Confirmed’ probably because the claims have not been settled and confirmed. 

This is a problem for us, as the ‘PartyHold’ can remain in place and the event always remains in the ‘PartiallyConfirmed’ status. 

As a result, the event will probably never be automatically closed. 

The event should change to the corresponding event status as in M10 regardless of the status of the claims, in my case ""Confirmed"". 

  

Please check and BR, Dalibor 

  

  


[This ticket was automatically created]",test intr atafam hello created two trade isin resulted two claim however marked instruction party hold therefore instruction status pending status waitingsettlementconfirmation mt confirmation regular interest payment already sent event status partially confirmed probably claim settled confirmed problem u partyhold remain place event always remains partiallyconfirmed status result event probably never automatically closed event change corresponding event status regardless status claim case confirmed please check br dalibor ticket automatically created,-0.023052070289850235,26.0,307,0.0059959762083696185,0.25576301757246256,1.***************,0.2,0.13,0.15,60.36%
Incident,OEKB-6164,Minor,"M12-I5: Errors on ""TODO – Shareholder Disclosure Request - Failed received seev"" screens",Automation test,2024-07-30 13:13:00,2024-07-30 15:55:00,NO,"Please find some display errors for the screens ""TODO – Shareholder Disclosure Request – Failed received message"": 
# Failed Received seev.046 
# Failed Received seev.049 
# Failed Received seev.045 

1) Please align the columns in all mentioned screens 
# Message Reference  
# Msg Function 
# Main Reference 
# Entitled ISIN / ID 
# Sender Address 
# Client Sec Account 
# CA Code 
# CA Type 
# Record Date 
# Notification Status 
# Error Description 
# Update Date 
# Update User Id 

Note: Receiver Address is not required, but Sender Address 


[This ticket was automatically created]",please find display error screen todo shareholder disclosure request failed received message failed received seev failed received seev failed received seev please align column mentioned screen message reference msg function main reference entitled isin id sender address client sec account ca code ca type record date notification status error description update date update user id note receiver address required sender address ticket automatically created,-0.****************,0.0,307,0.0059959762083696185,0.****************,1.***************,0.2,0.07,0.15,63.66%
Incident,OEKB-6163,Critical,M12-ASOC: wrong securities account is selected,Automation test,2024-07-29 11:43:00,2024-07-31 08:27:00,NO,"Hello, 

I wanted to open the folowing event in ASOC, but the wrong securities account is shown: 

!image-2024-07-23-12-03-09-787 (1).png! 

and after click on DETAIL the event will actually be opened for the wrong securities account: 
!image-2024-07-23-12-03-32-340 (1).png! 

OEKB IT analysed this already and received the following answer from MegaCor: 

  

_================_ 
_2024-07-29 09:52:16,644 [https-jsse-nio-8175-exec-1] INFO  at.oekb.casa.model.webservices.RestServiceProvider - Json-Object at.oekb.casa.swcodegen.megacor.model.EventSearchParams;_ 

_{""status"":[\\{""value"":""Activated""}_ 

_,\{""value"":""ActivatedBlockedNonMarketData""},\{""value"":""CrossCheckedBlockedNotCritical""},\{""value"":""ActivatedBlockedCritical""},\{""value"":""CrossCheckedBlockedCritical""},\{""value"":""ActivatedBlockedNotCritical""},\{""value"":""CrossChecked""}],""pageSize"":100000,{*}{color:#ff0000}""client"":[\\{""value"":""222100""}]{color}{*},""pageNumber"":1,""referenceDateFrom"":""2024-07-01T00:00:00.000+02:00"",""referenceDateTo"":""2024-09-06T00:00:00.000+02:00"",""mainReference"":""TEND0000001933""}_ 
_================_ 
_2024-07-29 09:52:17,270 [https-jsse-nio-8175-exec-1] INFO  at.oekb.casa.model.webservices.RestServiceProvider - Json-Object java.util.ArrayList;_ 
_[\{""eventType"":""Voluntary"",""status"":""Activated"",{*}{color:#ff0000}""client"":""222100""{color}{*},""quantityType"":""UNIT"",""caStatus"":""Waiting Instruction"",""updateDate"":""2024-07-23T02:00:00.000+02:00"",""eventName"":""Tender Offer"",""referenceDate"":""2024-08-30T02:00:00.000+02:00"",""mainReference"":""TEND0000001933"",""remainingQuantity"":********,\{*}{color:#ff0000}""securityAccount"":""279300""{color}{*},""eligibleQuanity"":4309,""entitledSecurity"":""AT000ADDIKO0"",""entitledSecurityDesc"":""ADDIKO BK AKT O.N."",""code"":""296581""}]_ 
_================_ 

  

Please check what is the problem here - thanks! 

stefan 


[This ticket was automatically created]",hello wanted open folowing event asoc wrong security account shown image png click detail event actually opened wrong security account image png oekb analysed already received following answer megacor info atoekbcasamodelwebservicesrestserviceprovider jsonobject atoekbcasaswcodegenmegacormodeleventsearchparams statusvalueactivated valueactivatedblockednonmarketdatavaluecrosscheckedblockednotcriticalvalueactivatedblockedcriticalvaluecrosscheckedblockedcriticalvalueactivatedblockednotcriticalvaluecrosscheckedpagesizecolorffclientvaluecolorpagenumberreferencedatefromtreferencedatetotmainreferencetend info atoekbcasamodelwebservicesrestserviceprovider jsonobject javautilarraylist eventtypevoluntarystatusactivatedcolorffclientcolorquantitytypeunitcastatuswaiting instructionupdatedateteventnametender offerreferencedatetmainreferencetendremainingquantitycolorffsecurityaccountcoloreligiblequanityentitledsecurityataddikoentitledsecuritydescaddiko bk akt oncode please check problem thanks stefan ticket automatically created,-0.****************,1.0,308,0.005896871438725093,0.****************,1.***************,0.2,0.15,0.15,67.99%
Incident,OEKB-6160,Major,M12-I5: RHDI0000001988 - MT564 REPE sent for Cancelled Entitlements,Automation test,2024-07-26 11:13:00,2024-08-05 11:37:00,YES,"RHDI0000001988 
I cancelled a lot of Client Entitlements and recalculated Client Entitlements for only 7 clients => 

!image-2024-07-26-10-35-02-129.png|thumbnail! 

However, when checking MT564 REPE, I noticed that messages have been sent for all clients, even if their entitlements have been cancelled. The REPE for the cancelled entitlements did not show any :36B::ENTL// balance => 

!image-2024-07-26-10-40-10-051.png|thumbnail! 

which is ""understandable"", however, there should be, of course, no MT564 REPE at all for cancelled entitlements. 

Could you please check! 
Thanks, Bert 


[This ticket was automatically created]",rhdi cancelled lot client entitlement recalculated client entitlement client imagepngthumbnail however checking mt repe noticed message sent client even entitlement cancelled repe cancelled entitlement show bentl balance imagepngthumbnail understandable however course mt repe cancelled entitlement could please check thanks bert ticket automatically created,-0.019419454038143158,10.0,311,0.005609277625013169,0.2548548635095358,1.***************,0.2,0.13,0.15,60.23%
Incident,OEKB-6159,Medium,M12-I5: EXRI0000001989 - MT564 includes PWAL in SLLE and BUYA option,Automation test,2024-07-26 11:13:00,2024-07-26 15:31:00,NO,"EXRI0000001989 
BUYA and SLLE were set up without adding an Election Period. 
However, I noticed that PWAL is populated in the MT564 => 

!image-2024-07-26-10-58-26-466.png|thumbnail! 

Is this value set by the system automatically for technical reasons? 
Normally, BUYA and SLLE only include the Trading Period. 

Could you please check? 
Thanks, Bert 



[This ticket was automatically created]",exri buya slle set without adding election period however noticed pwal populated mt imagepngthumbnail value set system automatically technical reason normally buya slle include trading period could please check thanks bert ticket automatically created,0.0003496930003166199,0.0,311,0.005609277625013169,0.24991257674992085,1.***************,0.2,0.1,0.15,54.99%
Information Request,OEKB-6158,Major,"M12-I5: RHDI0000001988 - Wrong CA Status ""Confirmed""",Automation test,2024-07-26 10:43:00,2024-07-26 16:46:00,NO,"RHDI0000001988 
I calculated and cancelled Client Entitlements. 
After that, I noticed that the CA Status is ""Confirmed"" => 

!image-2024-07-26-10-23-45-725.png|thumbnail! 

Could you please check? 
Thanks, Bert 


[This ticket was automatically created]",rhdi calculated cancelled client entitlement noticed ca status confirmed imagepngthumbnail could please check thanks bert ticket automatically created,0.004396047443151474,0.0,311,0.005609277625013169,0.24890098813921213,1.***************,0.2,0.13,0.06,45.84%
Incident,OEKB-6157,Critical,M12: Quantities not correct in ASOC QAS,Valdes ELIZABEHT,2024-07-26 09:27:00,2024-07-30 13:28:00,NO,"Dear All,  

as already mentioned we are facing problems in ASOC due to incorrect quantities:  

Issue in ASOC: RHDI0000001731 

Client: 223100 

Elig Quantity is the Market  !https://jira.oekb.co.at/images/icons/emoticons/warning.png|width=16,height=16!  Elig Position 

Remaining Quantity is the correct Client Elig Position 

  

Please see smy creenshot below:  

!image-2024-07-26-09-25-19-879.png!",dear already mentioned facing problem asoc due incorrect quantity issue asoc rhdi client elig quantity market elig position remaining quantity correct client elig position please see smy creenshot imagepng,-0.4387679658830166,4.0,311,0.005609277625013169,0.35969199147075415,0.*****************,0.012503434490811616,0.15,0.15,50.83%
Incident,OEKB-6155,Major,M12-I5: Manual creation of PRED Event,Automation test,2024-07-25 14:43:00,2024-07-25 16:46:00,YES,"TEST with AT0000A10QD4 

Hello,  

if i try to create an PRED Event manually i would like to fill the Current Pool Factor rate and Next Factor Rate. 

I have noticed that you can enter in M12 more decimal places than in M10. 

In M10 there is a maximum of 10 decimal places for PRED Event. 

Please make the same setting here as in M10 

  

Thank you and BR, Dalibor 


[This ticket was automatically created]",test ataqd hello try create pred event manually would like fill current pool factor rate next factor rate noticed enter decimal place maximum decimal place pred event please make setting thank br dalibor ticket automatically created,0.0012463107705116272,0.0,312,0.0055165644207607716,0.2496884223073721,1.***************,0.2,0.13,0.15,59.45%
Incident,OEKB-6154,Medium,M12-I5: MT566 In/Seev.036/Seev.037 - Menu layout,Automation test,2024-07-23 17:13:00,2024-07-23 17:39:00,NO,"Hello,  

please make in Menu Item Notifications => Incoming Messages out => MT566 In,  the same settings as for Incoming Messages => MT564 In. 

  

Thank you and BR, Dalibor 

  


[This ticket was automatically created]",hello please make menu item notification incoming message mt setting incoming message mt thank br dalibor ticket automatically created,0.023791417479515076,0.0,313,0.005425383630986206,0.24405214563012123,1.***************,0.2,0.1,0.15,54.11%
Information Request,OEKB-6153,Major,M12-I5 - MIGR: No payments in M12,Automation test,2024-07-23 16:43:00,2024-07-23 16:49:00,NO,"Event DVCA00098317 (just an example - seems to be for all events the same) 

Event has been migrated, including notifications and entitlements, but no payments are found. 

Event in M10 PROD: 
!image-2024-07-23-16-27-05-999.png! 
!image-2024-07-23-16-27-29-136.png! 

No payments in M12 test: 
!image-2024-07-23-16-29-22-474.png!!image-2024-07-23-16-30-05-435.png! 

And also no results if I search via Payments-Menu: 
!image-2024-07-23-16-30-32-621.png! 
!image-2024-07-23-16-30-50-540.png! 

Please check and explain that - as without payments we cannot do a reversal, claims and so on. 

Update: After discussing with Oussema on the phone: When we create the payments manually in exceptional cases, how is the reversal processed? If we book them, they are booked twice. And if we not book the new manual created payments, they cannot be reversed, right? How is it done? 

Thanks, 
stefan 

[This ticket was automatically created]",event dvca example seems event event migrated including notification entitlement payment found event prod imagepng imagepng payment test imagepngimagepng also result search via paymentsmenu imagepng imagepng please check explain without payment reversal claim update discussing oussema phone create payment manually exceptional case reversal processed book booked twice book new manual created payment reversed right done thanks stefan ticket automatically created,-0.040231503546237946,0.0,314,0.005335709927106009,0.****************,1.***************,0.2,0.13,0.06,47.51%
Information Request,OEKB-6152,Major,M12-I5 - MIGR: Notifications not to open in M12,Automation test,2024-07-23 16:17:00,2024-07-23 16:25:00,NO,"Event DVCA00092915 was migrated into M12 TEST environment, unfortunately notifications are not migrated without error. I noticed two things: 

1) The Entitled ISIN, Msg Function, Client Sec Account and CA Type are not filled in in the List of SENT MT564: 
!image-2024-07-23-15-39-56-467.png! 

Furthermore the following error occurs if I click on one entry: 
!image-2024-07-23-15-45-35-589.png! 

After rightclick the same error occurs. 

If I open the 'List Notification' screen I can view a notification, but there is also no message to find: 
!image-2024-07-23-15-46-49-480.png! 

  

Please check what happened here or explain the behaviour. 

Thanks, stefan 


[This ticket was automatically created]",event dvca migrated test environment unfortunately notification migrated without error noticed two thing entitled isin msg function client sec account ca type filled list sent mt imagepng furthermore following error occurs click one entry imagepng rightclick error occurs open list notification screen view notification also message find imagepng please check happened explain behaviour thanks stefan ticket automatically created,-0.*****************,0.0,314,0.005335709927106009,0.*****************,1.***************,0.2,0.13,0.06,50.34%
Requirement,OEKB-6151,Medium,"M12: New alerts for ""Instruction Settlement Status""",Stefan RIBISCH,2024-07-23 15:07:00,,NO,"As written with Oussema we need alerts if payments are in a waiting status. 

Please implement Oussemas suggestion as follows: 

  

""In M10 we have an intermediate class for t2 booking called settlement instructions. This class is no longer available in M12 and t2s booking   will always be reflected in the payment status and then these alert cannot be added in the same way as in M10 . 

I suggest to consolidate these  alerts into two alerts : 

*First alert displaying all payments blcoked  with status Waiting Settlement Confirmation : (will include Lack of cash / lack securities and Late settlement) 

*Second alert for Payment with status BookingRejected"" 

  

Thanks, stefan",written oussema need alert payment waiting status please implement oussemas suggestion follows intermediate class booking called settlement instruction class longer available t booking always reflected payment status alert added way suggest consolidate alert two alert first alert displaying payment blcoked status waiting settlement confirmation include lack cash lack security late settlement second alert payment status bookingrejected thanks stefan,0.010555107146501541,0.0,314,0.005335709927106009,0.24736122321337461,0.*****************,0.*****************,0.1,0.09,18.73%
Information Request,OEKB-6150,Major,M12-I5: EXRI0000001864 - MT567 CAST - Impact failed,Automation test,2024-07-22 16:23:00,2024-07-22 16:43:00,NO,"EXRI0000001864 
MT565 CANC was sent with Ref. CS00000000000261 (attached). 
MT567 CAST was imported including :20C::RELA//CS00000000000261 (attached), but was not processed => 

!image-2024-07-22-16-03-00-653.png|thumbnail! 

although the Linked Msg Ref. is the same as in the MT565 => 

!image-2024-07-22-16-04-04-557.png|thumbnail! 


Could you please check why the impact failed? 
Thanks, 
Bert 




[This ticket was automatically created]",exri mt canc sent ref c attached mt cast imported including crelacs attached processed imagepngthumbnail although linked msg ref mt imagepngthumbnail could please check impact failed thanks bert ticket automatically created,0.004089390859007835,0.0,315,0.005247518399181385,0.24897765228524804,1.***************,0.2,0.13,0.06,45.85%
Incident,OEKB-6149,Major,M12-I5: MCAL0000001908 - MT564REPE - Client 236500,Automation test,2024-07-22 15:13:00,2024-07-29 16:14:00,NO,"test with MCAL0000001908 

Hello, 

In the ISIN mentioned above, there is a breakdown Instruction for the total holding (6800000) for client 236500. 

The MT564 REPE message is not correct in my opinion. 

The Line :93B::ELIG shows a balance of 0 instead of 6800000 

The calculated amount and additional text (Direct payment as per your instruction -  

no processing of credit payments by OeKB CSD) are also missing. 

There is also a breakdown instruction for client 227200 but the MT564REPE message there seems to be in order. 

  

Please check! 

  

Thank you and BR, Dalibor 

  

  


[This ticket was automatically created]",test mcal hello isin mentioned breakdown instruction total holding client mt repe message correct opinion line belig show balance instead calculated amount additional text direct payment per instruction processing credit payment oekb csd also missing also breakdown instruction client mtrepe message seems order please check thank br dalibor ticket automatically created,-0.06308134645223618,7.0,315,0.005247518399181385,0.26577033661305904,1.***************,0.2,0.13,0.15,61.87%
Incident,OEKB-6148,Major,M12-I5: MCAL0000001908 - MT566 OUT Customer Reference,Automation test,2024-07-22 15:13:00,2024-08-07 14:01:00,NO,"test with MCAL0000001908 

Hello, 

The MT566 message to the client 227200 contains the customer reference ‘CS0000011277’ in line 20C.  

This is a PFRD Instruction and in my opinion the customer reference of the PFDC instruction (CS0000011275) belongs here. 

For customer 236500, line 20C contains ‘’CS0000011285"" (RFP). In my opinion, CS0000011283 (DFP) should be displayed here. 

  

Please check! 

  

Thank you and BR, Dalibor 

[This ticket was automatically created]",test mcal hello mt message client contains customer reference c line c pfrd instruction opinion customer reference pfdc instruction c belongs customer line c contains c rfp opinion c dfp displayed please check thank br dalibor ticket automatically created,0.0015698540955781937,15.0,315,0.005247518399181385,0.24960753647610545,1.***************,0.2,0.13,0.15,59.44%
Incident,OEKB-6147,Medium,M12 QAS Megara Alerts and Megacor-test Alerts,Nikolay TSONKOV,2024-07-22 10:49:00,2024-07-23 09:10:00,NO,"Hello colleagues,  

since friday 19/07/2024  17:00 we strat recieving M12 Megara Alerts and MegaraAlerts M12  . it seems from QAS and TEST env. at 20:00 we recieved more than 20 also at 22:47 multiple alerts recieved. 

  

From QAS Env we recieved  

M12 Megara Alerts- > e.g  
* Event Code : Failed_Run_Loan_Schedule 
Description : Failed_Run_Loan_Schedule 
Failed calculation of loan schedule for identifier :XS28665079110 for this reason : No loan schedule calculated for ISINs not deposited in OeKB! 

  
* Event Code : CA_FROM_LOANSCHEDULE_GENERATION_FAILURE 
Description : \{0} 
CA From Loan Schedule, Fri Jul 19 17:05:33 CEST 2024 has failed: HTTP 500 Internal Server Error for the Financial Instrument AT0000A1YQC0 

  
* Event Code : Failed_PendTrades 
Description : The Pending Trades interface could not be processed completly. 

For further details please referr to ""Interfaces > Incoming Interfaces > Failed Pending Trade Interface"". 
Pending Trades Interface Loaded with 39 are not processed 

  
* e.g Event Code : Failed_Instrument 
Description : The Securities Referential interface could not be processed completly. 

For further details please refer to ""Interfaces > Incoming Interfaces > Failed Securities Referential Interface"". 
Failed_Instrument 

  

From [<EMAIL>|mailto:<EMAIL>] we recieved e.g  

  
* Event Code : ENTITLEMENT_CALCULATION_FAILURE 
Description : Entitlement Calculation JOB 
Job Client entitlement calculation has failed due to a technical problem. For further details please refer to ""Tools > Quartz Scheduler > Jobs Management > Track Job Execution""/nCalculation Job has failed for AT0000A0QBT4 / INTR00154794 because the position Full Load was not processed correctly. Please check the record date. The entitlement calculation should be executed manually. 

  
* Event Code : Financial_Instruments_Automatic_Deactivation Failure 
Description : There has been a problem with the Financial_Instruments_Automatic_Deactivation Job. Please contact support team 
Exception Date : Mon Jul 22 00:00:06 CEST 2024 Thread ID : 184 
Thread Name : MegaCorScheduler_Worker-1 

  

attached you can find some examples 

are you aware of those Alerts? currently there are no new alerts coming, i did some checks on the interface level in M12 QAS i see no errors. can you pls share your opinion if those alerts should be ommited ? 

  

BR,  

Niki",hello colleague since friday strat recieving megara alert megaraalerts seems qas test env recieved also multiple alert recieved qas env recieved megara alert eg event code failedrunloanschedule description failedrunloanschedule failed calculation loan schedule identifier x reason loan schedule calculated isins deposited oekb event code cafromloanschedulegenerationfailure description ca loan schedule fri jul cest failed http internal server error financial instrument atayqc event code failedpendtrades description pending trade interface could processed completly detail please referr interface incoming interface failed pending trade interface pending trade interface loaded processed eg event code failedinstrument description security referential interface could processed completly detail please refer interface incoming interface failed security referential interface failedinstrument megacortestoekbatmailtomegacortestoekbat recieved eg event code entitlementcalculationfailure description entitlement calculation job job client entitlement calculation failed due technical problem detail please refer tool quartz scheduler job management track job executionncalculation job failed ataqbt intr position full load processed correctly please check record date entitlement calculation executed manually event code financialinstrumentsautomaticdeactivation failure description problem financialinstrumentsautomaticdeactivation job please contact support team exception date mon jul cest thread id thread name megacorschedulerworker attached find example aware alert currently new alert coming check interface level qas see error pls share opinion alert ommited br niki,-0.6912288349121809,0.0,315,0.005247518399181385,0.4228072087280452,0.012479781565630135,0.0023646865936200277,0.1,0.15,51.28%
Incident,OEKB-6146,Critical,M12-I5: EXRI0000001864 - Update of event failed,Automation test,2024-07-22 09:49:00,2024-08-01 09:09:00,NO,"EXRI0000001864 
I again face this strange situation (same behavior as described in the old ticket OEKB-5002): 
I wanted to update the event by (only) editing the outgoing comment => 

!image-2024-07-22-08-57-30-874.png|thumbnail! 

!image-2024-07-22-08-58-14-435.png|thumbnail! 

After clicking the ""Save""-button, a REDM (!!!) event appears => 

!image-2024-07-22-08-54-00-744.png|thumbnail! 

When checking the EXRI event, I see that no update was saved. 

Could you please check! 
Thanks, Bert 





[This ticket was automatically created]",exri face strange situation behavior described old ticket oekb wanted update event editing outgoing comment imagepngthumbnail imagepngthumbnail clicking savebutton redm event appears imagepngthumbnail checking exri event see update saved could please check thanks bert ticket automatically created,-0.39996096305549145,9.0,315,0.005247518399181385,0.34999024076387286,1.***************,0.2,0.15,0.15,77.5%
Incident,OEKB-6145,Critical,M12-I5: DRIP0000001910 - Market Entitlements CASH do not include Tax,Automation test,2024-07-19 17:13:00,2024-08-05 13:11:00,NO,"DRIP0000001910 
CASH instruction received. 
Client Entitlements are correct => 

!image-2024-07-19-16-44-04-918.png|thumbnail! 

Market Entitlements are not correct as tax calculation is missing => 

!image-2024-07-19-16-43-05-818.png|thumbnail! 


Could you please check? 
Thanks, Bert 


[This ticket was automatically created]",drip cash instruction received client entitlement correct imagepngthumbnail market entitlement correct tax calculation missing imagepngthumbnail could please check thanks bert ticket automatically created,-0.019252697005867958,16.0,317,0.005075484283263797,0.254813174251467,1.***************,0.2,0.15,0.15,63.22%
Incident,OEKB-6144,Major,M12-I5: DRIP0000001910 - CASH Option - Default Option Flag set to true automatically?!,Automation test,2024-07-19 16:17:00,2024-08-05 12:50:00,NO,"DRIP0000001910 
was set up due to incoming MT564 with two options: 

:13A::CAON//001 
:22F::CAOP//CASH 
:11A::OPTN//EUR 
:17B::DFLT//N 

and 

:13A::CAON//002 
:22F::CAOP//SECU 
:17B::DFLT//Y 


After having updated event data (but not the Default Option flag!!!) and when trying to save, the following error popped up => 

!image-2024-07-19-15-58-32-781.png|thumbnail! 


When viewing the event in a different screen, I see that the CASH option was mapped correctly as Non-Default-Option: 

!image-2024-07-19-16-01-08-040.png|thumbnail! 


So, obviously when updating the CASH option, the system is setting the default flag automatically to true!? 

Could you please check! 
Thanks, Bert 







[This ticket was automatically created]",drip set due incoming mt two option acaon fcaopcash aoptneur bdfltn acaon fcaopsecu bdflty updated event data default option flag trying save following error popped imagepngthumbnail viewing event different screen see cash option mapped correctly nondefaultoption imagepngthumbnail obviously updating cash option system setting default flag automatically true could please check thanks bert ticket automatically created,-0.*****************,16.0,318,0.004991593906910217,0.****************,1.***************,0.2,0.13,0.15,61.44%
Incident,OEKB-6143,Major,Blocked Security Account - Client Payment to be Validated,Automation test,2024-07-19 15:13:00,2024-07-19 15:35:00,YES,"test with PCAL0000001854 

Hello, 

The PCAL event mentioned above has a blocked account (222116).  

However, the Client payment was made and there was no Dashboard Alert.  

  

Please check! 

  

Thank you and BR, Dalibor 

[This ticket was automatically created]",test pcal hello pcal event mentioned blocked account however client payment made dashboard alert please check thank br dalibor ticket automatically created,-0.****************,0.0,318,0.004991593906910217,0.***************,1.***************,0.2,0.13,0.15,62.7%
Information Request,OEKB-6142,Major,M12-I5: EXRI0000001864 - Client Instruction should be autorejected if quantity does not respect MIEX/MILT,Automation test,2024-07-19 10:43:00,2024-07-19 11:19:00,NO,"EXRI0000001864 
is set up with 
MIEX 194806, 
MILT 194806, 

Client instruction was sent with instructed quantity 300 and moved to status ""Invalid Data"" => 

!image-2024-07-19-10-30-04-033.png|thumbnail! 

However, instruction should be automatically rejected. 
Could you please check? 
Thanks, Bert 



[This ticket was automatically created]",exri set miex milt client instruction sent instructed quantity moved status invalid data imagepngthumbnail however instruction automatically rejected could please check thanks bert ticket automatically created,-0.****************,0.0,318,0.004991593906910217,0.2628887128084898,1.***************,0.2,0.13,0.06,47.93%
Incident,OEKB-6141,Major,Menu - Cancel Breakdown Instructions,Automation test,2024-07-18 13:43:00,2024-07-18 14:03:00,NO,"Hello, 

If you look at the menu item ‘Cancel Breakdown Instructions’, I notice that some fields and columns are missing. 

For example, the ‘Breakdown Purpose""’ field is missing. 

The ‘ISIN’ column is also missing and the ‘Reason’ column is not filled.  

Please set the view as in M10. 

  

Thank you and BR, Dalibor 


[This ticket was automatically created]",hello look menu item cancel breakdown instruction notice field column missing example breakdown purpose field missing isin column also missing reason column filled please set view thank br dalibor ticket automatically created,-0.09903185069561005,0.0,319,0.004909090116516119,0.2747579626739025,1.***************,0.2,0.13,0.15,63.21%
Incident,OEKB-6140,Medium,M12-I5: DRIP0000001836 - :19B::TXFR in MT566 should be populated including 2 decimals only,Automation test,2024-07-17 16:18:00,2024-08-05 13:06:00,NO,"DRIP0000001836 
When checking MT566 for CASH option, I noticed that all amounts in several :19B:-tags are populated with 2 decimals only - except :19B::TXFR => 

!image-2024-07-17-16-07-37-600.png|thumbnail! 

In my opinion, also this amount should include only 2 decimals. 

Could you please check? 
Thanks, Bert 


[This ticket was automatically created]",drip checking mt cash option noticed amount several btags populated decimal except btxfr imagepngthumbnail opinion also amount include decimal could please check thanks bert ticket automatically created,0.026699384674429893,18.0,320,0.004827949993831441,0.24332515383139253,1.***************,0.2,0.1,0.15,54.0%
Incident,OEKB-6139,Medium,M12-I5: EXRI0000001785 - MT566 for SLLE/BUYA should include Trading Date and Time,Automation test,2024-07-17 12:13:00,2024-08-07 14:45:00,YES,"EXRI0000001785 
Payments have been processed for BUYA and SLLE option. 
When checking MT566, I noticed that - other than in M10 - no Trading Date and Time is populated => 

!image-2024-07-17-11-43-41-349.png|thumbnail! 

Example from M10 => 

!image-2024-07-17-11-44-14-240.png|thumbnail! 

Could you please check! 
Thanks, Bert 






[This ticket was automatically created]",exri payment processed buya slle option checking mt noticed trading date time populated imagepngthumbnail example imagepngthumbnail could please check thanks bert ticket automatically created,-0.009538419544696808,21.0,320,0.004827949993831441,0.2523846048861742,1.***************,0.2,0.1,0.15,55.36%
Information Request,OEKB-6138,Medium,M12-I5: No client payments generated for BPUT,Automation test,2024-07-17 11:43:00,2024-07-17 12:24:00,NO,"AT000B008073 

BPUT0000001742 

  

Market payment was generated automatically by swift MT566 received from 3i. 

But there is no client payment. 

There should be a client payment for client 205700 for nominal of 1.000 

market payment is not reconciled. 

Can you please check the reason for this. 

Thank you 

  

Gerold 


[This ticket was automatically created]",atb bput market payment generated automatically swift mt received client payment client payment client nominal market payment reconciled please check reason thank gerold ticket automatically created,-0.00565081462264061,0.0,320,0.004827949993831441,0.25141270365566015,1.***************,0.2,0.1,0.06,41.71%
Incident,OEKB-6137,Medium,"M12-I5: To-Do-List: Please add ""CASH"" to ""Market Payment Waiting Value Date""",Automation test,2024-07-17 11:43:00,2024-07-17 12:13:00,NO,"To clearly distinguish between MP Cash and MP Securities, please rename 
""Market Payment Waiting Value Date"" => 

!image-2024-07-17-11-22-14-451.png|thumbnail! 

to ""Market Cash Payment Waiting Value Date"". 

In addition, please move ""Market Security Payment Waiting Value Date"" to the line below ""Market Cash Payment Waiting Value Date"". 

Thanks, Bert 


[This ticket was automatically created]",clearly distinguish mp cash mp security please rename market payment waiting value date imagepngthumbnail market cash payment waiting value date addition please move market security payment waiting value date line market cash payment waiting value date thanks bert ticket automatically created,0.0720757469534874,0.0,320,0.004827949993831441,0.23198106326162815,1.***************,0.2,0.1,0.15,52.3%
Incident,OEKB-6136,Medium,M12: Client Payment Confirmation job was configured wrong,Automation test,2024-07-17 10:13:00,2024-07-17 10:20:00,NO,"As discussed with Oussema, the configuration of the Client Payment Confirmation job was wrong. 

This bug is for documental reasons to not forget it in Go-Live-configuration. 

BR, stefan 


[This ticket was automatically created]",discussed oussema configuration client payment confirmation job wrong bug documental reason forget goliveconfiguration br stefan ticket automatically created,-0.12459798343479633,0.0,320,0.004827949993831441,0.2811494958586991,1.***************,0.2,0.1,0.15,59.67%
Incident,OEKB-6135,Major,M12-I5: DRIP0000001836 - MT564 REPL - Outgoing comment in wrong order,Automation test,2024-07-16 16:18:00,2024-07-26 14:55:00,NO,"DRIP0000001836 
When checking MT564 REPL, I noticed that the outgoing comment in ADTX is not in the correct order (see attached). 

Could you please check! 
Thanks, Bert 




[This ticket was automatically created]",drip checking mt repl noticed outgoing comment adtx correct order see attached could please check thanks bert ticket automatically created,-0.001891162246465683,9.0,321,0.004748150999411478,0.2504727905616164,1.***************,0.2,0.13,0.15,59.57%
Incident,OEKB-6134,Medium,M12-I5: Wrong content on E-Mail-Report,Automation test,2024-07-16 14:13:00,2024-07-17 11:24:00,YES,"Ad-hoc booking for event MRGR0000001833 

concerning the ad-hoc-booking for client acc 220500 the attached e-mail-report is created. 

Unfortunately the following is stated, although no sese.025 exists: 
!image-2024-07-16-13-44-32-967.png! 

As already discussed with Oussema: [^flowOut_11550185.pdf]Please adapt this for cases where no sese.025 exists. 

Thanks, 
stefan 


[This ticket was automatically created]",adhoc booking event mrgr concerning adhocbooking client acc attached emailreport created unfortunately following stated although sese exists imagepng already discussed oussema flowoutpdfplease adapt case sese exists thanks stefan ticket automatically created,0.01568901352584362,0.0,321,0.004748150999411478,0.2460777466185391,1.***************,0.2,0.1,0.15,54.41%
Incident,OEKB-6133,Critical,M12-I5: EXRI0000001785 - MT564 REPL with ELIG balances not generated for all clients,Automation test,2024-07-15 16:17:00,2024-08-06 11:29:00,NO,"EXRI0000001785 
ELIG positions have been received today at 12.11. 
After manual processing of ""Impact Position For Notification"", I noticed that only one instruction for one client (223100) was generated, although there are, in total, 7 clients with ELIG positions => 

!image-2024-07-15-16-09-27-028.png|thumbnail! 

Could you please check? 
Thanks, Bert 


[This ticket was automatically created]",exri elig position received today manual processing impact position notification noticed one instruction one client generated although total client elig position imagepngthumbnail could please check thanks bert ticket automatically created,-0.04983891174197197,21.0,322,0.004669670966355772,0.262459727935493,1.***************,0.2,0.15,0.15,64.37%
Information Request,OEKB-6132,Medium,"""Impact Position For Notification Generation""-Job - please adjust Execution Time",Bertram Schon,2024-07-15 16:16:00,2024-07-26 14:17:00,NO,"Current situation: 
The ""Impact Position For Notification Generation""-job is currently configured to be executed 4 times per day:  

06:30:00:000 

09:40:00:000 

11:30:00:000 

15:30:00:000 

Target behavior: 
As we receive Delta Positions around 12:10, could you please adjust the execution time of the ""Impact Position For Notification Generation 3""-job  
from  


11:30:00:000 
to 
{*}12:30:00:000{*}? 

Thanks, Bert",current situation impact position notification generationjob currently configured executed time per day target behavior receive delta position around could please adjust execution time impact position notification generation job thanks bert,-0.*****************,10.0,322,0.004669670966355772,0.*****************,0.011010145928181883,0.0020862179624902143,0.1,0.06,14.35%
Information Request,OEKB-6131,Medium,M12-I5: SRD II SHDS - no alert for invalid instruction,Automation test,2024-07-15 12:18:00,2024-07-15 13:23:00,NO,"Event SHDS0000001693 

the batchjob created standing instructions on 09/07 already correctly. 

But as there is one instruction in status InvalidData (for account 221751): 
!image-2024-07-15-11-06-23-535.png! 

we would have expected an alert but no alert has been created on Dashboard. 

As discussed with Oussema: Please generate alerts for this topic under the section 'Shareholder Disclosure Request' on To Do List. 

Thanks, 
stefan 


[This ticket was automatically created]",event shds batchjob created standing instruction already correctly one instruction status invaliddata account imagepng would expected alert alert created dashboard discussed oussema please generate alert topic section shareholder disclosure request list thanks stefan ticket automatically created,0.*****************,0.0,322,0.004669670966355772,0.*****************,1.***************,0.2,0.1,0.06,41.19%
Incident,OEKB-6130,Critical,M12-I5: PRIO0000001787 - CASH Market Payment booked incorrectly,Automation test,2024-07-15 10:53:00,2024-07-15 11:42:00,NO,"PRIO0000001787 
MT566 received from CBF including debit confirmation on CASH account 
CATEUROCSDATWWXXX => 

!image-2024-07-15-10-49-07-879.png|thumbnail! 

When checking Market Payment, I noticed that the cash payment was generated incorrectly => 

!image-2024-07-15-10-46-54-028.png|thumbnail! 


Could you please check! 
Thanks, Bert 



[This ticket was automatically created]",prio mt received cbf including debit confirmation cash account cateurocsdatwwxxx imagepngthumbnail checking market payment noticed cash payment generated incorrectly imagepngthumbnail could please check thanks bert ticket automatically created,0.014579305425286293,0.0,322,0.004669670966355772,0.*****************,1.***************,0.2,0.15,0.15,61.95%
Incident,OEKB-6129,Critical,M12-I5: PRIO0000001787 - SECU Entitlements incorrect,Automation test,2024-07-15 10:28:00,2024-07-15 11:02:00,YES,"PRIO0000001787 
SECU Option was set up with ratio 1 for 15. 
Price 8,28. 
Instruction received for 495 shares. 

Entitlements should therefore be: 
SECU IN 33 shares 
CASH OUT 273,24 (33*8,28) 

However, cash entitlements are calculated incorrectly considering the instructed quantity (495) instead of the resulting quantity (33) => 

!image-2024-07-15-10-24-42-304.png|thumbnail! 

Could you please check! 
Thanks, Bert 



[This ticket was automatically created]",prio secu option set ratio price instruction received share entitlement therefore secu share cash however cash entitlement calculated incorrectly considering instructed quantity instead resulting quantity imagepngthumbnail could please check thanks bert ticket automatically created,-0.17214765958487988,0.0,322,0.004669670966355772,0.29303691489621997,1.***************,0.2,0.15,0.15,68.96%
Incident,OEKB-6128,Major,M12-I5: RHDI0000001784 - Some Client Entitlements were cancelled automatically,Automation test,2024-07-15 09:40:00,2024-08-02 12:45:00,NO,"RHDI0000001784 
When checking Client Entitlements, I noticed that some of it, have been cancelled automatically by the system => 

!image-2024-07-15-09-33-43-214.png|thumbnail! 

Please note there has been a similar bug reported in CMTTM-1536 / OEKB-5897. 
Maybe it's a regression issue? 

Could you please check? 
Thanks, Bert 





[This ticket was automatically created]",rhdi checking client entitlement noticed cancelled automatically system imagepngthumbnail please note similar bug reported cmttm oekb maybe regression issue could please check thanks bert ticket automatically created,-0.19547203928232193,18.0,322,0.004669670966355772,0.2988680098205805,1.***************,0.2,0.13,0.15,66.83%
Requirement,OEKB-6127,Medium,PRIO Event OVER option - Enhanced functionality when instructed quantity has to be updated,Bertram Schon,2024-07-12 15:45:00,,NO,"{color:#de350b}+*Current situation:*+{color} 
In case the instructed quantity for OVER has to be updated, due to a proration determined by the company, via the ""Update Quantity for OVER""-functionality, the system is updating the quantity of the Client Instruction and is cancelling the Client Entitlements. No action is taken for Market Instruction/Market Entitlement. 

+*Target behavior:*+ 
In addition of the update of instructed quantity and cancellation of Client Entitlements, also the Market Instruction should be updated with the new (prorated) quantity and Market Entitlements should be cancelled automatically. 

Afterwards, the user has to recalculate both Client and Market Entitlements. 
No MT565 CANC must be sent to the custodian, no MT567 must be sent to the client. 

Please check feasibility! 
Thanks, Bert",colordebcurrent situationcolor case instructed quantity updated due proration determined company via update quantity overfunctionality system updating quantity client instruction cancelling client entitlement action taken market instructionmarket entitlement target behavior addition update instructed quantity cancellation client entitlement also market instruction updated new prorated quantity market entitlement cancelled automatically afterwards user recalculate client market entitlement mt canc must sent custodian mt must sent client please check feasibility thanks bert,-0.05547604709863663,0.0,325,0.004441928425934291,0.26386901177465916,0.011010145928181883,0.0020862179624902143,0.1,0.09,18.39%
Incident,OEKB-6126,Major,"M12-I5: PRIO0000001787 - Client Instruction Status ""PossibleRejected"" incorrect?!",Automation test,2024-07-12 14:33:00,2024-07-12 17:36:00,NO,"PRIO0000001787 
Event set up with Record Date 08/07/2024. 
ELIG Position 222200: 

!image-2024-07-12-14-14-24-844.png|thumbnail! 

Instruction received for 495 shares => Instruction Status ""Created"" => correct 
Instruction received for further 8.790 shares => Instruction Status ""PossibleRejected"" 

In my opinion, the instruction should have been auto-rejected as there is no sufficient Record Date position remaining. 

Could you please check? 
Thanks, Bert 





[This ticket was automatically created]",prio event set record date elig position imagepngthumbnail instruction received share instruction status created correct instruction received share instruction status possiblerejected opinion instruction autorejected sufficient record date position remaining could please check thanks bert ticket automatically created,-0.002238314598798752,0.0,325,0.004441928425934291,0.2505595786496997,1.***************,0.2,0.13,0.15,59.58%
Incident,OEKB-6125,Medium,M12-I5: Incorrect payment date on pdf Payment confirmation,Automation test,2024-07-12 11:33:00,2024-07-12 18:44:00,NO,"I have created INTR event with payment date = 12.07.2024 

AT0000A159V2 

INTR0000001029 

But on pdf payment confirmation for client 236500 the payment date on the first page = 03.07.2024 - it should be 12.07.2024 

On the second page payment date is correct 

Can you please repair.  

  

Thank you 

  

Gerold 

  


[This ticket was automatically created]",created intr event payment date atav intr pdf payment confirmation client payment date first page second page payment date correct please repair thank gerold ticket automatically created,-0.00512426532804966,0.0,325,0.004441928425934291,0.2512810663320124,1.***************,0.2,0.1,0.15,55.19%
Incident,OEKB-6124,Major,M12-I5: No market claims generated,Automation test,2024-07-12 08:24:00,2024-07-12 08:50:00,YES,"I have created INTR event for *UNITS* in M12 

AT0000A2V9P6 
INTR0000001756 

ex-date = 11.07.2024 
record date = 10.07.2024 

I have created 2 trades in CCSYS QAS with trade date 09.07. and intented settlement date 12.07.2024 - see pdf-attached - the trades are matched  

I can find the relevant pending trades in M12 - see screenshot 

But no claims were generated. 

Can you check please the reason for this? 

  

Thank you  

Gerold 

  


[This ticket was automatically created]",created intr event unit atavp intr exdate record date created trade ccsys qas trade date intented settlement date see pdfattached trade matched find relevant pending trade see screenshot claim generated check please reason thank gerold ticket automatically created,0.0042656417936086655,0.0,325,0.004441928425934291,0.24893358955159783,1.***************,0.2,0.13,0.15,59.34%
Information Request,OEKB-6123,Medium,M12-I5: LIQU0000001744 - Error when trying to save (because of NO SECU OUT),Automation test,2024-07-10 16:54:00,2024-07-11 09:18:00,NO,"LIQU0000001744 
was set up without SECU OUT movement (I did not open the SECU OUT screen at all!). 

When trying to save the creation or update of the event, the following error popped up => 
!image-2024-07-10-16-45-44-418.png|thumbnail! 

I found out that I have to remove the SECU OUT movement to proceed, which is not a userfriendly behavior. 

Could you please check? 
Thanks, Bert 


[This ticket was automatically created]",liqu set without secu movement open secu screen trying save creation update event following error popped imagepngthumbnail found remove secu movement proceed userfriendly behavior could please check thanks bert ticket automatically created,-0.11543421447277069,0.0,326,0.004368509806825166,0.2788585536181927,1.***************,0.2,0.1,0.06,45.83%
Incident,OEKB-6121,Major,M12-I5: DVSE0000001733 - Resubmit Rejected SAP Booking - Button missing,Automation test,2024-07-10 15:19:00,2024-07-10 15:24:00,NO,"DVSE0000001733 
Client SAP booking has been rejected. 
When trying to resubmit, I noticed that the relevant button is missing. 

Please add this button! 
Many thanks! 

BR Bert 


[This ticket was automatically created]",dvse client sap booking rejected trying resubmit noticed relevant button missing please add button many thanks br bert ticket automatically created,0.02776140719652176,0.0,327,0.00429630469075234,0.24305964820086956,1.***************,0.2,0.13,0.15,58.46%
Information Request,OEKB-6120,Cosmetic,M12-I5: TEND0000001700 - Client Payments not generated automatically,Automation test,2024-07-10 10:38:00,2024-07-10 11:47:00,NO,"TEND0000001700 
Market Payments are created / reconciled totally. 

!image-2024-07-10-10-35-53-578.png|thumbnail! 

However, no Client Payments have been generated automatically. 

Could you please check? 
Thanks, Bert 


[This ticket was automatically created]",tend market payment created reconciled totally imagepngthumbnail however client payment generated automatically could please check thanks bert ticket automatically created,0.01233694888651371,0.0,327,0.00429630469075234,0.24691576277837157,1.***************,0.2,0.1,0.06,41.04%
Information Request,OEKB-6119,Cosmetic,M12-I5: Interface Operations/Freeze Interface/Receive LACK,Automation test,2024-07-10 09:53:00,2024-07-10 10:11:00,NO,"+++ Cosmetic issue +++ 
Can you please provide us with Check Boxes in this screen => 

!image-2024-07-10-09-51-07-844.png|thumbnail! 


Thanks, Bert 


[This ticket was automatically created]",cosmetic issue please provide u check box screen imagepngthumbnail thanks bert ticket automatically created,0.05001293122768402,0.0,327,0.00429630469075234,0.237496767193079,1.***************,0.2,0.1,0.06,39.62%
Incident,OEKB-6118,Medium,M12: Dashboard refresh,Automation test,2024-07-09 16:13:00,2024-07-09 17:45:00,NO,"General problem in M12: 

If we open the dashboard via this symbol: 

!image-2024-07-09-16-04-35-721.png! 

it takes from a few to 10/20 seconds (it depends) to open the dashboard. 

After choosing a category and clicking on the amount of alerts a new screen opens where these alerts can be processed manually - so far everything okay. 

But if you then go back again to the dashboard it again loads up to 10/20 seconds - this is very laborious and boring. 

Please set the performance as in M10 where the dashboard is open whole the time. 

Further: there is so much space on the home screen and the alerts take so less space where the user has to scroll if a category is opened and has in addition 3 pages too: 

!image-2024-07-09-16-09-46-205.png! 

Couldn't the space on the screen be used more efficient? There is so less information on such big screens. 

The first point with the performance is important for us, the second point would be nice if this can be solved better. 

Thanks, stefan 


[This ticket was automatically created]",general problem open dashboard via symbol imagepng take second depends open dashboard choosing category clicking amount alert new screen open alert processed manually far everything okay go back dashboard load second laborious boring please set performance dashboard open whole time much space home screen alert take less space user scroll category opened addition page imagepng couldnt space screen used efficient less information big screen first point performance important u second point would nice solved better thanks stefan ticket automatically created,0.06429251655936241,0.0,328,0.004225293020274839,0.2339268708601594,1.***************,0.2,0.1,0.15,52.59%
Incident,OEKB-6117,Cosmetic,M12-I5: DVSE0000001718 - Update Security Price For Taxation (CLIENT Side) failed,Automation test,2024-07-09 15:38:00,2024-07-09 17:32:00,NO,"DVSE0000001718 
Security Price was added, but no Client entitlements were calculated => 

!image-2024-07-09-15-12-10-531.png|thumbnail! 

When doing the same for MARKET side, it worked. 

!image-2024-07-09-15-33-02-934.png|thumbnail! 


Could you please check? 
Thanks, Bert 


[This ticket was automatically created]",dvse security price added client entitlement calculated imagepngthumbnail market side worked imagepngthumbnail could please check thanks bert ticket automatically created,0.009324731305241585,0.0,328,0.004225293020274839,0.2476688171736896,1.***************,0.2,0.1,0.15,54.65%
Incident,OEKB-6116,Cosmetic,"M12-I5: TEND0000001700 - Client Inst in status ""PossibleRejected"" - ""Reject""-functionality missing",Automation test,2024-07-09 14:06:00,2024-07-10 09:20:00,NO,"TEND0000001700 
MT565 NOAC was received, elig position < instructed position. 
Instruction moved to ""PossibleRejected"". 

When checking To-Do-List, I found out that there is no functionality/button to proceed => 

!image-2024-07-09-13-58-29-506.png|thumbnail! 

Please check and provide the relevant functionality (such as ""Reject""-button)! 

Thanks, Bert 







[This ticket was automatically created]",tend mt noac received elig position instructed position instruction moved possiblerejected checking todolist found functionalitybutton proceed imagepngthumbnail please check provide relevant functionality rejectbutton thanks bert ticket automatically created,0.04691021889448166,0.0,328,0.004225293020274839,0.23827244527637959,1.***************,0.2,0.1,0.15,53.24%
Incident,OEKB-6115,Major,M12-I5: SRD II PROX - Resolution Status not mapped,Automation test,2024-07-09 11:19:00,2024-07-09 12:15:00,NO,"Event PROX0000001690 

Resolution status is included in incoming seev.001 (you find it attached): 

!image-2024-07-09-11-17-00-315.png! 

unfortunately it is not mapped into MegaCor: 

!image-2024-07-09-11-17-47-957.png! 

  

Please check that - thanks. 

stefan 


[This ticket was automatically created]",event prox resolution status included incoming seev find attached imagepng unfortunately mapped megacor imagepng please check thanks stefan ticket automatically created,0.022239595651626587,0.0,328,0.004225293020274839,0.24444010108709335,1.***************,0.2,0.13,0.15,58.67%
Incident,OEKB-6114,Critical,M12-I5: REDM0000001611 - MT566 failed to Agent and Client,Automation test,2024-07-05 11:28:00,2024-07-05 13:09:00,NO,"REDM0000001611 
Payments were done successfully, however, MT566 - both to Issuer/Paying Agent (221700) and client (243900) failed in MegaBroker => 

!image-2024-07-05-11-11-12-909.png|thumbnail! 

Message IDs: 
11080024 
11080025 
11080026 
11080027 

Could you please check? 
Thanks, Bert 


[This ticket was automatically created]",redm payment done successfully however mt issuerpaying agent client failed megabroker imagepngthumbnail message id could please check thanks bert ticket automatically created,0.04376071318984032,0.0,332,0.003952791134272453,0.23905982170253992,1.***************,0.2,0.15,0.15,60.86%
Incident,OEKB-6113,Critical,M12-I5: DVSE0000001603 - Entitlement calculation failed,Automation test,2024-07-05 08:48:00,2024-07-06 11:37:00,NO,"DVSE0000001603 
was set up with Ex-Date and Record Date 03/07/2024, Pay Date 04/07/2024. 

When checking the event, I found out that no entitlements have been calculated although there is an ELIG position. 
When trying to calculate Entitlements manually, the following message poppep up => 

!image-2024-07-05-08-43-05-472.png|thumbnail! 

Could you please check? 
Thanks, Bert 






[This ticket was automatically created]",dvse set exdate record date pay date checking event found entitlement calculated although elig position trying calculate entitlement manually following message poppep imagepngthumbnail could please check thanks bert ticket automatically created,-0.022250238806009293,1.0,332,0.003952791134272453,0.2555625597015023,1.***************,0.2,0.15,0.15,63.33%
Incident,OEKB-6112,Major,"M12-I5: REDM0000001563 - ""Is Agent Involved""-flag not set correctly for SECU OUT",Automation test,2024-07-04 16:44:00,2024-07-10 15:45:00,NO,"REDM0000001563 
was updated due to incoming MT564 generated via csv file for ""Mass Redemptions"" (outside MegaCor). 

After validation, I noticed that the ""Is Agent Involved""-flag for SECU OUT movement is not set correctly => 

!image-2024-07-04-16-35-45-755.png|thumbnail! 
Correct value is FALSE. 

Flags for SECU IN (true) and CASH IN (false) are set correctly. 

Could you please check! 
Thanks, Bert 





[This ticket was automatically created]",redm updated due incoming mt generated via csv file mass redemption outside megacor validation noticed agent involvedflag secu movement set correctly imagepngthumbnail correct value false flag secu true cash false set correctly could please check thanks bert ticket automatically created,-0.006781972944736481,5.0,332,0.003952791134272453,0.2516954932361841,1.***************,0.2,0.13,0.15,59.75%
Incident,OEKB-6111,Critical,M12-I5: EXOF MAND - Input Screen not usable,Automation test,2024-07-03 15:03:00,2024-07-03 15:46:00,NO,"When trying to create EXOF MAND event manually, I noticed that the input screen is not usable => 

!image-2024-07-03-14-55-00-398.png|thumbnail! 

Fields like 
Paying Agent 
Issuer Agent 
LInked CA 
Shareholder Rights Directive Indicator 
Announcement Date 
Effective Date 
CA Comment 

are missing, also, the design looks different than the other input CA screens, see here PARI MAND input screen, as an example => 

!image-2024-07-03-14-59-08-428.png|thumbnail! 


Please check! 
Thanks, Bert 



[This ticket was automatically created]",trying create exof mand event manually noticed input screen usable imagepngthumbnail field like paying agent issuer agent linked ca shareholder right directive indicator announcement date effective date ca comment missing also design look different input ca screen see pari mand input screen example imagepngthumbnail please check thanks bert ticket automatically created,-0.006850775331258774,0.0,334,0.0038232032269108476,0.2517126938328147,1.***************,0.2,0.15,0.15,62.76%
Information Request,OEKB-6110,Major,M12-I5: TEND0000001446 - Validate Cancellation / No Unfreeze failed,Automation test,2024-07-03 09:58:00,2024-07-03 10:41:00,NO,"TEND0000001446 
Payments were already done. 
MT565 CANC was received. 
When trying to validate the cancellation request via 'Validate Cancellation / No Unfreeze', firstly, I noticed that there are two buttons with different names (do they have the same functionality, if yes, the name should be the same) => 

!image-2024-07-03-09-51-11-902.png|thumbnail! 

When trying to validate, via 'Validate No Unfreeze'-button, the following error popped up => 

!image-2024-07-03-09-50-20-218.png|thumbnail! 

After clicking 'SkipExternalFreezeCancellation', the following message popped up => 

!image-2024-07-03-09-54-41-783.png|thumbnail! 


However, the status of the instruction is still 'WaitingCancellationValidation' => 

!image-2024-07-03-09-55-18-077.png|thumbnail! 


Could you please check? 
Thanks, Bert 




[This ticket was automatically created]",tend payment already done mt canc received trying validate cancellation request via validate cancellation unfreeze firstly noticed two button different name functionality yes name imagepngthumbnail trying validate via validate unfreezebutton following error popped imagepngthumbnail clicking skipexternalfreezecancellation following message popped imagepngthumbnail however status instruction still waitingcancellationvalidation imagepngthumbnail could please check thanks bert ticket automatically created,-0.04629121348261833,0.0,334,0.0038232032269108476,0.2615728033706546,1.***************,0.2,0.13,0.06,47.74%
Incident,OEKB-6109,Medium,"M12-I5: ""TODO – QI Reporting"" - Header",Gregor WILDING,2024-07-02 11:27:00,2024-07-02 13:35:00,NO,"""TODO – QI Reportingt"": 

Please find attached some findings to this TODO-section. 

 ",todo qi reportingt please find attached finding todosection,0.02493390068411827,0.0,335,0.0037600112358255108,0.24376652482897043,0.024216245168726842,0.0045885282524504465,0.1,0.15,24.75%
Incident,OEKB-6108,Medium,"M12-I5: ""TODO – Shareholder Disclosure Request"" - Header",Automation test,2024-07-02 10:28:00,2024-07-24 10:02:00,YES,"""TODO – Shareholder Disclosure Request"": 

Please find attached some findings to this TODO-section. 


[This ticket was automatically created]",todo shareholder disclosure request please find attached finding todosection ticket automatically created,-0.020818224176764488,21.0,335,0.0037600112358255108,0.2552045560441911,1.***************,0.2,0.1,0.15,55.78%
Incident,OEKB-6107,Major,M12-I5: MRGR0000001506 - Update Fraction Price / Market Entitlements were calculated twice,Automation test,2024-07-02 09:28:00,2024-07-06 11:38:00,NO,"MRGR0000001506 
Fraction Price was updated via updateClientAndMarketFraction-functionality => 

!image-2024-07-02-09-20-56-185.png|thumbnail! 

Client Entitlements (in total: 1,28 shares) were updated successfully => 

!image-2024-07-02-09-24-02-749.png|thumbnail! 

However, when checking Market Entitlements, I noticed that the entitlements were calculated twice => 

!image-2024-07-02-09-24-56-631.png|thumbnail! 
' 

Could you please check? 
Thanks, Bert 



[This ticket was automatically created]",mrgr fraction price updated via updateclientandmarketfractionfunctionality imagepngthumbnail client entitlement total share updated successfully imagepngthumbnail however checking market entitlement noticed entitlement calculated twice imagepngthumbnail could please check thanks bert ticket automatically created,-0.028247714042663574,4.0,335,0.0037600112358255108,0.2570619285106659,1.***************,0.2,0.13,0.15,60.56%
Incident,OEKB-6106,Medium,"M12-I5: ""TODO – Shareholder Disclosure Request - Disclosure Response to be sent"" - Wrong Count",Automation test,2024-07-02 09:28:00,2024-07-02 10:21:00,NO,"The headers of the following screens, called from ""TODO - Proxy Voting"", are incorrect: 
* Missing LEI CODE 
* Cross Check Proxy Meeting 
* Cancellation to be validated 
* PV Market instructions to be send 
* Record Date in the Past 

  

Please adapt it and use unique names, as the name will be used for excel-export as well. 

  


[This ticket was automatically created]",header following screen called todo proxy voting incorrect missing lei code cross check proxy meeting cancellation validated pv market instruction send record date past please adapt use unique name name used excelexport well ticket automatically created,-0.023207983002066612,0.0,335,0.0037600112358255108,0.25580199575051665,1.***************,0.2,0.1,0.15,55.87%
Incident,OEKB-6105,Major,"M12-I5: MRGR0000001506 - MT564 contains :19B::ENTL//EUR0,",Automation test,2024-07-01 16:23:00,2024-07-26 14:17:00,NO,"MRGR0000001506 
was set up with CASE option including Cash component of EUR 0,1262113 per unit. 
Entitlements have been calculated manually before MT564 NEWM was sent. 

MT564 NEWM was then sent as ""Preliminary Advice of Payment"" (:22F::ADDB//CAPA) which is correct, but with incorrect Entitled Amount (EUR 0,) => 

!image-2024-07-01-16-21-17-077.png|thumbnail! 

Could you please check? 
Thanks, Bert 


[This ticket was automatically created]",mrgr set case option including cash component eur per unit entitlement calculated manually mt newm sent mt newm sent preliminary advice payment faddbcapa correct incorrect entitled amount eur imagepngthumbnail could please check thanks bert ticket automatically created,0.01937708631157875,24.0,336,0.003697863716482932,0.2451557284221053,1.***************,0.2,0.13,0.15,58.77%
Incident,OEKB-6104,Major,M12-I5: MT564 REDM failed in MegaBroker,Automation test,2024-07-01 14:58:00,2024-07-02 10:07:00,YES,"For ""Mass Redemption Events"" in the domestic market, MT564 messages are generated from specific Excel file and sent to MegaBroker. 
When testing this case, we noticed that the message (attached) failed in MegaBroker (Message ID 10610024) => 

!image-2024-07-01-14-51-13-863.png|thumbnail! 

Can you please check! 
Is there a configuration missing? 

Please also see attached MT564 from PROD which looks (more or less) the same. This message was processed accordingly in M10 => 

!image-2024-07-01-14-54-27-373.png|thumbnail! 

Thanks, 
Bert 




[This ticket was automatically created]",mass redemption event domestic market mt message generated specific excel file sent megabroker testing case noticed message attached failed megabroker message id imagepngthumbnail please check configuration missing please also see attached mt prod look less message processed accordingly imagepngthumbnail thanks bert ticket automatically created,-0.05588468164205551,0.0,336,0.003697863716482932,0.2639711704105139,1.***************,0.2,0.13,0.15,61.6%
Incident,OEKB-6103,Medium,M12-I5: Create Secu Client Payments out of Entitlement - Sort columns does not work,Automation test,2024-07-01 11:28:00,2024-07-06 11:39:00,NO,"When creating Client Payments out of Entitlements, it is important for the user to sort the columns, especially the ""Impact Type"" (in case only SECU OUT payments have to be generated). 

However, the sorting does not work at all for any of the columns => 

!image-2024-07-01-11-23-48-204.png|thumbnail! 

Please check! 
Thanks, Bert 


[This ticket was automatically created]",creating client payment entitlement important user sort column especially impact type case secu payment generated however sorting work column imagepngthumbnail please check thanks bert ticket automatically created,0.011291518807411194,5.0,336,0.003697863716482932,0.2471771202981472,1.***************,0.2,0.1,0.15,54.58%
Incident,OEKB-6102,Cosmetic,Client Group View - not readable,Valdes ELIZABEHT,2024-06-28 10:25:00,2024-07-06 11:40:00,YES,"Dear All,  

during my review of SRDII Notifications configuration - I wanted to see the groups captured - but I'm not able to read anything.  

!image-2024-06-28-10-25-29-223.png! 

Please adjust the visible columns - so that infomrations can be retrieved.  

Thanks & KR,  

Eli",dear review srdii notification configuration wanted see group captured im able read anything imagepng please adjust visible column infomrations retrieved thanks kr eli,-0.009074613451957703,8.0,339,0.0035175167749121284,0.2522686533629894,0.*****************,0.012503434490811616,0.1,0.15,27.22%
Information Request,OEKB-6101,Major,Missing function in ASOC - notification should be downloadable,Valdes ELIZABEHT,2024-06-28 09:33:00,2024-06-28 12:03:00,YES,"*During our UAT we noticed one function has not yet been implemented in ASOC:* 


The notification download. I have not found any documentation in Excel for: ""{*}MegaCor/services/rest/remote-message{*}"" 

Please request this from Vermeg. 

  

Based on this example search result, the notification should be downloadable: 
[ 

{""creationDate"":""2023-09-25T02:00:00. 000+02:00"",""entitledIsin"":""JP3151600008"",""client"":""222200"",""appReference"":""DCA229768"",""notificationType"":""Corporate Action Notification"",""receiverAddress"":""BAWAATW0XXXX"",""messageFunction"": ""NEWM"",""messageAppReference"":""**********"",""mainReference"":""CONS0000030528"",""messageFormat"":""Swift"",""messageType"":""564"",""securityAccount"":""222200"",""code"":""142992""} 
] 

  

Please provide this asap to be able to continue with our tests.  

Thanks & KR,  

Eli 

 ",uat noticed one function yet implemented asoc notification download found documentation excel megacorservicesrestremotemessage please request vermeg based example search result notification downloadable creationdatet entitledisinjpclientappreferencedcanotificationtypecorporate action notificationreceiveraddressbawaatwxxxxmessagefunction newmmessageappreferencemainreferenceconsmessageformatswiftmessagetypesecurityaccountcode please provide asap able continue test thanks kr eli,0.*****************,0.0,339,0.0035175167749121284,0.*****************,0.*****************,0.012503434490811616,0.13,0.06,15.01%
Information Request,OEKB-6100,Major,M12-I5: SRD II PROX - incomprehensible client notifications,Automation test,2024-06-28 08:58:00,2024-07-01 12:15:00,NO,"Event PROX0000001445 

For this ISIN AT0000741053 there are 90 clients existing, but following notifications have been generated: 
* CA creation: 32 
* CA Update: 31 (difference: no CA Update for account 277700) 
* CA Meeting Entitlement: 88 

Why did the system create CA Meeting Entitlements for almost 60 clients but generated no CA creation or CA Update messages? 

We searched in Client Reporting SLA to find a reason for that but didn't find out anything. 

For example: Accounts 203000 or 204200: No SLA existing for these clients or accounts, but no CA creation and no CA Update generated. 

Please advise what was wrong here. 

Thanks, stefan 


[This ticket was automatically created]",event prox isin client existing following notification generated ca creation ca update difference ca update account ca meeting entitlement system create ca meeting entitlement almost client generated ca creation ca update message searched client reporting sla find reason didnt find anything example account sla existing client account ca creation ca update generated please advise wrong thanks stefan ticket automatically created,-0.009534448385238647,3.0,339,0.0035175167749121284,0.*****************,1.***************,0.2,0.13,0.06,46.36%
Information Request,OEKB-6099,Major,M12-I5: DVCA0000000912 - MT566 Message not sent,Automation test,2024-06-27 17:18:00,2024-07-02 19:39:00,NO,"DVCA0000000912 

Hello,  

in the above event the account number in USD was missing for customer 2399 and therefore the event was in status ""Partially confirmed""  

I have created the ""Client Cash Account SLA"" and made the payment for the client. 

The event status is ""Confirmed"" and the client's (2399) status is ""Sent"" but I can't find the M566 message. 

Please check! 

  

Thank you and BR, Dalibor 


[This ticket was automatically created]",dvca hello event account number usd missing customer therefore event status partially confirmed created client cash account sla made payment client event status confirmed client status sent cant find message please check thank br dalibor ticket automatically created,-0.*****************,5.0,339,0.0035175167749121284,0.*****************,1.***************,0.2,0.13,0.06,48.23%
Incident,OEKB-6098,Medium,"M12-I5: Errors in header of ""TODO – Proxy Voting screens",Automation test,2024-06-27 16:16:00,2024-07-23 15:09:00,YES,"The headers of the following screens, called from ""TODO - Proxy Voting"", are incorrect: 
* Missing LEI CODE 
* Cross Check Proxy Meeting 
* Cancellation to be validated 
* PV Market instructions to be send 
* Record Date in the Past 

  

Please adapt it and use unique names, as the name will be used for excel-export as well. 

  


[This ticket was automatically created]",header following screen called todo proxy voting incorrect missing lei code cross check proxy meeting cancellation validated pv market instruction send record date past please adapt use unique name name used excelexport well ticket automatically created,-0.023207983002066612,25.0,340,0.0034593773364647584,0.25580199575051665,1.***************,0.2,0.1,0.15,55.87%
Incident,OEKB-6097,Medium,"M12-I5: Errors on ""TODO – Proxy Voting - Failed sent seev"" screens",Automation test,2024-06-27 14:08:00,2024-06-29 15:07:00,YES,"Please find attached a document regarding errors on TO[^TODO – Proxy Voting – Failed sent message.docx]DO – Proxy Voting - Failed sent seev"""" screens. 


[This ticket was automatically created]",please find attached document regarding error totodo proxy voting failed sent messagedocxdo proxy voting failed sent seev screen ticket automatically created,-0.8149608802050352,2.0,340,0.0034593773364647584,0.4537402200512588,1.***************,0.2,0.1,0.15,85.56%
Incident,OEKB-6096,Medium,"M12-I5: Errors on ""TODO – Proxy Voting - Failed received seev"" screens",Automation test,2024-06-27 13:38:00,2024-07-30 10:36:00,YES,"Please find attached a document regarding errors on TODO – Proxy Voting"" screens[^TODO – Proxy Voting.docx] 


[This ticket was automatically created]",please find attached document regarding error todo proxy voting screenstodo proxy votingdocx ticket automatically created,-0.0237607192248106,32.0,340,0.0034593773364647584,0.25594017980620265,1.***************,0.2,0.1,0.15,55.89%
Incident,OEKB-6095,Major,M12-I5: TEND0000001446 - MT566 OUT - :13A::LINK// missing,Stefan RIBISCH,2024-06-27 12:03:00,2024-06-29 15:08:00,NO,"+++ CLONE ok OEKB-6011 +++ 

TEND0000001446 
When checking MT566 OUT, I noticed that 
:13A::LINK//565 
is missing in the linkage block => 

!screenshot-1 (1).png! 

See example from M10 => 

!screenshot-2.png! 

Could you please check? 
Thanks, Bert",clone ok oekb tend checking mt noticed alink missing linkage block screenshot png see example screenshotpng could please check thanks bert,-0.21726786717772484,2.0,340,0.0034593773364647584,0.3043169667944312,0.*****************,0.*****************,0.13,0.15,40.77%
Incident,OEKB-6094,Major,M12-I5: TEND0000001469 - No MT567 CANC confirmation sent to client,Automation test,2024-06-27 10:43:00,2024-07-02 19:28:00,NO,"TEND0000001469 
Cancellation request was received, MT567 CAST was sent containing => 

!image-2024-06-27-10-37-56-875.png|thumbnail! 

which is ok. 

After validation of cancellation request and unfreeze, the client instruction moved to Status ""CreatedCICancelled"", however, no further MT567 CAST confirming the cancellation was sent to the client. 

Could you please check? 
Thanks, Bert 


[This ticket was automatically created]",tend cancellation request received mt cast sent containing imagepngthumbnail ok validation cancellation request unfreeze client instruction moved status createdcicancelled however mt cast confirming cancellation sent client could please check thanks bert ticket automatically created,-0.2940979488193989,5.0,340,0.0034593773364647584,0.3235244872048497,1.***************,0.2,0.13,0.15,70.53%
Incident,OEKB-6093,Medium,M12-I5: Cosmetic Issue - Rename result list outgoing MT567,Automation test,2024-06-27 10:38:00,2024-06-29 15:06:00,NO,"When checking outgoing MT567 via 

!image-2024-06-27-10-32-53-463.png|thumbnail! 

I noticed that the result list is named incorrectly => 

!image-2024-06-27-10-34-25-571.png|thumbnail! 

Please amend it to ""Sent"", thanks! 
BR Bert 


[This ticket was automatically created]",checking outgoing mt via imagepngthumbnail noticed result list named incorrectly imagepngthumbnail please amend sent thanks br bert ticket automatically created,-0.011681830510497093,2.0,340,0.0034593773364647584,0.2529204576276243,1.***************,0.2,0.1,0.15,55.44%
Incident,OEKB-6092,Major,M12: ASOC Deadline Time should not be populated,Valdes ELIZABEHT,2024-06-27 10:10:00,2024-06-27 11:50:00,NO,"Dear All,  

No time should be transmitted at all from the event data.  

!Eventdates.png! 

In ASCO following is visible:  

!ASOC_Eventdates.png! 

Can you please check and revert. 

Thanks & KR,  

Eli 

 ",dear time transmitted event data eventdatespng asco following visible asoceventdatespng please check revert thanks kr eli,-0.012157661840319633,0.0,340,0.0034593773364647584,0.2530394154600799,0.*****************,0.012503434490811616,0.13,0.15,31.83%
Incident,OEKB-6091,Major,M12-I5: BPUT0000000028 - MT564 OUT contains,Automation test,2024-06-26 17:48:00,2024-06-29 15:08:00,NO,"BPUT0000000028  

Hello, 

When checking MT564 OUT, I noticed that it includes an ""Option Feature Indicator"" (OPFT) with the value QCAS (""Feature whereby the holder should only instruct a cash amount"") 

  

16S:CADETL 
:16R:CAOPTN 
:13A::CAON//001 
{color:#de350b}:22F::OPTF//QCAS{color} 
:22F::CAOP//CASH 
:11A::OPTN//EUR 

  

Please check! 

  

Thank you and BR, Dalibor 


[This ticket was automatically created]",bput hello checking mt noticed includes option feature indicator opft value qcas feature whereby holder instruct cash amount scadetl rcaoptn acaon colordebfoptfqcascolor fcaopcash aoptneur please check thank br dalibor ticket automatically created,0.022424399852752686,2.0,340,0.0034593773364647584,0.24439390003681183,1.***************,0.2,0.13,0.15,58.66%
Information Request,OEKB-6090,Medium,Megara Aler,Nikolay TSONKOV,2024-06-26 14:30:00,2024-06-28 09:21:00,NO,"Hi Emna, colleagues 

  

attached you will find one Alert which we recieved , bur as per checks inside MeagCor M12 it is visble that the first 3 batches are processed. In the alert it is also stated batch 4 and 5 

  

can you pls check  

  

BR,  

Niki",hi emna colleague attached find one alert recieved bur per check inside meagcor visble first batch processed alert also stated batch pls check br niki,0.007071908563375473,1.0,341,0.003402198858410551,0.24823202285915613,0.012479781565630135,0.0023646865936200277,0.1,0.06,11.59%
Information Request,OEKB-6089,Major,M12-I5: INTR0000001185 - Error when trying to open the MT566 OUT message,Automation test,2024-06-26 12:03:00,2024-06-26 13:48:00,NO,"test with INTR0000001185 

Hello,  

When trying to open the MT566 OUT Swift, an error message appears. 

  

Please check! 

  

Thank you and BR, Dalibor 


[This ticket was automatically created]",test intr hello trying open mt swift error message appears please check thank br dalibor ticket automatically created,-0.008587680757045746,0.0,341,0.003402198858410551,0.25214692018926144,1.***************,0.2,0.13,0.06,46.32%
Information Request,OEKB-6088,Major,M12-I5: SUPER EVENT EXOF - unnecessary CA Updates,Automation test,2024-06-26 11:49:00,2024-06-27 01:29:00,NO,"Event EXOF0000001227 

Today I noticed by chance that after the last reminder on 17/06 one day later on 18/06 the system generated and sent CA Updates. 

The compare screen is very confusing but they would show as a change in the comment from Bert, but we added this already on 14/06: 

!image-2024-06-26-11-45-33-735.png! 

Fact is: We did not do any change in the event and an Update has been sent out again. I am quite sure we had this problem already but it did not occur in the meantime for a longer period - now I noticed it randomly again. 

Please check that. 
Thanks, stefan 


[This ticket was automatically created]",event exof today noticed chance last reminder one day later system generated sent ca update compare screen confusing would show change comment bert added already imagepng fact change event update sent quite sure problem already occur meantime longer period noticed randomly please check thanks stefan ticket automatically created,-0.023405738174915314,0.0,341,0.003402198858410551,0.25585143454372883,1.***************,0.2,0.13,0.06,46.88%
Incident,OEKB-6087,Medium,M12-I5: SRD II PROX - NRIN not included in outgoing seev.001,Automation test,2024-06-26 11:23:00,2024-06-29 15:08:00,NO,"After positive retest of OEKB-5778 I checked the outgoing seev.001 of PROX0000001445 and unfortunately here the ""NRIN"" is not included. 
In this screenshot you find the List feed on the left side and the output on the right side: 

!image-2024-06-26-11-19-30-825.png! 

I attached the whole flowOut for you to this bug too. 

Please include NRIN in the outgoing SWIFT in case it is existing in MegaCor. 

Thanks, stefan 


[This ticket was automatically created]",positive retest oekb checked outgoing seev prox unfortunately nrin included screenshot find list feed left side output right side imagepng attached whole flowout bug please include nrin outgoing swift case existing megacor thanks stefan ticket automatically created,0.02118786796927452,3.0,341,0.003402198858410551,0.24470303300768137,1.***************,0.2,0.1,0.15,54.21%
Incident,OEKB-6086,Major,M12-I5: - Ad-Hoc booking CASH Instructions incorrect,Automation test,2024-06-26 11:13:00,2024-06-26 16:00:00,NO,"test with DVCA0000001291 

Hello,  

I have created AdHoc *CASH* bookings in the Event mentioned above. 

*CASH* Client vs Client and *CASH* Client vs Market 

The booking status is still ""Waiting settlement Confirmation"" and ""Waiting CashOut Payment"". 

The instructions in CCSYS are in ""failling"" status with the reason *""SXAA015 - Failure of the settlement attempt of the settlement instruction due to a lack of securities in the securities position of the counterparty"".* 

In my opinion, this is a mistake, ""DVP"" and ""RVP"" instructions were created here. 

""PFRD"" and ""PFDC"" instructions would be correct. 

Bert has the same problem in MRGR0000001249. 

  

Please check! 

  

Thank you and BR, Dalibor 

  


[This ticket was automatically created]",test dvca hello created adhoc cash booking event mentioned cash client v client cash client v market booking status still waiting settlement confirmation waiting cashout payment instruction ccsys failling status reason sxaa failure settlement attempt settlement instruction due lack security security position counterparty opinion mistake dvp rvp instruction created pfrd pfdc instruction would correct bert problem mrgr please check thank br dalibor ticket automatically created,-0.1267978437244892,0.0,341,0.003402198858410551,0.2816994609311223,1.***************,0.2,0.13,0.15,64.25%
Information Request,OEKB-6085,Major,M12-I5: ODLT0000001425 - SAP Booking rejected - Not available in To Do-screen / How to resend?,Automation test,2024-06-26 10:08:00,2024-06-26 14:02:00,NO,"ODLT0000001425 
Cash booking settled in T2S during Night Time Settlement. 
Hence, own fees booking request was sent to SAP but could not be processed => 

!image-2024-06-26-09-58-14-015.png|thumbnail! 

When searching the failed booking under ""Rejected Client SAP Cash Bookings"" in the To-Do-screen, I cannot find it => 

!image-2024-06-26-10-01-17-832.png|thumbnail! 

Could you please check? 
BR Bert 




[This ticket was automatically created]",odlt cash booking settled t night time settlement hence fee booking request sent sap could processed imagepngthumbnail searching failed booking rejected client sap cash booking todoscreen find imagepngthumbnail could please check br bert ticket automatically created,-0.04950161091983318,0.0,341,0.003402198858410551,0.2623754027299583,1.***************,0.2,0.13,0.06,47.86%
Incident,OEKB-6084,Blocker,Webservice Parameter changed for the market deadline or client deadline,Valdes ELIZABEHT,2024-06-26 09:14:00,2024-06-26 13:32:00,NO,"Dear All,  

we faced an Issue on the ASOC for market and client deadline:  

EXOF0000001391 
Event was created with client deadline 4.7.2024, 10 a.m., and market deadline 4.7.2024, 5 p.m. 

In ASOC they are not visible because the parameter name for the market deadline or client deadline has apparently changed in the interface 
(should be clientDeadline &marketDeadline => {color:#ff0000}clientDeadlineTime & {color}{color:#ff0000}marketDeadlineTime{color} ) 

  

Please check and revert.  

KR,  

Eli 

  

 ",dear faced issue asoc market client deadline exof event created client deadline market deadline pm asoc visible parameter name market deadline client deadline apparently changed interface clientdeadline marketdeadline colorffclientdeadlinetime colorcolorffmarketdeadlinetimecolor please check revert kr eli,-0.11534934118390083,0.0,341,0.003402198858410551,0.2788373352959752,0.*****************,0.012503434490811616,0.14,0.15,37.2%
Incident,OEKB-6083,Major,M12-I5: SRD II PROX - wrong date format in outgoing seev.003,Automation test,2024-06-25 12:03:00,2024-06-25 13:13:00,NO,"Same bug like OEKB-6079 but for other seev-message 

PROX0000001224 

in the outgoing SWIFT seev.003 (and maybe also in others - please check that it is corrected everywhere) the date+time is wrong formatted. According to SWIFT it should be ""YYYY-MM-DDThh:mm:ss.sss+/-hh:mm"" in the attached SWIFT it is ""YYYY-MM-DDThh:mm:ss.sss+/-hh:mm"". There must be a ""."" (point) before the ""sss"". 

Please check and correct that everywhere - thanks. 

BR, stefan 


[This ticket was automatically created]",bug like oekb seevmessage prox outgoing swift seev maybe also others please check corrected everywhere datetime wrong formatted according swift yyyymmddthhmmssssshhmm attached swift yyyymmddthhmmssssshhmm must point ss please check correct everywhere thanks br stefan ticket automatically created,-0.20579935237765312,0.0,342,0.003345965457471272,0.3014498380944133,1.***************,0.2,0.13,0.15,67.22%
Incident,OEKB-6082,Major,M12-I5: SRD II PROX - wrong header in outgoing seev.003,Automation test,2024-06-25 11:58:00,2024-06-25 15:15:00,NO,"Same bug like OEKB-6080 but for other seev-type 

PROX0000001224 

in the outgoing SWIFT seev.003 the header version is wrong - we received following error message from SWIFT network: 

""The application header's namespace (urn:iso:std:iso:20022:tech:xsd:head.001.001.03) is not valid. It should be 'BusinessApplicationHeaderV02' (urn:iso:std:iso:20022:tech:xsd:head.001.001.02)."". 

In the attached outgoing SWIFT it is ""<AppHdr xmlns=""urn:iso:std:iso:20022:tech:xsd:head.001.001.03"">"" 

Please check and correct that - thanks. 

BR, stefan 


[This ticket was automatically created]",bug like oekb seevtype prox outgoing swift seev header version wrong received following error message swift network application header namespace urnisostdisotechxsdhead valid businessapplicationheaderv urnisostdisotechxsdhead attached outgoing swift apphdr xmlnsurnisostdisotechxsdhead please check correct thanks br stefan ticket automatically created,-0.36623513139784336,0.0,342,0.003345965457471272,0.34155878284946084,1.***************,0.2,0.13,0.15,73.23%
Incident,OEKB-6081,Medium,M12-I5: SRD II PROX - event could not be updated,Automation test,2024-06-25 11:49:00,2024-06-25 18:28:00,NO,"PROX0000001426 

event was received with Vote Channel VOPI and with Processing Text for next intermediary. I wanted than to update the event - the only change I have done is adapted this processing text - but when I wanted to save I got following error message: 

First this warning: 
!image-2024-06-25-11-45-36-751.png! 

after clicking on OK this error message: 
!image-2024-06-25-11-45-50-908.png! 

Save is not possible afterwards. 

Please check that - it must be possible to change this text and update the event. 

Thanks, 
stefan 


[This ticket was automatically created]",prox event received vote channel vopi processing text next intermediary wanted update event change done adapted processing text wanted save got following error message first warning imagepng clicking ok error message imagepng save possible afterwards please check must possible change text update event thanks stefan ticket automatically created,-0.1461132038384676,0.0,342,0.003345965457471272,0.2865283009596169,1.***************,0.2,0.1,0.15,60.48%
Incident,OEKB-6080,Major,M12-I5: SRD II PROX - wrong header in outgoing seev.001,Automation test,2024-06-25 11:38:00,2024-06-25 15:15:00,NO,"PROX0000001426 

in the outgoing SWIFT seev.001 the header version is wrong - we received following error message from SWIFT network: 

""The application header's namespace (urn:iso:std:iso:20022:tech:xsd:head.001.001.03) is not valid. It should be 'BusinessApplicationHeaderV02' (urn:iso:std:iso:20022:tech:xsd:head.001.001.02)."". 

In the attached outgoing SWIFT it is ""<AppHdr xmlns=""urn:iso:std:iso:20022:tech:xsd:head.001.001.03"">"" 

Please check and correct that - thanks. 

BR, stefan 


[This ticket was automatically created]",prox outgoing swift seev header version wrong received following error message swift network application header namespace urnisostdisotechxsdhead valid businessapplicationheaderv urnisostdisotechxsdhead attached outgoing swift apphdr xmlnsurnisostdisotechxsdhead please check correct thanks br stefan ticket automatically created,-0.3267086520791054,0.0,342,0.003345965457471272,0.33167716301977634,1.***************,0.2,0.13,0.15,71.75%
Incident,OEKB-6079,Major,M12-I5: SRD II PROX - wrong date format in outgoing seev.001,Automation test,2024-06-25 11:23:00,2024-06-25 15:25:00,NO,"PROX0000001426 

in the outgoing SWIFT seev.001 (and maybe also in others - please check that it is corrected everywhere) the date+time is wrong formatted. According to SWIFT it should be ""YYYY-MM-DDThh:mm:ss.sss+/-hh:mm"" in the attached SWIFT it is ""YYYY-MM-DDThh:mm:ss:sss+/-hh:mm"". There must be a ""."" (point) before the ""sss"". 

Please check and correct that everywhere - thanks. 

BR, stefan 

[This ticket was automatically created]",prox outgoing swift seev maybe also others please check corrected everywhere datetime wrong formatted according swift yyyymmddthhmmssssshhmm attached swift yyyymmddthhmmssssshhmm must point ss please check correct everywhere thanks br stefan ticket automatically created,-0.07394766621291637,0.0,342,0.003345965457471272,0.2684869165532291,1.***************,0.2,0.13,0.15,62.27%
Incident,OEKB-6078,Major,M12-I5: - EXWA0000001331 - MT566 Message not sent to 3i,Automation test,2024-06-25 09:40:00,2024-06-29 15:09:00,NO,"test with EXWA0000001331 

Hello, 

the above event has been processed and is in Status confirmed.  

However, the MT566 message was not sent to 3i. 

  

Please check! 

  

Thank you and BR, Dalibor 


[This ticket was automatically created]",test exwa hello event processed status confirmed however mt message sent please check thank br dalibor ticket automatically created,-0.0036455877125263214,4.0,342,0.003345965457471272,0.2509113969281316,1.***************,0.2,0.13,0.15,59.64%
Incident,OEKB-6077,Major,M12-I5: - Dashboard Alert - Late Market Payment,Automation test,2024-06-24 16:16:00,2024-06-27 18:07:00,NO,"Dashboard alert => Market Payment => Late Market Security Payment/ Late Market Cash Payment 

Hello, 

Please configure the column settings as in M10. 

  

Thank you and BR, Dalibor 


[This ticket was automatically created]",dashboard alert market payment late market security payment late market cash payment hello please configure column setting thank br dalibor ticket automatically created,0.001706024631857872,3.0,343,0.003290661512896186,0.24957349384203553,1.***************,0.2,0.13,0.15,59.44%
Incident,OEKB-6075,Major,M12-I5: EXWA0000001331 - MT564NEWM to DEF005 is not sent,Automation test,2024-06-20 16:18:00,2024-06-20 18:27:00,NO,"test with EXWA0000001331 

  

Hello,  

The automatic job has generated and sent the MT564 messages for the above-mentioned event.  But i have noticed, that the message was not sent to DEF005 (3i).  

  

Please check! 

  

Thank you and BR, Dalibor 


[This ticket was automatically created]",test exwa hello automatic job generated sent mt message abovementioned event noticed message sent def please check thank br dalibor ticket automatically created,-0.02461400255560875,0.0,347,0.0030784368306890934,0.2561535006389022,1.***************,0.2,0.13,0.15,60.42%
Incident,OEKB-6072,Medium,M12-I5: ODLT0000001329 - MT564 OUT - Unknown field :92A::ATAX,Automation test,2024-06-20 09:18:00,2024-06-20 09:25:00,NO,"+++ CLONE OF CMTTM-1619 / OEKB-5999 +++ 

ODLT0000001329 
I noticed that there is an unknown field populated in the outgoing MT564 (full message attached) => 

!screenshot-1.png|thumbnail! 

Could you please check why this field is included and/or delete it? 
Thanks, Bert 

[^flowOut_9310024.txt] 




[This ticket was automatically created]",clone cmttm oekb odlt noticed unknown field populated outgoing mt full message attached screenshotpngthumbnail could please check field included andor delete thanks bert flowouttxt ticket automatically created,-0.03643046319484711,0.0,347,0.0030784368306890934,0.2591076157987118,1.***************,0.2,0.1,0.15,56.37%
Information Request,OEKB-6071,Cosmetic,"M12-I5: RHDI0000001312 - CA Status ""PartiallyConfirmed"" incorrect",Automation test,2024-06-20 09:03:00,2024-06-20 09:27:00,NO,"RHDI0000001312 
was set up for re-testing bug OEKB-6026 (Release CSD Hold). 

After all client payments have been confirmed, the standard behavior (in M10) is that the CA Status moves to ""Confirmed"" which is the condition to release the CSD Hold of claims. 

In this case, the CA Status is ""PartiallyConfirmed"" => 

!image-2024-06-20-08-55-30-409.png|thumbnail! 

although all regular payments have been confirmed => 

!image-2024-06-20-09-00-59-205.png|thumbnail! 


Could you please check? 

Thanks! 
BR Bert 






[This ticket was automatically created]",rhdi set retesting bug oekb release csd hold client payment confirmed standard behavior ca status move confirmed condition release csd hold claim case ca status partiallyconfirmed imagepngthumbnail although regular payment confirmed imagepngthumbnail could please check thanks br bert ticket automatically created,0.002317579463124275,0.0,347,0.0030784368306890934,0.24942060513421893,1.***************,0.2,0.1,0.06,41.41%
Incident,OEKB-6069,Critical,M12-I5: SPLF0000001293 - Market Payment Instruction (sese.023) not generated/sent?!,Automation test,2024-06-19 09:08:00,2024-06-19 09:23:00,NO,"SPLF0000001293 
Market Payment SECU IN was created according to incoming MT566. 
When checking instructions in CCSYS, I found out that only the ""Counterpart Market Payment Instruction"" is available (and is, of course, UNMATCHED), but not the ""Market Payment Instruction"" => 

!image-2024-06-19-09-05-00-190.png|thumbnail! 

!image-2024-06-19-09-05-27-723.png|thumbnail! 

Could you please check! 
Thanks, Bert 




[This ticket was automatically created]",splf market payment secu created according incoming mt checking instruction ccsys found counterpart market payment instruction available course unmatched market payment instruction imagepngthumbnail imagepngthumbnail could please check thanks bert ticket automatically created,0.021652940660715103,0.0,348,0.0030275547453758153,0.24458676483482122,1.***************,0.2,0.15,0.15,61.69%
Incident,OEKB-6068,Critical,M12-I5: SPLF0000001293 - Event automatically moved to status Created/Activated,Automation test,2024-06-18 17:03:00,2024-06-25 09:51:00,NO,"SPLF0000001293 
I imported MT564 (attached). 
By checking the event, I noticed that it is already in status Created/Activated without any user intervention?! 

Consequently, client notifications were generated/sent which is not the requested behavior! 

Could you please check? 

Thanks, Bert 


[This ticket was automatically created]",splf imported mt attached checking event noticed already status createdactivated without user intervention consequently client notification generatedsent requested behavior could please check thanks bert ticket automatically created,-0.09123974107205868,6.0,348,0.0030275547453758153,0.27280993526801467,1.***************,0.2,0.15,0.15,65.92%
Incident,OEKB-6067,Major,"M12-I5: error when forcing Status ""BlockedForCutOffTime""",Automation test,2024-06-18 17:03:00,2024-06-18 19:07:00,NO,"I wanted to force three reversed client payments which were in Status ""BlockedForCutOffTime"", unfortunately after selecting the affected, following error occurs: 

!image-2024-06-18-17-02-23-932.png! 

and the lines are deselected again. 

Please check what is the problem here. 

  

Thanks, stefan 


[This ticket was automatically created]",wanted force three reversed client payment status blockedforcutofftime unfortunately selecting affected following error occurs imagepng line deselected please check problem thanks stefan ticket automatically created,-0.26402801275253296,0.0,348,0.0030275547453758153,0.31600700318813324,1.***************,0.2,0.13,0.15,69.4%
Incident,OEKB-6066,Major,"M12-I5: DVCA0000001291 - Event automatically ""crosschecked""",Automation test,2024-06-18 16:53:00,2024-07-04 12:52:00,NO,"test with DVCA0000001291  

Hello, 

the above DVCA Event was created by importing MT564NEWM message and incorrectly automatically ""cross checked"". The Status is ""Activated"" ""Created"". 

I expected to find the event in Status ""Preliminary"" ""Created"" because the crosscheck has to be done manually. 

  

Please check! 

  

Thank you and BR, Dalibor 


[This ticket was automatically created]",test dvca hello dvca event created importing mtnewm message incorrectly automatically cross checked status activated created expected find event status preliminary created crosscheck done manually please check thank br dalibor ticket automatically created,-0.11171085014939308,15.0,348,0.0030275547453758153,0.27792771253734827,1.***************,0.2,0.13,0.15,63.69%
Incident,OEKB-6065,Major,M12-I5: MT567 Impact Failed,Automation test,2024-06-18 16:28:00,2024-06-18 23:43:00,NO,"Event EXOF0000001227 

I imported some MT567 (from custodian) and they are allocated to the right event but they all have an impact failed error: 

!image-2024-06-18-16-25-08-179.png! 

Seems to be excatly the same error as already reported and fixed and positive retested in OEKB-5994. 

Please check that again. 

Thanks, stefan 


[This ticket was automatically created]",event exof imported mt custodian allocated right event impact failed error imagepng seems excatly error already reported fixed positive retested oekb please check thanks stefan ticket automatically created,-0.10413867607712746,0.0,349,0.00297751366695929,0.27603466901928186,1.***************,0.2,0.13,0.15,63.41%
Information Request,OEKB-6064,Major,M12-I5: SHPR0000001214 - Cut Off Time,Automation test,2024-06-18 16:17:00,2024-06-18 18:28:00,NO,"test with SHPR0000001214 

Hello, 

for the above mentioned SHPR Event the MT566 has been imported and validated after half past three. This means that the ""cut-off time"" has been reached. 

However, only client CASH payment were stopped here and is in Status ""BlockedForCutOffTime"". The market CASH payment has been done. 

In my opinion, both ""CASH"" bookings (Client and Market) must automatically block from 15:30.  

  

Please check! 

  

Thank you and BR, Dalibor 

  

  


[This ticket was automatically created]",test shpr hello mentioned shpr event mt imported validated half past three mean cutoff time reached however client cash payment stopped status blockedforcutofftime market cash payment done opinion cash booking client market must automatically block please check thank br dalibor ticket automatically created,-0.3940916657447815,0.0,349,0.00297751366695929,0.3485229164361954,1.***************,0.2,0.13,0.06,60.78%
Incident,OEKB-6062,Major,M12-I5: SHPR - MT564NEWM and MT564REPE issue,Automation test,2024-06-18 15:23:00,2024-06-21 12:54:00,NO,"test with SHPR0000001214 

Hello, 

The MT564NEWM and MT564REPE messages have been sent successfully but some fields are missing. 

In MT564NEWM the line :92A::ATAX//0, is missing 

In MT564REPE the lines 

:92A::TAXR//0, 

:92A::ATAX//0, 

:19B::TAXR//EUR0, 
:19B::ATAX//EUR0, 

:19B::TXFR// 

are missing. 

  

Please check. 

  

Thank you and BR, Dalibor 

  


[This ticket was automatically created]",test shpr hello mtnewm mtrepe message sent successfully field missing mtnewm line aatax missing mtrepe line ataxr aatax btaxreur bataxeur btxfr missing please check thank br dalibor ticket automatically created,0.010608665645122528,2.0,349,0.00297751366695929,0.24734783358871937,1.***************,0.2,0.13,0.15,59.1%
Information Request,OEKB-6061,Major,M12-I5: SUPER EVENT EXOF - Import of MT567 not possible,Automation test,2024-06-18 12:38:00,2024-06-18 14:54:00,NO,"Event EXOF0000001227 

maybe it has the same root cause as OEKB-6057 but at the moment it is not possible to import an MT567. I leave it in the dropfolder but it is not found in MegaBroker and also not in MegaCor. 

Please check that. 

Thanks, 
Stefan 


[This ticket was automatically created]",event exof maybe root cause oekb moment possible import mt leave dropfolder found megabroker also megacor please check thanks stefan ticket automatically created,0.00857771560549736,0.0,349,0.00297751366695929,0.24785557109862566,1.***************,0.2,0.13,0.06,45.68%
Incident,OEKB-6059,Critical,M12-I5: REDM automatically closed by error,Automation test,2024-06-18 08:48:00,2024-06-18 11:05:00,NO,"Event REDM0000001246 

Yesterday I created this event with paydate 18/06. Today I wanted to check if REPE/seev.035 have been sent out and noticed that the event was closed already. 

Same error as already reported and fixed in OEKB-5957 - why did it occur again? 

Please check it again - thanks. 

stefan 


[This ticket was automatically created]",event redm yesterday created event paydate today wanted check repeseev sent noticed event closed already error already reported fixed oekb occur please check thanks stefan ticket automatically created,-0.1779004279524088,0.0,349,0.00297751366695929,0.2944751069881022,1.***************,0.2,0.15,0.15,69.17%
Information Request,OEKB-6057,Critical,M12-I5: LIQU0000001254 - Market Payment not processed / MegaBroker down?,Automation test,2024-06-18 08:45:00,2024-06-18 11:07:00,NO,"LIQU0000001254 
Could you please check why Market Payment is not processed, respectively, if MegaBroker is down, as no instruction was sent to CCSYS/T2S. 
When checking the message, this information is received => 

!image-2024-06-18-08-39-18-602.png|thumbnail! 

Thanks, Bert 


[This ticket was automatically created]",liqu could please check market payment processed respectively megabroker instruction sent ccsysts checking message information received imagepngthumbnail thanks bert ticket automatically created,0.03823166899383068,0.0,349,0.00297751366695929,0.24044208275154233,1.***************,0.2,0.15,0.06,47.57%
Incident,OEKB-6056,Critical,M12-I5: EXOF0000001117 - REPAIR Client Instructions - EDIT functionality missing,Automation test,2024-06-17 12:57:00,2024-06-17 13:02:00,NO,"EXOF0000001117 
When trying to repair Client Instructions, I noticed that the EDIT functionality is not available anymore => 

!image-2024-06-17-12-51-26-167.png|thumbnail! 

Could you please check! 
Thanks, Bert 


[This ticket was automatically created]",exof trying repair client instruction noticed edit functionality available anymore imagepngthumbnail could please check thanks bert ticket automatically created,0.004640476778149605,0.0,350,0.0029282996948181888,0.2488398808054626,1.***************,0.2,0.15,0.15,62.33%
Incident,OEKB-6055,Medium,M12-I1: Customer Interface- XML Batch Operation - Record Count,Automation test,2024-06-17 11:43:00,2024-06-18 12:33:00,NO,"The ""Record count"" in Screen ""XML Batch Operation"" for ""Customer Interface"" isn´t always correct. See red marked rows in attached screenshot.  

Futhermore there are entries with empty value in row ""Records Count"".  

Could you please check? 

!image-2024-06-17-11-37-03-326.png! 


[This ticket was automatically created]",record count screen xml batch operation customer interface isnt always correct see red marked row attached screenshot futhermore entry empty value row record count could please check imagepng ticket automatically created,-0.06440025940537453,1.0,350,0.0029282996948181888,0.26610006485134363,1.***************,0.2,0.1,0.15,57.42%
Incident,OEKB-6054,Critical,M12-I5: MRGR0000001249 - Ad Hoc Booking (CASH and SECU) failed - sese.023 incomplete,Automation test,2024-06-17 11:13:00,2024-06-17 11:49:00,NO,"MRGR0000001249 
Ad hoc booking CASH was generated, but payments failed due to incorrect sese.023, see CCSYS rejection with the following rejection reason => 

*{color:#de350b}<AddtlRsnInf>INVALID SECURITIES TRANSACTION TYPE.</AddtlRsnInf>{color}* 

When checking sese.023, we noticed that ""Securities Transaction Type"" is missing. 
In other messages, it is populated like => 

<ns0:SctiesTxTp> 
<ns0:Cd>CORP</ns0:Cd> 
</ns0:SctiesTxTp> 

Please check! 
Thanks, Bert 

*Please note the same applies for Ad Hoc Booking SECU! (sese.023 attached)* 

[This ticket was automatically created]",mrgr ad hoc booking cash generated payment failed due incorrect sese see ccsys rejection following rejection reason colordebaddtlrsninfinvalid security transaction typeaddtlrsninfcolor checking sese noticed security transaction type missing message populated like nssctiestxtp nscdcorpnscd nssctiestxtp please check thanks bert please note applies ad hoc booking secu sese attached ticket automatically created,-0.322947995737195,0.0,350,0.0029282996948181888,0.33073699893429875,1.***************,0.2,0.15,0.15,74.61%
Information Request,OEKB-6049,Medium,Client Reporting SLA in M12 TEST,Valdes ELIZABEHT,2024-06-13 14:34:00,2024-06-14 11:48:00,NO,"Dear All,  

I just wanted to know if the Client Reporting SLA's dedicated to OeNB are already captured in M12 TEST also? 

I'm not able at the moment to have a look at it as the application is down.  

  

Please let me know as we wuld need to make some TESTS also in M12 TEST.  

Thanks & KR,  

Eli 

 ",dear wanted know client reporting slas dedicated oenb already captured test also im able moment look application please let know wuld need make test also test thanks kr eli,0.0877325776964426,0.0,354,0.0027394448187683684,0.22806685557588935,0.*****************,0.012503434490811616,0.1,0.06,10.09%
Incident,OEKB-6048,Medium,"M12-I5: Manual Creation BONU Event - ""Is Subject to Market Claim"" unclear",Automation test,2024-06-13 09:04:00,2024-06-13 09:38:00,NO,"When creating a new BONU event manually, I noticed the field ""Is Subject to Market Claim"" => 

!image-2024-06-13-08-55-19-934.png|thumbnail! 

Please let me know, if this is an internal field only and/or if it can be skipped? The user should not take any action regarding generation of Market Claims. 

Thanks, Bert 


[This ticket was automatically created]",creating new bonu event manually noticed field subject market claim imagepngthumbnail please let know internal field andor skipped user take action regarding generation market claim thanks bert ticket automatically created,0.028202010318636894,0.0,354,0.0027394448187683684,0.24294949742034078,1.***************,0.2,0.1,0.15,53.94%
Incident,OEKB-6047,Major,M12-I2: DVCA0000001137 - Repair Invalid Market Payment,Automation test,2024-06-12 17:13:00,2024-06-13 15:28:00,NO,"test with DVCA0000001137 

Hello,  

the above event was created using MT564 Swift upload. 

I have now imported the MT566 but it has not been allocated to the above event, instead it can be found under ""repair invalid market payment"".  

The error description is as follows: 

No option specified 

No event specified 

  

Please check! 

  

Thank you and BR, Dalibor 

  


[This ticket was automatically created]",test dvca hello event created using mt swift upload imported mt allocated event instead found repair invalid market payment error description follows option specified event specified please check thank br dalibor ticket automatically created,-0.07347322441637516,0.0,354,0.0027394448187683684,0.2683683061040938,1.***************,0.2,0.13,0.15,62.26%
Incident,OEKB-6045,Major,M12-I5: SRD II - system created an update without done any changes,Automation test,2024-06-12 13:33:00,2024-06-24 15:40:00,NO,"Event PROX0000001205 

I created this event and sent out seev.001 messages. Afterwards I sent a seev.002 cancellation to MegaCor and noticed that no e-mail-alert has been created (see bug OEKB-6044). 

Nevertheless I wanted to cancel the event to create a seev.002 to the client. Unfortunately it was not possible as the buttons are grey now: 
!image-2024-06-12-13-27-42-007.png! 

In compare screen I just found that the status has been changed: 
!image-2024-06-12-13-28-56-068.png! 

but I haven't done any action which should be validated. 

This is a strange behaviour from the system - please analyze it as I am not able to cancel this event now without validating a change I haven't done. 

Thanks, stefan 


[This ticket was automatically created]",event prox created event sent seev message afterwards sent seev cancellation megacor noticed emailalert created see bug oekb nevertheless wanted cancel event create seev client unfortunately possible button grey imagepng compare screen found status changed imagepng havent done action validated strange behaviour system please analyze able cancel event without validating change havent done thanks stefan ticket automatically created,-0.3055843412876129,12.0,355,0.0026941657785782246,0.32639608532190323,1.***************,0.2,0.13,0.15,70.96%
Incident,OEKB-6044,Medium,M12-I5: SRD II - no e-mail-alert has been created for incoming seev.002,Automation test,2024-06-12 13:23:00,2024-06-12 14:04:00,NO,"Yesterday, an e-mail-alert has been created for event PROX0000001136. 

Today for event PROX0000001205 it did not work - a seev.002 has been imported into MegaCor but no alert has been created. 

Please check what happened this time. 

Thanks, stefan 


[This ticket was automatically created]",yesterday emailalert created event prox today event prox work seev imported megacor alert created please check happened time thanks stefan ticket automatically created,0.025019077584147453,0.0,355,0.0026941657785782246,0.24374523060396314,1.***************,0.2,0.1,0.15,54.06%
Information Request,OEKB-6043,Major,M12-I5: PCAL0000001194 - MT564 Out - Generation Failed,Automation test,2024-06-12 12:48:00,2024-06-12 12:53:00,NO,"test with PCAL0000001194 

The MT564 messages (NEWM and REPE) have the status ‘generation failed’. 

The reason in megabroker is as follows: 

PALM-15007: The length of the value (24.8565965583175) is more then the expected: (15). The expected pattern is (15d) 

  

that probably has something to do with Ticket OEKB-6042 

  

Please check! 

  

Thank you and BR, Dalibor  

  


[This ticket was automatically created]",test pcal mt message newm repe status generation failed reason megabroker follows palm length value expected expected pattern probably something ticket oekb please check thank br dalibor ticket automatically created,0.0036899633705615997,0.0,355,0.0026941657785782246,0.2490775091573596,1.***************,0.2,0.13,0.06,45.86%
Incident,OEKB-6042,Major,M12-I5: PCAL Event - Redemption Rate,Automation test,2024-06-12 12:13:00,2024-06-14 20:29:00,NO,"test with PCAL0000001194 

Hello,  

the number of entered digits (redemption rate) exceeds the required lenght. 

The maximal lenght should be 15 (M10). My input was 17 and it has been shortened to 16. 

In addition, a warning should always appear when this happens and saving should not be possible. 

  

Thank you and BR, Dalibor 

  


[This ticket was automatically created]",test pcal hello number entered digit redemption rate exceeds required lenght maximal lenght input shortened addition warning always appear happens saving possible thank br dalibor ticket automatically created,-0.013344734907150269,2.0,355,0.0026941657785782246,0.25333618372678757,1.***************,0.2,0.13,0.15,60.0%
Incident,OEKB-6039,Critical,M12-I5: CONV0000001164 - Market Payments fail due to incorrect sese.023,Automation test,2024-06-12 10:13:00,2024-06-12 11:43:00,NO,"CONV0000001164 
Several sese.023 for Market Payments (attached) have been rejected by CCSYS. 
When checking sese.023, I noticed that 

* DCA of Paying Agent is wrong (CATEURBAWAATWWXXXT2SEUR instead of CATEURBAWAATWWXXXT2S) as reported in ticket OEKB-6029) 

* SttlmQty is missing (when there is only cash involved, this sequence - afaik - should be populated as: 
<ns0:SttlmQty> 
<ns0:Qty> 
<ns0:Unit>0</ns0:Unit> 
</ns0:Qty> 
</ns0:SttlmQty> 

Please also note that the missing SttlmQty also was the reason for the failed sese.024 reported in ticket OEKB-6028! 

Please check! 
Thanks, Bert 




[This ticket was automatically created]",conv several sese market payment attached rejected ccsys checking sese noticed dca paying agent wrong cateurbawaatwwxxxtseur instead cateurbawaatwwxxxts reported ticket oekb sttlmqty missing cash involved sequence afaik populated nssttlmqty nsqty nsunitnsunit nsqty nssttlmqty please also note missing sttlmqty also reason failed sese reported ticket oekb please check thanks bert ticket automatically created,-0.7939506061375141,0.0,355,0.0026941657785782246,0.44848765153437853,1.***************,0.2,0.15,0.15,92.27%
Information Request,OEKB-6037,Major,M12-I5: INTR Event - Missing INTR Event,Automation test,2024-06-11 16:43:00,2024-06-12 13:15:00,YES,"Hello, 

can you please check why no INTR event has been created in AT0000A33SH3. 

The first coupon would be on 23.05.2024 and the holdings are available. 

  

Thank you and BR, Dalibor 


[This ticket was automatically created]",hello please check intr event created atash first coupon would holding available thank br dalibor ticket automatically created,0.026549402624368668,0.0,356,0.002649635135094777,0.24336264934390783,1.***************,0.2,0.13,0.06,45.0%
Incident,OEKB-6036,Major,"M12-I5: CONV0000001164 - Payment Status still ""WaitingInstructionConfirmation"" although already settled",Automation test,2024-06-11 12:45:00,2024-06-11 13:11:00,NO,"CONV0000001164 
Client Payments have been generated. 
Instructions are settled in CCSYS since ~30 minutes, 
sese.025 received in MegaBroker without error => 

!image-2024-06-11-12-41-47-446.png|thumbnail! 

However, client payments are still in status ""WaitingInstructionConfirmation"" => 

!image-2024-06-11-12-42-32-494.png|thumbnail! 

Could you please check? 
Thanks, Bert 




[This ticket was automatically created]",conv client payment generated instruction settled ccsys since minute sese received megabroker without error imagepngthumbnail however client payment still status waitinginstructionconfirmation imagepngthumbnail could please check thanks bert ticket automatically created,0.014658475294709206,0.0,356,0.002649635135094777,0.2463353811763227,1.***************,0.2,0.13,0.15,58.95%
Incident,OEKB-6035,Medium,M12 ECMS: Internal error when creating event,Automation test,2024-06-11 12:23:00,2024-06-11 19:24:00,NO,"When creating an MCAL event the following error occurs after pasting an ISIN: 

!image-2024-06-11-12-20-03-271.png! 

I tried it three times, the third time with another ISIN - same error. 

As a result - the ISIN and description in the SECURITY OUT area is not filled in automatically: 
!image-2024-06-11-12-21-45-921.png! 

Please check that - thanks. 

stefan 


[This ticket was automatically created]",creating mcal event following error occurs pasting isin imagepng tried three time third time another isin error result isin description security area filled automatically imagepng please check thanks stefan ticket automatically created,-0.0966144148260355,0.0,356,0.002649635135094777,0.2741536037065089,1.***************,0.2,0.1,0.15,58.62%
Incident,OEKB-6034,Major,M12-I5: SRD II - seev.002 IN rejected in MegaCor,Automation test,2024-06-11 11:13:00,2024-06-11 11:53:00,YES,"Event PROX0000001136 

After fixing of OEKB-6033 I could import the seev.002 into MegaCor, unfortunately no e-mail alert has been created for that. I found a dashboard alert for this SWIFT with following reason: 

!image-2024-06-11-11-09-40-573.png! 

Why has this seev.002 been rejected? What does it mean as we do not know this status for a seev.002. 

Further: Whe is Sender and Receiver the same BIC? As you can see in the SWIFT, the receiver is OCSD and not ESES, so the error description seems not to be correct. 

Please clarify this matter that a cancellation can be processed. 

Thanks, stefan 


[This ticket was automatically created]",event prox fixing oekb could import seev megacor unfortunately email alert created found dashboard alert swift following reason imagepng seev rejected mean know status seev whe sender receiver bic see swift receiver ocsd es error description seems correct please clarify matter cancellation processed thanks stefan ticket automatically created,-0.05226600542664528,0.0,356,0.002649635135094777,0.2630665013566613,1.***************,0.2,0.13,0.15,61.46%
Incident,OEKB-6033,Major,M12-I5: SRD II seev.002 IN failed in MegaBroker,Automation test,2024-06-10 16:38:00,2024-06-10 21:56:00,NO,"Event PROX0000001136 

attached seev.002 failed in MegaBroker due to following error: 

!image-2024-06-10-16-35-28-381.png! 

Please check what the problem is with that as this already worked (at least in old M12 environment). 

Thanks, stefan 


[This ticket was automatically created]",event prox attached seev failed megabroker due following error imagepng please check problem already worked least old environment thanks stefan ticket automatically created,-0.1046379879117012,0.0,357,0.0026058405184084983,0.2761594969779253,1.***************,0.2,0.13,0.15,63.42%
Incident,OEKB-6032,Major,M12-I5: INTR Event - Create Breakdown Instruction,Automation test,2024-06-10 15:49:00,2024-06-14 20:31:00,NO,"test with INTR0000000987 

I wanted to enter an instruction manually, but I noticed that you can only continue in another field if you enter the ‘Main Ref’. The ISIN is not accepted.  

  

Please check! 

  

Thank you and BR, Dalibor 


[This ticket was automatically created]",test intr wanted enter instruction manually noticed continue another field enter main ref isin accepted please check thank br dalibor ticket automatically created,0.016017351299524307,4.0,357,0.0026058405184084983,0.24599566217511892,1.***************,0.2,0.13,0.15,58.9%
Incident,OEKB-6031,Major,M12-I5: SRD II - seev.047 OUT failed in Alliance,Automation test,2024-06-10 15:45:00,2024-06-12 12:51:00,YES,"Event SHDS0000000924 and SHDS0000000925 

the seev.047 OUT was rejected by alliance due to following errors (here you find comparisons from M10 to M12): 

1) AcctSvcr LEI is missing: 
!image-2024-06-10-15-38-42-209.png! 

2) LglPrsn LEI is missing: 
!image-2024-06-10-15-39-40-321.png! 

  

3) ShrhldgTp is missing at ShrhldgBal part: 
!image-2024-06-10-15-52-34-510.png! 

Please check why this is missing - we had this problem already longer time ago. 

Thanks, stefan 

  

[This ticket was automatically created]",event shds shds seev rejected alliance due following error find comparison acctsvcr lei missing imagepng lglprsn lei missing imagepng shrhldgtp missing shrhldgbal part imagepng please check missing problem already longer time ago thanks stefan ticket automatically created,-0.6280883997678757,1.0,357,0.0026058405184084983,0.4070220999419689,1.***************,0.2,0.13,0.15,83.05%
Incident,OEKB-6030,Critical,M12-I5: Fund Distribution in EUR - Repair Invalid Market Payment,Automation test,2024-06-10 14:23:00,2024-06-11 18:59:00,YES,"test with DVCA0000000981 

Hello, 

The above event was created automatically (IFAS Files) and had the status ‘Waiting Payment’ on 10/06/2024 (payment date).  

I also received an MT566 message from 3i, but it can be found under ‘Repair Invalid Market Payment’. 

The error message is as follows: ""No option specified"" and ""No event specified"". 

  

Could you please check why this is the case? 

  

Thank you and BR, Dalibor 


[This ticket was automatically created]",test dvca hello event created automatically ifas file status waiting payment payment date also received mt message found repair invalid market payment error message follows option specified event specified could please check case thank br dalibor ticket automatically created,-0.1132423710078001,1.0,357,0.0026058405184084983,0.27831059275195,1.***************,0.2,0.15,0.15,66.75%
Incident,OEKB-6029,Critical,M12-I5: EXOF0000001117 - Market Payment (Paying Agent) contains incorrect DCA,Stefan RIBISCH,2024-06-10 11:42:00,2024-06-14 20:33:00,NO,"EXOF0000001117 
Market Payments CASH were generated against Paying Agent 222100. 
Payments have been rejected in T2S/CCSYS SIMU => 

!image-2024-06-10-11-30-43-041.png! 

When checking sese.023 (attached), I noticed that the DCA is incorrect (it includes EUR at the end) => 

!image-2024-06-10-11-31-51-834.png! 

although EUR-DCA is set up this way => 

!image-2024-06-10-11-32-48-762.png! 

Could you please check? 
Thanks, Bert",exof market payment cash generated paying agent payment rejected tsccsys simu imagepng checking sese attached noticed dca incorrect includes eur end imagepng although eurdca set way imagepng could please check thanks bert,0.016560623422265053,4.0,357,0.0026058405184084983,0.24585984414443374,0.*****************,0.*****************,0.15,0.15,35.0%
Information Request,OEKB-6028,Major,M12: FlowIns are not processed since 9:25,Stefan RIBISCH,2024-06-10 11:13:00,2024-06-10 13:00:00,NO,"I wanted to import a seev.001 message but no event has been created. 

I checked this in MegaBroker and found out that no FlowIn has been processed since 9:25: 

!image-2024-06-10-11-10-51-348.png! 

Further I noticed that according to MegaBroker there has been not even one FlowOut since the same time 9:25. 

Maybe there is also a connection to Bert's ticket OEKB-6027? 

Please check urgently as this blocks our tests. 

Thanks, stefan",wanted import seev message event created checked megabroker found flowin processed since imagepng noticed according megabroker even one flowout since time maybe also connection berts ticket oekb please check urgently block test thanks stefan,0.07164953276515007,0.0,357,0.0026058405184084983,0.23208761680871248,0.*****************,0.*****************,0.13,0.06,16.44%
Incident,OEKB-6027,Critical,M12-I5: DVOP0000001085 - Payments not processed?,Stefan RIBISCH,2024-06-10 10:59:00,2024-06-10 13:36:00,NO,"DVOP0000001085 
Market and Client Payments should have been generated. 
However, they are not available in CCSYS and also cannot be viewed => 

!image-2024-06-10-10-48-14-304.png! 

Could you please check? 
Thanks, Bert",dvop market client payment generated however available ccsys also viewed imagepng could please check thanks bert,0.018053462728857994,0.0,357,0.0026058405184084983,0.2454866343177855,0.*****************,0.*****************,0.15,0.15,34.95%
Incident,OEKB-6026,Major,M12-I5: RHDI0000001078 - Market Claims - Release of CSD Hold failed,Stefan RIBISCH,2024-06-10 09:30:00,2024-06-14 20:33:00,YES,"RHDI0000001078 
is in status ""Confirmed"" since 07/06/2024. 

When checking Market Claims in CCSYS, I noticed that the release of the CSD Hold did not take place => 

!image-2024-06-10-09-10-23-317.png! 

Could you please check if confirmed events (as in M10) are still reported to CCSYS in order to release the hold? 

Thanks, Bert",rhdi status confirmed since checking market claim ccsys noticed release csd hold take place imagepng could please check confirmed event still reported ccsys order release hold thanks bert,-0.03611411340534687,4.0,357,0.0026058405184084983,0.2590285283513367,0.*****************,0.*****************,0.13,0.15,33.98%
Incident,OEKB-6025,Major,M12-I5: SPLF0000001082 - Entitlements not calculated,Stefan RIBISCH,2024-06-10 09:28:00,2024-06-14 20:34:00,NO,"SPLF0000001082 
was set up with Record Date and Ex Date 07/06/2024 => 

!image-2024-06-10-08-59-09-680.png! 

Although there are entitled positions, no Entitlements have been calculated via job. 

Could you please check! 
Thanks, Bert",splf set record date ex date imagepng although entitled position entitlement calculated via job could please check thanks bert,0.032561931759119034,4.0,357,0.0026058405184084983,0.*****************,0.*****************,0.*****************,0.13,0.15,31.4%
Incident,OEKB-6024,Major,M12-I5: CAPD0000000869 - Email-Report-566,Stefan RIBISCH,2024-06-10 08:57:00,2024-07-06 11:42:00,YES,"test with CAPD0000000869 

Hello, 

Client 220500 has also received a ‘Corporate Action Confirmation’ by e-mail. I have noticed that some information are incorrect or missing. 

For example: Securities Account Name, Event Name, Sese.025 Transaction ID is missing,  

I have also attached an example from M10 so that you can compare it with M12. 

  

Please check. 

  

Thank you and BR, Dalibor",test capd hello client also received corporate action confirmation email noticed information incorrect missing example security account name event name sese transaction id missing also attached example compare please check thank br dalibor,-0.****************,26.0,357,0.0026058405184084983,0.****************,0.*****************,0.*****************,0.13,0.15,38.04%
Incident,OEKB-6021,Major,M12-I5: CAPD0000000869 - MT566 OUT Line :20C:,Stefan RIBISCH,2024-06-07 13:53:00,2024-06-29 15:12:00,YES,"test with CAPD0000000869 

After sending MT566 NEWM i have noticed that the field :20C::RELA//CS0000003721 is missing. 

  

Please check! 

  

Thank you and BR, Dalibor",test capd sending mt newm noticed field crelacs missing please check thank br dalibor,-0.*****************,22.0,360,0.0024787521766663585,0.*****************,0.*****************,0.*****************,0.13,0.15,47.05%
Incident,OEKB-6020,Major,M12-I5: EXWA0000001075 - Record Date is missing,Stefan RIBISCH,2024-06-07 13:52:00,2024-06-14 20:35:00,NO,"test with EXWA0000001075 

Hello,  

the above EXWA Event was created via Excel import.  

The following data was entered in Excel: 

Source: 65200 

ISIN: AT0000A1ZGW6 

Currency: HUF 

ExDate: 07.06.2024 

PayDate: 07.06.2024 

Price: 120 

This data was correctly transferred to the M12. 

Unfortunately, the ‘Record Date’ field is empty. In M10, the ‘Record Date’ field is filled automatically. 

  

Please check! 

  

Thank you and BR, Dalibor",test exwa hello exwa event created via excel import following data entered excel source isin atazgw currency huf exdate paydate price data correctly transferred unfortunately record date field empty record date field filled automatically please check thank br dalibor,-0.012480927631258965,7.0,360,0.0024787521766663585,0.25312023190781474,0.*****************,0.*****************,0.13,0.15,33.09%
Incident,OEKB-6018,Medium,M12-I5: EXWA0000001075 - Event in Status Crosschecked and not Activated,Gregor WILDING,2024-06-06 14:10:00,2024-06-06 16:41:00,YES,"test with EXWA0000001075 

Hello,  

the above EXWA Event was created via Excel import but the ""Status"" did not change automatically from ""Crosschecked"" to ""Activated"" as intended. 

Normally, an event is set to the status ‘Activated’ by an automatic process in MegaCor. This happens 5 working days before the ‘reference date’. 

Please check! 

Thank you and BR, Dalibor",test exwa hello exwa event created via excel import status change automatically crosschecked activated intended normally event set status activated automatic process megacor happens working day reference date please check thank br dalibor,-0.0265206266194582,0.0,361,0.0024377820068495625,0.25663015665486455,0.024216245168726842,0.0045885282524504465,0.1,0.15,26.68%
Incident,OEKB-6017,Major,M12-I5: RHDI0000001066 - Create Mkt Payments from Mkt Entitlements should not include Market Claim Payments,Gregor WILDING,2024-06-06 14:08:00,2024-06-06 18:41:00,NO,"RHDI0000001066 
When trying to create Market Payments via ""Create Mkt Payments from Mkt Entitlements""-button/functionality, I noticed that the result list is showing 29 different Market Payments instead of only 1 => 

!image-2024-06-06-09-28-41-155.png! 

Obviously, this list includes 28 Market Claims Payments which is not a expected behaviour. Moreover, I found out that these Market Claims Payments have been created already, so they should not be included in the list to ""Create Mkt Payments...""! 

Could you please check? 
Thanks, Bert",rhdi trying create market payment via create mkt payment mkt entitlementsbuttonfunctionality noticed result list showing different market payment instead imagepng obviously list includes market claim payment expected behaviour moreover found market claim payment created already included list create mkt payment could please check thanks bert,-0.006114894524216652,0.0,361,0.0024377820068495625,0.25152872363105416,0.024216245168726842,0.0045885282524504465,0.13,0.15,30.42%
Incident,OEKB-6016,Medium,"M12-I5: Global View - Market Claims / ""Late Trades""-button/functionality unclear",Gregor WILDING,2024-06-06 14:07:00,2024-06-07 11:20:00,NO,"When testing Market Claims, I noticed that there is a new button ""Late Trades"" => 

!image-2024-06-06-09-13-43-419.png! 

Could you please check and explain the background/functionality of this button and search criteria? 

Thanks, Bert",testing market claim noticed new button late trade imagepng could please check explain backgroundfunctionality button search criterion thanks bert,0.06255178712308407,0.0,361,0.0024377820068495625,0.23436205321922898,0.024216245168726842,0.0045885282524504465,0.1,0.15,23.34%
Incident,OEKB-6014,Critical,M12-I5: MRGR0000000871 - MT564 OUT contains unknown/not understandable OPFT indicator,Gregor WILDING,2024-06-06 07:49:00,2024-06-06 16:16:00,NO,"MRGR0000000871 
was set up with SECU option (with no cash movement at all). 
When checking MT564 OUT, I noticed that it includes an ""Option Feature Indicator"" (OPFT) with the value QCAS (""Feature whereby the holder should only instruct a cash amount"") => 

!image-2024-06-05-16-21-27-197.png! 

Could you please check? 
Thanks, Bert",mrgr set secu option cash movement checking mt noticed includes option feature indicator opft value qcas feature whereby holder instruct cash amount imagepng could please check thanks bert,0.020944010466337204,0.0,361,0.0024377820068495625,0.2447639973834157,0.024216245168726842,0.0045885282524504465,0.15,0.15,32.4%
Incident,OEKB-6013,Major,M12-I5: EXRI0000001067 - MT564 OUT includes wrong COAF in LINK sequence,Gregor WILDING,2024-06-05 15:22:00,2024-06-05 16:31:00,NO,"EXRI0000001067 
was set up with Linked CA. 
When checking MT564 OUT, I notice that the linked CORP is correct, however, there is also a LINK sequence including the COAF which is incorrect (COAF always starts with ""AT"") => 

!image-2024-06-05-14-18-29-224.png! 

Please either correct the COAF or delete the LINK containing COAF (in M10, only there is only a LINK to the CORP, afaik). 

Thanks, Bert",exri set linked ca checking mt notice linked corp correct however also link sequence including coaf incorrect coaf always start imagepng please either correct coaf delete link containing coaf link corp afaik thanks bert,-0.04152273200452328,0.0,362,0.0023974890143765195,0.2603806830011308,0.024216245168726842,0.0045885282524504465,0.13,0.15,31.75%
Incident,OEKB-6012,Critical,M12-I5: ODLT0000001058 - Freeze failed due to wrong Intended Settlement Date,Gregor WILDING,2024-06-05 15:21:00,2024-06-14 20:37:00,NO,"ODLT0000001058 
When checking the position freeze in T2S/CCSYS, I noticed that it failed, obviously, because of the wrong intended Settlement Date (which is the Pay Date of the event, not the business date) => 

!image-2024-06-05-12-47-26-025.png! 

Could you please check? 
Thanks, Bert",odlt checking position freeze tsccsys noticed failed obviously wrong intended settlement date pay date event business date imagepng could please check thanks bert,-0.4610209185630083,9.0,362,0.0023974890143765195,0.3652552296407521,0.024216245168726842,0.0045885282524504465,0.15,0.15,50.48%
Incident,OEKB-6011,Critical,M12-I5: BIDS0000000977 - MT566 OUT - :13A::LINK// missing,Gregor WILDING,2024-06-05 06:35:00,2024-06-05 09:54:00,NO,"BIDS0000000977 
When checking MT566 OUT, I noticed that 
:13A::LINK//565 
is missing in the linkage block => 

!image-2024-06-04-10-56-57-363.png! 

See example from M10 => 

!image-2024-06-04-10-57-25-645.png! 

Could you please check? 
Thanks, Bert",bid checking mt noticed alink missing linkage block imagepng see example imagepng could please check thanks bert,-0.04631971940398216,0.0,362,0.0023974890143765195,0.26157992985099554,0.024216245168726842,0.0045885282524504465,0.15,0.15,34.93%
Information Request,OEKB-6010,Major,M12-I5: SRD II SHDS - seev.046 OUT failed in MegaBroker,Stefan RIBISCH,2024-06-04 10:34:00,2024-06-04 10:45:00,NO,"Event SHDS0000000936 

seev.046 were sent out last Wednesday but they were all Technically Rejected in MegaBroker: 

!image-2024-06-04-09-58-07-657.png! 

due to following error: 

!image-2024-06-04-09-58-31-870.png! 

Please check what is the problem with that - thanks. 

BR, stefan",event shds seev sent last wednesday technically rejected megabroker imagepng due following error imagepng please check problem thanks br stefan,-0.7132176421582699,0.0,363,0.002357862006490233,0.42830441053956747,0.*****************,0.*****************,0.13,0.06,45.87%
Incident,OEKB-6008,Major,M12-I5: BIDS0000000977 - :22F::OPTF//BOIS populated twice in MT564 OUT,Automation test,2024-06-03 14:03:00,2024-06-29 15:12:00,NO,"BIDS0000000977 
was set up due to incoming MT564 containing the tag :22F::OPTF//BOIS. 

When checking outgoing MT564, I noticed that this tag is populated twice => 

!image-2024-06-03-13-59-48-774.png|thumbnail! 

Please check! 
Thanks, Bert 



[This ticket was automatically created]",bid set due incoming mt containing tag foptfbois checking outgoing mt noticed tag populated twice imagepngthumbnail please check thanks bert ticket automatically created,0.023543542250990868,26.0,364,0.0023188899754337054,0.24411411443725228,1.***************,0.2,0.13,0.15,58.62%
Incident,OEKB-6007,Major,"M12-I5: MEET0000000976 - Special character ""+"" not mapped in outgoing MT564",Automation test,2024-06-03 10:28:00,2024-06-06 17:03:00,NO,"MEET0000000976 
We often use *+* in our outgoing comment which is an allow special character. 
Today, I noticed (for the first time), that these characters have been ignored/not mapped into the outgoing 564 => 

Event data: 

!image-2024-06-03-10-19-58-797.png|thumbnail! 

564: 
!image-2024-06-03-10-23-02-889.png|thumbnail! 


Could you please check? 
Thanks, Bert 




[This ticket was automatically created]",meet often use outgoing comment allow special character today noticed first time character ignorednot mapped outgoing event data imagepngthumbnail imagepngthumbnail could please check thanks bert ticket automatically created,-0.026014888659119606,3.0,364,0.0023188899754337054,0.2565037221647799,1.***************,0.2,0.13,0.15,60.48%
Incident,OEKB-6006,Major,M12-I5: ForceCreateNewEvent failed,Automation test,2024-06-03 10:08:00,2024-06-03 10:18:00,NO,"Feed was imported with new Market Ref => 

!image-2024-06-03-10-04-32-855.png|thumbnail! 

However, when trying to force the creation of a new event, the action failed => 

!image-2024-06-03-10-02-48-911.png|thumbnail! 


Could you please check? 
Thanks, Bert 


[This ticket was automatically created]",feed imported new market ref imagepngthumbnail however trying force creation new event action failed imagepngthumbnail could please check thanks bert ticket automatically created,-0.00680297426879406,0.0,364,0.0023188899754337054,0.2517007435671985,1.***************,0.2,0.13,0.15,59.76%
Incident,OEKB-6005,Major,M12-I5: BIDS0000000922 - Client Entitlements not calculated automatically,Automation test,2024-05-29 17:33:00,2024-05-30 13:09:00,NO,"BIDS0000000922 
CASH Option was set up with ""Individual Process Of Inst"" flag set to true. 
Instruction was received and moved to status ""Created"", both Market Entitlements and Market Instruction have been created => 

!image-2024-05-29-17-28-35-618.png|thumbnail! 

!image-2024-05-29-17-28-52-516.png|thumbnail! 

After importing MT566 and validating cash payment, Market Payment is in status ""Created"" => 

!image-2024-05-29-17-30-02-763.png|thumbnail! 

When checking Client Payments, I noticed that no payments have been generated. When checking Client Entitlements, I found out that no Entitlements have been calculated automatically after the instruction moved to status ""Created"" => 


!image-2024-05-29-17-31-19-685.png|thumbnail! 



When trying to calculate Client Entitlements Elective (as a workaround), this action failed => 

!screenshot-1.png|thumbnail! 


Could you please check? 
Thanks, Bert 


[This ticket was automatically created]",bid cash option set individual process inst flag set true instruction received moved status created market entitlement market instruction created imagepngthumbnail imagepngthumbnail importing mt validating cash payment market payment status created imagepngthumbnail checking client payment noticed payment generated checking client entitlement found entitlement calculated automatically instruction moved status created imagepngthumbnail trying calculate client entitlement elective workaround action failed screenshotpngthumbnail could please check thanks bert ticket automatically created,0.0010402984917163849,0.0,368,0.0021693377695380265,0.2497399253770709,1.***************,0.2,0.13,0.15,59.46%
Incident,OEKB-6004,Major,M12-I5: DVCA Event - Dividendrate = 0,Automation test,2024-05-29 14:11:00,2024-05-31 10:03:00,YES,"test with DVCA0000000910 

The client and market entitlement have already been calculated by automatic job process and MT564REPE have been sent which should not be the case as the dividend rate is 0. 

Please check! 

  

Thank you and BR, Dalibor 


[This ticket was automatically created]",test dvca client market entitlement already calculated automatic job process mtrepe sent case dividend rate please check thank br dalibor ticket automatically created,0.005094321444630623,1.0,369,0.002133481770037708,0.24872641963884234,1.***************,0.2,0.13,0.15,59.31%
Incident,OEKB-6003,Critical,M12-I5: SRD II SHDS - seev.046 IN failed in MegaBroker,Automation test,2024-05-29 11:23:00,2024-05-29 13:39:00,NO,"SHDS0000000926 

I wanted to import a seev.046 message. Unfortunately it failed in MegaBroker due to following error: 

!image-2024-05-29-11-21-55-327.png! 

Please check why this import is not possible. 

Thanks, stefan 


[This ticket was automatically created]",shds wanted import seev message unfortunately failed megabroker due following error imagepng please check import possible thanks stefan ticket automatically created,-0.5282060578465462,0.0,369,0.002133481770037708,0.38205151446163654,1.***************,0.2,0.15,0.15,82.31%
Incident,OEKB-6002,Critical,M12-I5: SRD II SHDS - seev.045 out failed,Automation test,2024-05-29 11:13:00,2024-05-29 11:29:00,NO,"Event SHDS0000000926 

  

notifications seev.045 OUT have been created at 9:55 and have been sent at 10:15: 

!image-2024-05-29-11-07-59-812.png! Unfortunately following error occurs when I want to view the message: 

!image-2024-05-29-11-07-41-509.png! 

I searched in MegaBroker but no FlowOut has been found. 

Please check what is the problem with that as this worked already. 

Thanks, stefan 


[This ticket was automatically created]",event shds notification seev created sent imagepng unfortunately following error occurs want view message imagepng searched megabroker flowout found please check problem worked already thanks stefan ticket automatically created,-0.49464016780257225,0.0,369,0.002133481770037708,0.37366004195064306,1.***************,0.2,0.15,0.15,81.05%
Incident,OEKB-6001,Major,M12-I5: SRD II PROX - wrong time mapping,Automation test,2024-05-28 17:33:00,2024-05-28 18:32:00,NO,"Event PROX0000000909 

we had this error already - there is again a time date/time field wrong mapped: 

Vote Market Deadline 
!image-2024-05-28-17-22-36-937.png! 
!image-2024-05-28-17-22-50-111.png! 

As a consequence of that - the time is also wrong on the outgoing seev.001: 

!image-2024-05-28-18-21-02-190.png! 

Please check that again - thanks. 

stefan 

[This ticket was automatically created]",event prox error already time datetime field wrong mapped vote market deadline imagepng imagepng consequence time also wrong outgoing seev imagepng please check thanks stefan ticket automatically created,-0.2711764220148325,0.0,369,0.002133481770037708,0.3177941055037081,1.***************,0.2,0.13,0.15,69.67%
Incident,OEKB-6000,Major,M12-I5: CAPD0000000868 Notifications Out - MT566 - Menu layout,Automation test,2024-05-28 14:38:00,2024-06-01 23:26:00,NO,"test with CAPD0000000868 

Hello,  

please rename the column ‘Market Reference’ in the menu item Notifications Out => MT566/CANO/Report to ‘Main Reference’ as the displayed value (CAPD0000000868) is the Main Reference. 

  

Thank you and BR, Dalibor 


[This ticket was automatically created]",test capd hello please rename column market reference menu item notification mtcanoreport main reference displayed value capd main reference thank br dalibor ticket automatically created,0.00858796015381813,4.0,370,0.0020982184180809026,0.24785300996154547,1.***************,0.2,0.13,0.15,59.18%
Incident,OEKB-5999,Medium,M12-I5: DTCH0000000907 - MT564 REPL OUT - Unknown field :92A::ATAX,Automation test,2024-05-28 13:18:00,2024-06-01 23:27:00,NO,"DTCH0000000907 
After updating event by adding the final price, I noticed that there is an unknown field populated in the outgoing MT564 REPL (full message attached) => 

!image-2024-05-28-13-10-56-572.png|thumbnail! 

Could you please check why this field is included and/or delete it? 
Thanks, Bert 






[This ticket was automatically created]",dtch updating event adding final price noticed unknown field populated outgoing mt repl full message attached imagepngthumbnail could please check field included andor delete thanks bert ticket automatically created,-0.06869333237409592,4.0,370,0.0020982184180809026,0.267173333093524,1.***************,0.2,0.1,0.15,57.58%
Incident,OEKB-5998,Major,M12-I5: MT564 REPE Value Date,Automation test,2024-05-28 12:57:00,2024-06-01 23:27:00,NO,"test with CAPD0000000868 

The MT564REPE message was sent successfully, but please remove the :98A::VALU//20240527 line, as in M10 Value Date does not always match Paydate date. 

  

Thank you and BR, Dalibor 


[This ticket was automatically created]",test capd mtrepe message sent successfully please remove avalu line value date always match paydate date thank br dalibor ticket automatically created,-0.01654547080397606,4.0,370,0.0020982184180809026,0.254136367700994,1.***************,0.2,0.13,0.15,60.12%
Incident,OEKB-5997,Major,M12-I5: SPLR0000000872 - Cash Payment Reversal - Instructions still in status WaitingBookingCancellation,Automation test,2024-05-27 16:17:00,2024-06-14 20:39:00,NO,"SPLR0000000872 
Client Payments SECU IN, SECU OUT have been reversed/cancelled successfully. 
CASH IN reversal booking requests have been sent to SAP but are still in status ""WaitingBookingCancellation"" => 

!image-2024-05-27-15-54-27-643.png|thumbnail! 

although a SAP response was created with ""No Error"" (= ""kein Fehler"") => 

!image-2024-05-27-15-58-51-463.png|thumbnail! 

Could you please check? 
Thanks, Bert 



[This ticket was automatically created]",splr client payment secu secu reversedcancelled successfully cash reversal booking request sent sap still status waitingbookingcancellation imagepngthumbnail although sap response created error kein fehler imagepngthumbnail could please check thanks bert ticket automatically created,-0.09952609986066818,18.0,371,0.0020635379180653198,0.27488152496516705,1.***************,0.2,0.13,0.15,63.23%
Incident,OEKB-5996,Major,M12-I2: MCAL0000000870 - Reference Date and Event Status,Automation test,2024-05-24 16:14:00,2024-06-01 23:27:00,NO,"test with MCAL0000000870 

Hello,  

The above event was created using MT564NEWM Swift upload. 

The ex and record date are missing in the Swift message. However, 98A: ANOUN20230728 was used as the reference date, which in my opinion is incorrect. 

Can you please check how this is handled in M10? 

Secondly, the event has automatically switched to the ‘activated’ status. 

The ‘Activated’ status can only be achieved if the event has the status ‘Cross Checked’ and the record date and/or Extag is known, which is not the case here. 

  

Please check! 

  

Thank you and BR, Dalibor 


[This ticket was automatically created]",test mcal hello event created using mtnewm swift upload ex record date missing swift message however anoun used reference date opinion incorrect please check handled secondly event automatically switched activated status activated status achieved event status cross checked record date andor extag known case please check thank br dalibor ticket automatically created,-0.06629109941422939,8.0,374,0.001962897986236676,0.26657277485355735,1.***************,0.2,0.13,0.15,61.99%
Incident,OEKB-5995,Major,M12-I2: CAPD0000000868 - Global View - List Feeds - Missing Columns,Automation test,2024-05-24 13:48:00,2024-06-01 23:27:00,NO,"test with CAPD0000000868 

Hello, 

There are missing columns in ""List Feeds"" menu. The columns ‘Feed Source’ and ‘Status’ are missing. Could you please adapt these as in M10? 

Futhermore the title of the list (currently ""Search Result : List Received MT564/Seev.031/Seev0.35/Seev.039"") should be renamed, to ""Search Result: CA Data Feed""  

Thank you and BR, Dalibor 


[This ticket was automatically created]",test capd hello missing column list feed menu column feed source status missing could please adapt futhermore title list currently search result list received mtseevseevseev renamed search result ca data feed thank br dalibor ticket automatically created,-0.10323113575577736,8.0,374,0.001962897986236676,0.27580778393894434,1.***************,0.2,0.13,0.15,63.37%
Incident,OEKB-5994,Major,M12-I5: TEND0000000860 - MT567 IN - Impact failed,Automation test,2024-05-24 12:18:00,2024-06-01 23:28:00,NO,"TEND0000000860 
MT565 was sent to custodian. 
MT567 was imported but failed => 

!image-2024-05-24-12-12-26-021.png|thumbnail! 

Could you please check? 
Thanks, Bert 



[This ticket was automatically created]",tend mt sent custodian mt imported failed imagepngthumbnail could please check thanks bert ticket automatically created,0.03589901886880398,8.0,374,0.001962897986236676,0.241025245282799,1.***************,0.2,0.13,0.15,58.15%
Incident,OEKB-5993,Medium,M12 QAS webservice communicaion error with the REST API technology,Nikolay TSONKOV,2024-05-24 09:18:00,2024-05-24 10:03:00,NO,"Hello colleagues,  

in M12 QAS there is a technical error in the webservice with the REST Technology  

  

The issue was catched as it was reported issue in the ASOC QAS env connected to MegaCor M12 QAS  when choosing search event or notification serach , by any of those choices there was HTTP 500 Internal Server Error 

can you pls have a look  and if you need more info pls let me know 

  

2024-05-23 17:28:15,484 [https-jsse-nio-8175-exec-8] INFO  at.oekb.casa.model.webservices.RestServiceProvider - Json-Object at.oekb.casa.swcodegen.megacor.model.EventSearchParams; 

{""status"":[\\{""value"":""Activated""} 

,\{""value"":""ActivatedBlockedNonMarketData""},\{""value"":""CrossCheckedBlockedNotCritical""},\{""value"":""ActivatedBlockedCritical""},\{""value"":""CrossCheckedBlockedCritical""},\{""value"":""ActivatedBlockedNotCritical""},\{""value"":""CrossChecked""}],""pageSize"":100000,""client"":[\\{""value"":""222100""}],""pageNumber"":1,""referenceDateFrom"":""2024-05-16T00:00:00.000+02:00"",""referenceDateTo"":""2024-06-06T00:00:00.000+02:00""} 
================ 
2024-05-23 17:28:15,523 [https-jsse-nio-8175-exec-8] ERROR at.oekb.casa.model.eventsearch.EventSearch - error searching events 
at.oekb.casa.swcodegen.megacor.invoker.ApiException: eventSearch call failed with: 500 - \{""errorCode"":null,""description"":""org.hibernate.exception.GenericJDBCException: could not prepare statement"",""cause"":null,""toDo"":null,""module"":null,""messageType"":null} 

Bei der Notificationsearch kommt offenbar gar keine valide Antwort zurück: 

2024-05-23 16:19:13,812 [https-jsse-nio-8175-exec-9] INFO  at.oekb.casa.model.webservices.RestServiceProvider - Json-Object at.oekb.casa.swcodegen.megacor.model.NotificationSearchParams; 

{""pageSize"":100000,""client"":[\\{""value"":""202700""} 

],""pageNumber"":1,""creationDateFrom"":""2024-05-16T16:19:00.000+02:00"",""creationDateTo"":""2024-05-23T23:59:59.000+02:00""} 
================ 
2024-05-23 16:19:13,911 [https-jsse-nio-8175-exec-9] ERROR at.oekb.casa.model.notifications.NotificationSearch - error searching events 
at.oekb.casa.swcodegen.megacor.invoker.ApiException: com.fasterxml.jackson.databind.exc.MismatchedInputException: No content to map due to end-of-input 

 ",hello colleague qas technical error webservice rest technology issue catched reported issue asoc qas env connected megacor qas choosing search event notification serach choice http internal server error pls look need info pls let know info atoekbcasamodelwebservicesrestserviceprovider jsonobject atoekbcasaswcodegenmegacormodeleventsearchparams statusvalueactivated valueactivatedblockednonmarketdatavaluecrosscheckedblockednotcriticalvalueactivatedblockedcriticalvaluecrosscheckedblockedcriticalvalueactivatedblockednotcriticalvaluecrosscheckedpagesizeclientvaluepagenumberreferencedatefromtreferencedatetot error atoekbcasamodeleventsearcheventsearch error searching event atoekbcasaswcodegenmegacorinvokerapiexception eventsearch call failed errorcodenulldescriptionorghibernateexceptiongenericjdbcexception could prepare statementcausenulltodonullmodulenullmessagetypenull bei der notificationsearch kommt offenbar gar keine valide antwort zurck info atoekbcasamodelwebservicesrestserviceprovider jsonobject atoekbcasaswcodegenmegacormodelnotificationsearchparams pagesizeclientvalue pagenumbercreationdatefromtcreationdatetot error atoekbcasamodelnotificationsnotificationsearch error searching event atoekbcasaswcodegenmegacorinvokerapiexception comfasterxmljacksondatabindexcmismatchedinputexception content map due endofinput,-0.10903294011950493,0.0,374,0.001962897986236676,0.27725823502987623,0.012479781565630135,0.0023646865936200277,0.1,0.15,29.44%
Incident,OEKB-5992,Medium,"M12-I5: Errors on the ""Close CA"" tab",Automation test,2024-05-23 18:08:00,2024-05-24 18:26:00,NO,"Please find attached a document regarding errors on the ""Close CA"" tab 


[This ticket was automatically created]",please find attached document regarding error close ca tab ticket automatically created,-0.05789061635732651,1.0,374,0.001962897986236676,0.2644726540893316,1.***************,0.2,0.1,0.15,57.17%
Incident,OEKB-5991,Medium,"M12-I5: Errors on the ""Archive CA"" tab",Automation test,2024-05-23 18:08:00,2024-06-01 23:28:00,YES,"Please find attached a document regarding errors on the ""Archive CA"" tab 


[This ticket was automatically created]",please find attached document regarding error archive ca tab ticket automatically created,-0.030133631080389023,9.0,374,0.001962897986236676,0.25753340777009726,1.***************,0.2,0.1,0.15,56.13%
Incident,OEKB-5990,Medium,"M12-I5: Errors on the ""Cancel CA by reason"" tab",Automation test,2024-05-23 17:58:00,2024-06-01 23:29:00,YES,"Please find attached a document regarding errors on the ""Cancel CA by reason"" tab 


[This ticket was automatically created]",please find attached document regarding error cancel ca reason tab ticket automatically created,-0.13134991005063057,9.0,374,0.001962897986236676,0.28283747751265764,1.***************,0.2,0.1,0.15,59.93%
Incident,OEKB-5989,Medium,"M12-I5: Errors on the ""Cancel CA"" tab",Automation test,2024-05-23 17:28:00,2024-05-23 18:23:00,YES,"Please find attached a document regarding errors on the ""Cancel CA"" tab 


[This ticket was automatically created]",please find attached document regarding error cancel ca tab ticket automatically created,-0.20733493566513062,0.0,374,0.001962897986236676,0.30183373391628265,1.***************,0.2,0.1,0.15,62.78%
Incident,OEKB-5988,Medium,"M12-I5: Errors on the ""Activate CA"" tab",Automation test,2024-05-23 17:23:00,2024-06-01 23:29:00,NO,"Please find attached a document regarding errors on the ""Activate CA"" tab 


[This ticket was automatically created]",please find attached document regarding error activate ca tab ticket automatically created,-0.010764839127659798,9.0,374,0.001962897986236676,0.25269120978191495,1.***************,0.2,0.1,0.15,55.4%
Incident,OEKB-5987,Medium,"M12-I5: Errors on the ""Cross Check CA"" tab",Automation test,2024-05-23 17:18:00,2024-05-24 10:51:00,NO,"Please find attached a document regarding errors on the ""Cross Check CA"" tab 


[This ticket was automatically created]",please find attached document regarding error cross check ca tab ticket automatically created,-0.04352056235074997,0.0,374,0.001962897986236676,0.2608801405876875,1.***************,0.2,0.1,0.15,56.63%
Incident,OEKB-5986,Medium,"M12-I5: M12-I5: Errors on the ""Internal Comment by Event"" tab",Automation test,2024-05-23 17:09:00,2024-05-24 19:08:00,YES,"Please find attached a document regarding errors on the ""Internal Comment by Event"" tab 


[This ticket was automatically created]",please find attached document regarding error internal comment event tab ticket automatically created,-0.06575681269168854,1.0,374,0.001962897986236676,0.26643920317292213,1.***************,0.2,0.1,0.15,57.47%
Incident,OEKB-5985,Major,M12-I5: DECR - MT566 sent to 3i,Automation test,2024-05-23 16:28:00,2024-05-23 16:41:00,YES,"test with DECR0000000839 

Hello, 

the above Event is in status ""confirmed"" but I have noticed that the MT566 message sent to 3i is in status ‘Waiting Ack’. 

  

Please check! 

  

Thank you and BR, Dalibor 

  


[This ticket was automatically created]",test decr hello event status confirmed noticed mt message sent status waiting ack please check thank br dalibor ticket automatically created,0.0010196864604949951,0.0,375,0.0019304541362277093,0.24974507838487625,1.***************,0.2,0.13,0.15,59.46%
Incident,OEKB-5984,Medium,"M12-I5: M12-I5: Errors on the ""Custodian SLA for rights"" tab",Automation test,2024-05-23 16:28:00,2024-06-01 23:30:00,YES,"Please find attached a document regarding errors on the ""Custodian SLA for rights"" tab 


[This ticket was automatically created]",please find attached document regarding error custodian sla right tab ticket automatically created,-0.017396355047822,9.0,375,0.0019304541362277093,0.2543490887619555,1.***************,0.2,0.1,0.15,55.65%
Incident,OEKB-5983,Major,M12-I5: Seev.0035 – AcctsListAndBalDtls incorrect,Automation test,2024-05-23 15:38:00,2024-05-23 17:38:00,NO,"seev.0035 – AcctsListAndBalDtls is incorrect resp. differs to seev.031 


[This ticket was automatically created]",seev acctslistandbaldtls incorrect resp differs seev ticket automatically created,0.015749186277389526,0.0,375,0.0019304541362277093,0.24606270343065262,1.***************,0.2,0.13,0.15,58.91%
Incident,OEKB-5982,Major,M12-I5: Seev.0035 – MsgDefIdr incorrect,Automation test,2024-05-23 15:33:00,2024-05-23 16:59:00,NO,"Seev.0035 – MsgDefIdr is incorrect 


[This ticket was automatically created]",seev msgdefidr incorrect ticket automatically created,0.0006392281502485275,0.0,375,0.0019304541362277093,0.24984019296243787,1.***************,0.2,0.13,0.15,59.48%
Incident,OEKB-5981,Medium,"M12-I5: Errors on the ""US Tax reporting"" tab",Automation test,2024-05-23 14:58:00,2024-06-01 23:34:00,NO,"Please find attached a document regarding errors on the ""US Tax reporting"" tab 


[This ticket was automatically created]",please find attached document regarding error u tax reporting tab ticket automatically created,-0.12463094666600227,9.0,375,0.0019304541362277093,0.28115773666650057,1.***************,0.2,0.1,0.15,59.67%
Incident,OEKB-5980,Medium,"M12-I5: Errors on the ""List Received Screen"" tab",Automation test,2024-05-23 14:28:00,2024-06-01 23:34:00,YES,"Please find attached a document regarding errors on the ""List Received Screen"" tab 


[This ticket was automatically created]",please find attached document regarding error list received screen tab ticket automatically created,-0.11517726443707943,9.0,375,0.0019304541362277093,0.27879431610926986,1.***************,0.2,0.1,0.15,59.32%
Information Request,OEKB-5979,Major,M12-I5: SOFF0000000826 - No Market Claims detected / Pend Trade Interface Processing failed,Automation test,2024-05-23 14:10:00,2024-05-24 10:10:00,NO,"SOFF0000000826 
I set up instructions in CCSYS SIMU in order to test Market Claim detection. 
When checking the Pend Trade Interface Processing, I noticed that it has failed (obviously since 06/05/2024) => 

!image-2024-05-23-13-58-52-445.png|thumbnail! 

!image-2024-05-23-13-58-15-518.png|thumbnail! 

Could you please check? 
Thanks, Bert 





[This ticket was automatically created]",soff set instruction ccsys simu order test market claim detection checking pend trade interface processing noticed failed obviously since imagepngthumbnail imagepngthumbnail could please check thanks bert ticket automatically created,-0.6080895103514194,0.0,375,0.0019304541362277093,0.40202237758785486,1.***************,0.2,0.13,0.06,68.8%
Incident,OEKB-5978,Medium,"M12-I5: Errors on the ""Repair Received Screen"" tab",Automation test,2024-05-23 14:10:00,2024-06-01 23:31:00,YES,"Please find attached a document regarding errors on the ""Repair Received Screen"" tab 


[This ticket was automatically created]",please find attached document regarding error repair received screen tab ticket automatically created,-0.12485421635210514,9.0,375,0.0019304541362277093,0.2812135540880263,1.***************,0.2,0.1,0.15,59.68%
Incident,OEKB-5977,Critical,M12-I5: SOFF0000000826 - Market Payment failed,Automation test,2024-05-23 14:10:00,2024-05-23 15:57:00,YES,"SOFF0000000826 
Market Payment was generated (sese.023 sent), however, a sese.024 has been received with the following rejection information (full message attached) => 

<PrcgSts> 
<Rjctd> 
<Rsn> 
<Cd> 
<Cd>OTHR</Cd> 
</Cd> 
*_ <AddtlRsnInf>INCOMPLETE COMPULSORY SETTLEMENT PARTIES</AddtlRsnInf>_* 
</Rsn> 
</Rjctd> 
</PrcgSts> 

Could you please check? 
Thanks, Bert 


[This ticket was automatically created]",soff market payment generated sese sent however sese received following rejection information full message attached prcgsts rjctd rsn cd cdothrcd cd addtlrsninfincomplete compulsory settlement partiesaddtlrsninf rsn rjctd prcgsts could please check thanks bert ticket automatically created,0.003030303865671158,0.0,375,0.0019304541362277093,0.2492424240335822,1.***************,0.2,0.15,0.15,62.39%
Incident,OEKB-5976,Cosmetic,M12-I5: MEET0000000833 - Market Instruction Cancellation (MT565 CANC) failed,Automation test,2024-05-23 09:53:00,2024-05-23 13:14:00,NO,"MEET0000000833 
When testing a cancellation of an Market Instruction, the system tried to generate the MT565 CANC, but with no success => 

!image-2024-05-23-09-49-19-772.png|thumbnail! 

The file shows the following error => 

*_Cannot invoke ""[B.clone()"" because ""content"" is null_* 

Could you please check! 
Thanks, Bert 





[This ticket was automatically created]",meet testing cancellation market instruction system tried generate mt canc success imagepngthumbnail file show following error invoke bclone content null could please check thanks bert ticket automatically created,-0.013363324105739594,0.0,375,0.0019304541362277093,0.2533408310264349,1.***************,0.2,0.1,0.15,55.5%
Information Request,OEKB-5974,Medium,M12-I5: DVCA: Entitlement Notification did not happen via Job,Automation test,2024-05-22 14:28:00,2024-05-22 16:33:00,NO,"DVCA0000000832 

Event created today via MT564, RDTE yesterday, PAYD today 

I created the client entitlements at11:47: 
!image-2024-05-22-14-24-13-427.png! 

Until 13:13 no notifications MT564 REPE have been generated automatically from the system, so I had to generate the notifications manually: 
!image-2024-05-22-14-23-28-362.png! 

Please check why these notifications have not been generated automatically. 

Thanks, stefan 


[This ticket was automatically created]",dvca event created today via mt rdte yesterday payd today created client entitlement imagepng notification mt repe generated automatically system generate notification manually imagepng please check notification generated automatically thanks stefan ticket automatically created,0.009891442954540253,0.0,376,0.0018985465358918202,0.24752713926136494,1.***************,0.2,0.1,0.06,41.13%
Information Request,OEKB-5973,Critical,M12-I5: MEET0000000833 - MT565 OUT - Instruction Comment incomplete,Bertram Schon,2024-05-22 14:26:00,2024-05-22 16:19:00,NO,"Hi Thouraya, 
Sorry I had to clone OEKB-5891 after checking the 2nd outgoing MT565. 

The first one (where I input the instruction comment in a row) did work. 
The second one only includes a part of the comment (same bug as initially reported in OEKB-5891) => 

!image-2024-05-22-14-34-19-337.png! 

  

Could you please check again! 
Thanks, Bert 

 ",hi thouraya sorry clone oekb checking nd outgoing mt first one input instruction comment row work second one includes part comment bug initially reported oekb imagepng could please check thanks bert,-0.22605564258992672,0.0,376,0.0018985465358918202,0.3065139106474817,0.011010145928181883,0.0020862179624902143,0.15,0.06,27.79%
Incident,OEKB-5972,Major,M12-I5: MEET0000000833 - MT564 OUT - ADTX in wrong order,Automation test,2024-05-22 10:58:00,2024-05-30 12:35:00,NO,"MEET0000000833 
Outgoing comment was created including Meeting Agenda => 

!image-2024-05-22-10-51-27-725.png|thumbnail! 

However, the comment/Agenda in the outgoing 564 (attached) is populated in a wrong order => 

!image-2024-05-22-10-52-41-272.png|thumbnail! 

Could you please check? 
Thanks, Bert 





[This ticket was automatically created]",meet outgoing comment created including meeting agenda imagepngthumbnail however commentagenda outgoing attached populated wrong order imagepngthumbnail could please check thanks bert ticket automatically created,-0.14913077093660831,8.0,376,0.0018985465358918202,0.2872826927341521,1.***************,0.2,0.13,0.15,65.09%
Incident,OEKB-5971,Critical,M12-I5: No flowOuts for MT566 created,Automation test,2024-05-21 16:43:00,2024-05-21 17:07:00,NO,"Event DVCA0000000751 

Today I processed event DVCA0000000751 and confirmed the client payments. 
22 entries are found in MT566 OUT screen of the Global View of the event: 

!image-2024-05-21-16-39-08-204.png! but no result is in 'View Message': 
!image-2024-05-21-16-39-30-387.png! 

and also in FlowOuts in MegaBroker (the last MT566 received on 19/05): 
!image-2024-05-21-16-40-59-222.png! 

It seems that the confirmations are not really created and sent. 

Please check that - thanks. 

BR, stefan 


[This ticket was automatically created]",event dvca today processed event dvca confirmed client payment entry found mt screen global view event imagepng result view message imagepng also flowouts megabroker last mt received imagepng seems confirmation really created sent please check thanks br stefan ticket automatically created,0.04283883608877659,0.0,377,0.0018671663218015242,0.23929029097780585,1.***************,0.2,0.15,0.15,60.89%
Incident,OEKB-5970,Major,M12-I5: SWIFTs OUT are NACKED in Alliance (T40),Automation test,2024-05-21 14:11:00,2024-05-21 14:13:00,NO,"Today we have many NACKs in Alliance again, now with a new error code T40: 

!image-2024-05-21-13-47-39-426.png! 

Please check what is the problem with them - thanks. 

stefan 


[This ticket was automatically created]",today many nacks alliance new error code imagepng please check problem thanks stefan ticket automatically created,0.0022607818245887756,0.0,377,0.0018671663218015242,0.2494348045438528,1.***************,0.2,0.13,0.15,59.42%
Information Request,OEKB-5969,Medium,M12 - Again wrong business date,Stefan RIBISCH,2024-05-21 09:15:00,2024-05-21 09:29:00,NO,"Today we noticed that the Business Date is again wrong in M12 QAS: 

!image-2024-05-21-09-14-04-547.png! 

although Eli configured the holidays in the system a few weeks ago. 

Please check that - maybe there is also a context to OEKB-5968. 

Thanks, stefan 

PS: To be able to continue with tests I changed the BD now to 21/05/2024 again.",today noticed business date wrong qas imagepng although eli configured holiday system week ago please check maybe also context oekb thanks stefan p able continue test changed bd,-0.13412299752235413,0.0,377,0.0018671663218015242,0.28353074938058853,0.*****************,0.*****************,0.1,0.06,19.65%
Incident,OEKB-5968,Medium,QAS --Flow Failure Alert-- email Allert every 20 sec,Nikolay TSONKOV,2024-05-21 08:45:00,2024-05-21 12:11:00,YES,"Hello Colleagues,  

yestaday 20.05.2024 ( Monday ) was not a working day in Vienna, we reieceve on almost each 20 sec Email Notification with content 

Event Code : SingleFlowDeterminingReceiversError<br>Description : <br>Exception Date : Tue May 21 08:06:00 CEST 2024 User Name : STP<br>Thread ID : 135<br>Thread Name : MegaBrokerScheduler_Worker-9<br> 

  

Example of the email attached  

  

can you pls advice on the next steps? 

  

maybe it can be related to the deployment which happend on Saturday 18.05  on the M12 QAS env  

  

BR,  

Niki 

 ",hello colleague yestaday monday working day vienna reieceve almost sec email notification content event code singleflowdeterminingreceiverserrorbrdescription brexception date tue may cest user name stpbrthread id brthread name megabrokerschedulerworkerbr example email attached pls advice next step maybe related deployment happend saturday qas env br niki,0.019902747124433517,0.0,377,0.0018671663218015242,0.24502431321889162,0.012479781565630135,0.0023646865936200277,0.1,0.15,24.61%
Incident,OEKB-5967,Major,M12-I5: - REDM0000000646 Event - SECU booking in CCSYS not correct,Automation test,2024-05-17 14:58:00,2024-06-01 23:31:00,NO,"test with REDM0000000646 , Custodian NBB 

the payment for above REDM Event was performed by import of MT566 Swift.  

The bookings have been made and the event has a confirmed status. 

I had a look at this in CCSYS. The CASH Booking looks good.  

CS0000002595 - PFRD - Payment Free of Receive Debit (OCSD232100 (OCSDATWWXXX)) 

CS0000002593 - PFDC - Payment Free of Delivery Credit (OCSD222100 (BKAUATWWXXX)) 

The SECU booking is not correct in my opinion. 

CS0000002587 - DFP - Delivery Free of Payment (OCSD222100 (BKAUATWWXXX))=>OK 

CS0000002589 - RFP - Receive Free of Payment (OCSD232100 (OCSDATWWXXX))=>NOT OK  

The above booking means that the holding is not derecognised and 232100 now have a client position. 

I would expect the ‘RFP’ booking to specify OCSD9751200 as the securities account. 

  

Please check! 

  

Thank you and BR, Dalibor 


[This ticket was automatically created]",test redm custodian nbb payment redm event performed import mt swift booking made event confirmed status look ccsys cash booking look good c pfrd payment free receive debit ocsd ocsdatwwxxx c pfdc payment free delivery credit ocsd bkauatwwxxx secu booking correct opinion c dfp delivery free payment ocsd bkauatwwxxxok c rfp receive free payment ocsd ocsdatwwxxxnot ok booking mean holding derecognised client position would expect rfp booking specify ocsd security account please check thank br dalibor ticket automatically created,0.*****************,15.0,381,0.0017467471362611197,0.****************,1.***************,0.2,0.13,0.15,58.85%
Incident,OEKB-5966,Blocker,pending trades batch order not respected. M12 TEST,Lukasz Walczuk,2024-05-16 16:24:00,2024-05-16 16:48:00,NO,"Hello,  

When importing the historical pending trade files, we have noticed that the security mechanism is not working properly or is not implemented at all. We imported the files from 1.03.2024 to 3.03.2024 and found that the files from 2.03.204 were completely skipped (see screenshot below). M10 has this capability and this is also essential for our business continuity tests.  

Please advise on next steps.  

  

BR 

Lukasz 

!image-2024-05-16-16-21-21-136.png!",hello importing historical pending trade file noticed security mechanism working properly implemented imported file found file completely skipped see screenshot capability also essential business continuity test please advise next step br lukasz imagepng,0.014361431822180748,0.0,382,0.0017178759455574704,0.2464096420444548,0.002645083300698369,0.000501194110431838,0.14,0.15,30.54%
Incident,OEKB-5965,Major,M12-I5: CAPD0000000562 - Event in Status Paid and not Confirmed,Automation test,2024-05-16 15:18:00,2024-05-29 15:59:00,NO,"test with CAPD0000000562 

the MT566 Swift has been sent to the client but the Event is still in Status ""Paid"" and not in Status ""Confirmed"" as usual. 

Please check! 

  

Thank you and BR, Dalibor 


[This ticket was automatically created]",test capd mt swift sent client event still status paid status confirmed usual please check thank br dalibor ticket automatically created,-0.04123195819556713,13.0,382,0.0017178759455574704,0.2603079895488918,1.***************,0.2,0.13,0.15,61.05%
Incident,OEKB-5964,Medium,"M12: Missing buttons on ""Rejected Market Cash Bookings"" screen",Automation test,2024-05-16 11:58:00,2024-05-18 10:37:00,NO,"The rejected market payment reported in OEKB-5963 created also correctly a dashboard alert. 

Unfortunately we have no possibility to resend this entry: 

!image-2024-05-16-11-55-06-532.png! 

In M10 there are the following buttons: 

!image-2024-05-16-11-56-09-915.png! 

Is there a new process in M12 or are the buttons missing? Please advise how this should be handled. 

Thanks, 
stefan 


[This ticket was automatically created]",rejected market payment reported oekb created also correctly dashboard alert unfortunately possibility resend entry imagepng following button imagepng new process button missing please advise handled thanks stefan ticket automatically created,-0.012380465865135193,1.0,382,0.0017178759455574704,0.2530951164662838,1.***************,0.2,0.1,0.15,55.46%
Incident,OEKB-5963,Critical,M12-I5: BONU - market payment CashBookingRejected,Automation test,2024-05-16 11:53:00,2024-05-16 11:56:00,NO,"Event BONU0000000645 

Market Payment for fractions was created manually from Mkt Entitlement and after validating, Status changed to CashBookingRejected: 

!image-2024-05-16-11-46-04-824.png! 

We then noticed that the outgoing XML CASHINTERFACE was technically rejected in MB: 

!image-2024-05-16-11-46-51-400.png! 

Please check what is the problem with that. 

Thanks, stefan 


[This ticket was automatically created]",event bonu market payment fraction created manually mkt entitlement validating status changed cashbookingrejected imagepng noticed outgoing xml cashinterface technically rejected mb imagepng please check problem thanks stefan ticket automatically created,-0.2771134227514267,0.0,382,0.0017178759455574704,0.3192783556878567,1.***************,0.2,0.15,0.15,72.89%
Information Request,OEKB-5962,Medium,M12-I5: mismatched MT566 created market payment,Automation test,2024-05-15 15:39:00,2024-05-15 17:18:00,YES,"Event BONU0000000645 

Following Market Entitlements have been generated: 
!image-2024-05-15-15-28-34-043.png! 

An MT566 for totally 89 shares have been sent to MegaCor (you find it attached). 

A dashboard alert 'Reconciliation Mismatch with Market Security Pament' has been created but also a Market Payment in Status 'Created'. This means that a wrong amount of shares has been booked which is also seen in CCSYS: 
!image-2024-05-15-15-32-28-228.png! 

Just for information: Client Payments have not been created. 

Please check why this has happened despite a difference in the amount of securities. 

Thanks, stefan 


[This ticket was automatically created]",event bonu following market entitlement generated imagepng mt totally share sent megacor find attached dashboard alert reconciliation mismatch market security pament created also market payment status created mean wrong amount share booked also seen ccsys imagepng information client payment created please check happened despite difference amount security thanks stefan ticket automatically created,-0.03344874829053879,0.0,383,0.00168948195366259,0.2583621870726347,1.***************,0.2,0.1,0.06,42.75%
Information Request,OEKB-5961,Critical,M12-I5: SWIFTs OUT are NACKED in Alliance (E22),Stefan RIBISCH,2024-05-15 13:55:00,2024-05-15 14:02:00,YES,"As a clone of OEKB-5866 I opened this one. 

During the last days there were many SWIFTs in Status 'Technically Rejected': 

!image-2024-05-15-13-56-34-357.png! 

In Alliance they all have error code E22: 
!image-2024-05-15-13-57-19-139.png! 

Please check that - thanks. 

stefan",clone oekb opened one last day many swift status technically rejected imagepng alliance error code e imagepng please check thanks stefan,-0.052014946937561035,0.0,383,0.00168948195366259,0.26300373673439026,0.*****************,0.*****************,0.15,0.06,24.07%
Incident,OEKB-5960,Major,M12-I5: seev.001 rejected - The String Value of the field Conf Inf mustn't exceed the length 255.,Automation test,2024-05-15 09:23:00,2024-05-15 09:29:00,NO,"Current result: 

seev.001 get rejected with status ""Parsing and Mapping Failed"". Error Message: ""The String Value of the field Conf Inf mustn't exceed the length 255.""  

According to xsd, however, the maximum field length is 350 characters. 


[This ticket was automatically created]",current result seev get rejected status parsing mapping failed error message string value field conf inf mustnt exceed length according xsd however maximum field length character ticket automatically created,-0.14668577909469604,0.0,383,0.00168948195366259,0.286671444773674,1.***************,0.2,0.13,0.15,65.0%
Information Request,OEKB-5959,Medium,M12-I5: - INTR0000000595 INTR Event - Swift Issue,Dalibor VIDIC,2024-05-14 16:20:00,2024-05-16 17:40:00,NO,"Hello,  

I have created the above event via MT564 Swift but the interest rate applied is incorrect. 

I suspect that the received MT564 swift message is not correct and therefore the INTR event is not created correctly. 

The swift message contains Field 92A with rate of 100 which in my opinion is not right. 

The field :92A::INTR//0,628 and :92A::INTP//0,628 are not considered in M12. 

Can you please check? 

  

Thank you and BR, Dalibor 

 ",hello created event via mt swift interest rate applied incorrect suspect received mt swift message correct therefore intr event created correctly swift message contains field rate opinion right field aintr aintp considered please check thank br dalibor,-0.056956641376018524,2.0,384,0.001661557273173934,0.26423916034400463,0.001661557273173934,0.0003148342130624354,0.1,0.06,13.68%
Incident,OEKB-5958,Minor,M12-I5: Notifications not created on '55,Automation test,2024-05-14 15:03:00,2024-05-18 10:44:00,NO,"We noticed that the notification job again doesn't run every hour on 55 but again on 20. 

This has been already fixed with [OEKB-5852|https://jira.vermeg.com/browse/OEKB-5852] and has now changed again. 

Please check that - thanks. 

stefan 


[This ticket was automatically created]",noticed notification job doesnt run every hour already fixed oekb changed please check thanks stefan ticket automatically created,3.3080577850341797e-06,3.0,384,0.001661557273173934,0.24999917298555374,1.***************,0.2,0.07,0.15,50.5%
Incident,OEKB-5957,Major,M12-I5: - BE0001735860 REDM Event has been closed automatically,Automation test,2024-05-14 09:48:00,2024-05-14 10:05:00,NO,"ISIN BE0001735860 

I have created INTR and REDM events for the above ISIN with Swift upload and payment date 14 May 2024. 

The INTR event (INTR0000000595) is now correctly in the status ‘Waiting payment’. 

The REDM event (REDM0000000594) was closed automatically for some reason. 

  

Please check! 

  

Thank you and BR, Dalibor 


[This ticket was automatically created]",isin created intr redm event isin swift upload payment date may intr event intr correctly status waiting payment redm event redm closed automatically reason please check thank br dalibor ticket automatically created,-0.*****************,0.0,384,0.001661557273173934,0.****************,1.***************,0.2,0.13,0.15,62.57%
Incident,OEKB-5956,Major,M12-I5: Notifications Out - MT566 - Menu layout,Automation test,2024-05-10 16:15:00,2024-05-18 10:43:00,NO,"test with CAPD0000000535 

Hello,  

please make in Menu Item Notifications out / MT566/CACO/Report the same settings as in M10. 

For example, column ""Receiver BIC"" is missing or is called ‘Nostro Sec Account"" here in M12. 

In M10 this column is named "" Receiver BIC"" and is filled with BIC of the respective client. 

In addition, M12 reads ‘List received MT566’ instead of ‘List sent MT566’. 

Please check! 

  

Thank you and BR, Dalibor 

  

  

  

  


[This ticket was automatically created]",test capd hello please make menu item notification mtcacoreport setting example column receiver bic missing called nostro sec account column named receiver bic filled bic respective client addition read list received mt instead list sent mt please check thank br dalibor ticket automatically created,-0.*****************,7.0,388,0.0015543984350843032,0.****************,1.***************,0.2,0.13,0.15,60.9%
Incident,OEKB-5955,Major,M12-I5: CAPD0000000535 - Event in Status PartiallyConfirmed,Automation test,2024-05-10 15:43:00,2024-05-18 10:42:00,YES,"test with CAPD0000000535 

the event mentioned above was in Status ""Paid"" and I have sent the MT566 Swifts for both clients but now is the event in status ""PartiallyConfirmed"" and not in Status ""Confirmed"". 

I have noticed that the message to ‘3i’ is in ‘Waiting Ack’ status. 

And why does the SEME reference not start with CS, but has the number 57? 

  

Please check! 

  

Thank you and BR, Dalibor 

  

  

[This ticket was automatically created]",test capd event mentioned status paid sent mt swift client event status partiallyconfirmed status confirmed noticed message waiting ack status seme reference start c number please check thank br dalibor ticket automatically created,-0.009946469217538834,7.0,388,0.0015543984350843032,0.2524866173043847,1.***************,0.2,0.13,0.15,59.87%
Incident,OEKB-5954,Major,M12-I5: DTCH0000000560 - Maximum Quantity Of Securities mapped incorrectly,Automation test,2024-05-10 12:48:00,2024-05-10 13:10:00,NO,"DTCH0000000560 
Maximum Quantity Of Securities was set up => 

!image-2024-05-10-12-43-50-043.png|thumbnail! 

However, when checking MT564 OUT, I noticed that the data was not mapped correctly (qualifier MQSO is missing) => 

!image-2024-05-10-12-45-39-241.png|thumbnail! 

Please check! 
Thanks, Bert 




[This ticket was automatically created]",dtch maximum quantity security set imagepngthumbnail however checking mt noticed data mapped correctly qualifier mqso missing imagepngthumbnail please check thanks bert ticket automatically created,0.06512912921607494,0.0,388,0.0015543984350843032,0.23371771769598126,1.***************,0.2,0.13,0.15,57.06%
Information Request,OEKB-5951,Critical,M12 TEST - historical Pending Trades not picked up,Valdes ELIZABEHT,2024-05-08 15:56:00,2024-05-08 17:17:00,NO,"Dear All,  

I just tried together with OeKB IT to import the historical pending trades into M12 TEST.  

Before loading the whole punch we tried with a single file (Batch 1 of 01.03.2024) 

*It's still in MQ and it's not picked up by M12 TEST* - can yo uplease check if there is something missing and revert.  

  

Thanks & KR,  

Eli  

  

 ",dear tried together oekb import historical pending trade test loading whole punch tried single file batch still mq picked test yo uplease check something missing revert thanks kr eli,-0.0342157706618309,0.0,390,0.0015034391929775724,0.2585539426654577,0.*****************,0.012503434490811616,0.15,0.06,22.16%
Incident,OEKB-5950,Major,M12-I5: CAPD0000000535 - No MT564REPE sent to client,Automation test,2024-05-08 14:18:00,2024-05-18 10:42:00,NO,"test with CAPD0000000535  

Hello,  

The above event was created using MT564 import. The CAPD event was at 15:56 (07.05.2024) in Status ""Activated/Created"" and I expected the MT564NEWM and MT564REPE messages to be sent automatically by job. 

The event is now correctly in status ‘Waiting Payment’, but I have noticed that only the MT564NEWM messages have been sent and no MT564REPE messages. 

The MT564NEWM were sent this morning and not yesterday as usual. Why is that so?  

And why were the entitlements calculated this morning (07:22) and not yesterday evening as usual? 

  

Thank you and BR, Dalibor 

  

  

  

  


[This ticket was automatically created]",test capd hello event created using mt import capd event status activatedcreated expected mtnewm mtrepe message sent automatically job event correctly status waiting payment noticed mtnewm message sent mtrepe message mtnewm sent morning yesterday usual entitlement calculated morning yesterday evening usual thank br dalibor ticket automatically created,-0.022327270358800888,9.0,390,0.0015034391929775724,0.****************,1.***************,0.2,0.13,0.15,60.34%
Incident,OEKB-5948,Critical,M12-I5: SPLR0000000353 - Client Payment Reversal failed due to incorrect sese.023,Automation test,2024-05-08 09:42:00,2024-05-18 10:44:00,YES,"SPLR0000000353 
When trying to reverse Client Payments, I noticed that the reversal of SECU IN bookings was successful, however, the reversal of SECU OUT failed. 

When checking sese.023, it seems that <ns0:SfkpgAcct> (Nostro Sec Account) 
is missing in the ""Counterpart Payment Instruction Reverse"" (attached) => 

!image-2024-05-08-09-36-55-972.png|thumbnail! 

in comparison to the ""Client Payment Instruction Reverse"" => 

!image-2024-05-08-09-37-58-529.png|thumbnail! 


Could you please check? 
Thanks, Bert 






[This ticket was automatically created]",splr trying reverse client payment noticed reversal secu booking successful however reversal secu failed checking sese seems nssfkpgacct nostro sec account missing counterpart payment instruction reverse attached imagepngthumbnail comparison client payment instruction reverse imagepngthumbnail could please check thanks bert ticket automatically created,-0.*****************,10.0,390,0.0015034391929775724,0.*****************,1.***************,0.2,0.15,0.15,65.81%
Incident,OEKB-5945,Medium,M12-I5: FWD instrument deactivation - history entry missing,Automation test,2024-05-07 11:28:00,2024-06-29 15:05:00,NO,"*Current result:* 

1) Receive a file containing an ISIN to be deactivated 
=> ""Deactivation Flag"" in instrument changed to ""true"" (OK) 

2) Job ""Financial_Instruments_Automatic_Deactivation"" runs in EoD 
=> ""Is Active"" Flag changed to ""false"" (OK) 
=> Transaction not traceable in ""history"" (NOK - see screenshot) 


[This ticket was automatically created]",current result receive file containing isin deactivated deactivation flag instrument changed true ok job financialinstrumentsautomaticdeactivation run eod active flag changed false ok transaction traceable history nok see screenshot ticket automatically created,-0.09738419950008392,53.0,391,0.0014785895288500525,0.274346049875021,1.***************,0.2,0.1,0.15,58.65%
Incident,OEKB-5944,Critical,M12-I5: CONV0000000510 - Performance Issues,Automation test,2024-05-06 14:18:00,2024-05-06 15:04:00,NO,"Please note we face enormous performance issues today, especially when trying to send notifications. 

I wanted to confirm client payments for CONV0000000510 and the circle keeps spinning for around 30min (!) and is still going on... 

!image-2024-05-06-14-14-19-239.png|thumbnail! 

Could you please check? 
Thanks, Bert 

  

*UPDATE:* 
After around 45min., CA confirmations have been created for a few clients, but not for all - there are still a lot of payments which were not confirmed => 

!image-2024-05-06-15-01-49-961.png! 

  

Could you please investigate? 
Thanks, Bert 

  

[This ticket was automatically created]",please note face enormous performance issue today especially trying send notification wanted confirm client payment conv circle keep spinning around min still going imagepngthumbnail could please check thanks bert update around min ca confirmation created client still lot payment confirmed imagepng could please investigate thanks bert ticket automatically created,0.027579158544540405,0.0,392,0.0014541505935435817,0.2431052103638649,1.***************,0.2,0.15,0.15,61.47%
Incident,OEKB-5943,Critical,M12-I5: error message after send notification,Automation test,2024-05-06 12:13:00,2024-05-10 15:19:00,NO,"Event INTR0000000511 

After generation of notifiations I wanted to send out, but after clicking ""Send"" the system took very very long and the the following error message appeared: 

!image-2024-05-06-12-08-16-636.png! 

Afterwards only 9 out of 32 notifications were sent. 

Please check that - thanks. 

stefan 


[This ticket was automatically created]",event intr generation notifiations wanted send clicking send system took long following error message appeared imagepng afterwards notification sent please check thanks stefan ticket automatically created,-0.021922174841165543,4.0,392,0.0014541505935435817,0.2554805437102914,1.***************,0.2,0.15,0.15,63.32%
Incident,OEKB-5942,Critical,M12-I5: CONV0000000510 - No REPE (but REPL) sent,Automation test,2024-05-06 11:18:00,2024-05-09 13:39:00,NO,"CONV0000000510 
Entitlements have been calculated manually. 
After generating and sending notifications manually, I noticed that no REPE but REPL messages have been sent => 

!image-2024-05-06-11-16-34-983.png|thumbnail! 

Could you please check! 
Thanks, Bert 


[This ticket was automatically created]",conv entitlement calculated manually generating sending notification manually noticed repe repl message sent imagepngthumbnail could please check thanks bert ticket automatically created,0.0001756623387336731,3.0,392,0.0014541505935435817,0.24995608441531658,1.***************,0.2,0.15,0.15,62.49%
Incident,OEKB-5941,Major,M12-I5: CONV0000000510 - Client Elig Position screen - ISIN missing/new buttons with strange/no functionality,Automation test,2024-05-06 10:43:00,2024-05-08 17:15:00,NO,"CONV0000000510 
When checking Client Elig Positions, I noticed that the screen looks different than before. 
There is no ISIN anymore, also, there are two new buttons 

!image-2024-05-06-10-31-52-183.png|thumbnail! 

When trying to export, the following sheet was downloaded => 

!image-2024-05-06-10-40-27-077.png|thumbnail! 

When performing ""Impact Position"", the following message popped up => 

!image-2024-05-06-10-41-08-340.png|thumbnail! 

Could you please check? 
Thanks, Bert 





[This ticket was automatically created]",conv checking client elig position noticed screen look different isin anymore also two new button imagepngthumbnail trying export following sheet downloaded imagepngthumbnail performing impact position following message popped imagepngthumbnail could please check thanks bert ticket automatically created,-0.0027387291193008423,2.0,392,0.0014541505935435817,0.****************,1.***************,0.2,0.13,0.15,59.6%
Incident,OEKB-5940,Major,M12-I5: CONV0000000510 - Update Elig Position does not work,Automation test,2024-05-06 10:33:00,2024-05-06 15:48:00,NO,"CONV0000000510 
When trying to update ELIG position for a clients account, the following error message popped up after performing ""EDIT"" => 

!image-2024-05-06-10-28-01-943.png|thumbnail! 

Could you please check? 
Thanks, Bert 


[This ticket was automatically created]",conv trying update elig position client account following error message popped performing edit imagepngthumbnail could please check thanks bert ticket automatically created,-0.015134954825043678,0.0,392,0.0014541505935435817,0.****************,1.***************,0.2,0.13,0.15,60.07%
Incident,OEKB-5939,Critical,M12-I5: CONV0000000510 - No MT564 generated/sent for DEF accounts,Automation test,2024-05-06 10:28:00,2024-05-06 11:54:00,NO,"CONV0000000510 
In order to process transformations, MT564 to CCSYS should be sent. 
However, I noticed that no MT564 have been generated/sent for DEF accounts at all. 

Could you please check? 

Thanks, Bert 


[This ticket was automatically created]",conv order process transformation mt ccsys sent however noticed mt generatedsent def account could please check thanks bert ticket automatically created,0.014041978865861893,0.0,392,0.0014541505935435817,0.*****************,1.***************,0.2,0.15,0.15,61.97%
Incident,OEKB-5938,Critical,M12-I5: Import of MT564 not possible,Automation test,2024-05-06 10:08:00,2024-05-06 11:50:00,NO,"Today we tried already three times to import an MT564 into MegaCor unfortunately the all stay ""in process"" 

!image-2024-05-06-10-06-21-499.png! due to following error: 

!image-2024-05-06-10-05-07-478.png! 

and no event is created in Megacor. 

Please check what is the problem here. 

Thanks, stefan 


[This ticket was automatically created]",today tried already three time import mt megacor unfortunately stay process imagepng due following error imagepng event created megacor please check problem thanks stefan ticket automatically created,-0.****************,0.0,392,0.0014541505935435817,0.****************,1.***************,0.2,0.15,0.15,94.46%
Incident,OEKB-5937,Medium,M12-I5: CONV0000000510 - SEND ALL does not work,Automation test,2024-05-06 10:03:00,2024-05-06 11:49:00,NO,"CONV0000000510 
After generating notifications and trying to send via SEND ALL functionality, I noticed that nothing happens. 
Only if all messages are selected, the sending works, which is not a proper behavior. 

Could you please check? 
Thanks, Bert 


[This ticket was automatically created]",conv generating notification trying send via send functionality noticed nothing happens message selected sending work proper behavior could please check thanks bert ticket automatically created,-0.003885272890329361,0.0,392,0.0014541505935435817,0.25097131822258234,1.***************,0.2,0.1,0.15,55.15%
Incident,OEKB-5936,Major,M12-I5: TEND0000000494 - No Own fees calculated,Automation test,2024-05-03 09:58:00,2024-05-18 10:45:00,NO,"TEND0000000494 
was set up with Own Fees Flag set to true => 

!image-2024-05-03-09-55-18-851.png|thumbnail! 

Eligible Cash Acc SLA exists for 227200 => 

!image-2024-05-03-09-56-01-062.png|thumbnail! 

However, no debit of fees (EUR 7,00) was processed => 

!image-2024-05-03-09-57-33-312.png|thumbnail! 


Could you please check? 

Thanks, Bert 


[This ticket was automatically created]",tend set fee flag set true imagepngthumbnail eligible cash acc sla exists imagepngthumbnail however debit fee eur processed imagepngthumbnail could please check thanks bert ticket automatically created,0.016701504588127136,15.0,395,0.0013832308322338331,0.24582462385296822,1.***************,0.2,0.13,0.15,58.87%
Incident,OEKB-5935,Medium,M12-I5: BONU - sending of EMAIL-REPORT-566 failed in MB,Automation test,2024-05-02 17:08:00,2024-05-15 16:15:00,YES,"Events BONU0000000424 and BONU0000000462 

for client 220500 there are two entries in Client reporting SLA: 

!image-2024-05-02-16-59-56-121.png! 

for both events in MB are found two FLowOuts, but the ones for SWIFT are successful, the ones for Email are Sending Failed: 

!image-2024-05-02-17-03-05-550.png! 

Unfortunately they are also not to find in the mailbox of the email-address which is filled in at the client's data: 

!image-2024-05-02-17-04-08-780.png! 

Please check what does not work here. 

Thanks, stefan 

[This ticket was automatically created]",event bonu bonu client two entry client reporting sla imagepng event mb found two flowouts one swift successful one email sending failed imagepng unfortunately also find mailbox emailaddress filled client data imagepng please check work thanks stefan ticket automatically created,-0.019154684618115425,12.0,395,0.0013832308322338331,0.*****************,1.***************,0.2,0.1,0.15,55.72%
Information Request,OEKB-5934,Major,M12-I5: No usage of other template in client reporting SLA,Automation test,2024-05-02 16:23:00,2024-05-03 11:57:00,NO,"We noticed following situation at two BONU events BONU0000000424 and BONU0000000462: 
* BONU0000000424: No notification ""37"" generated for account 236510 due to Notification Status ""Processed With Failure"" and error description ""The address [Email, Current] is not found for the destination securitiesAccount"" 
* BONU0000000462: No notification ""43"" generated for account 225400 due to same condition 

We analyzed further and found the reason for that: For both clients a Client Reporting SLA exists with templates for ""Email, Current"": 

!image-2024-05-02-15-56-44-424.png! 

but for both clients no e-mail-address with purpose ""Current"" exists. So far okay, BUT there are two big topics in our opinion: 

*1)* {*}{*}For the first point we create already bug as it could may have a different rootcause. 

*2)* In our opinion it is wrong, that if more templates are existing in one SLA no one works just because of one missing source. Why have no SWIFTs MT566 have been sent for both as these templates are also included in the SLAs? 

for client 236500: 

!image-2024-05-02-16-06-25-609.png! 

for client 225400: 

!image-2024-05-02-16-04-37-613.png! 

Please check whe in both cases no SWIFT-MT566 has been sent out. 

Thanks, stefan 


[This ticket was automatically created]",noticed following situation two bonu event bonu bonu bonu notification generated account due notification status processed failure error description address email current found destination securitiesaccount bonu notification generated account due condition analyzed found reason client client reporting sla exists template email current imagepng client emailaddress purpose current exists far okay two big topic opinion first point create already bug could may different rootcause opinion wrong template existing one sla one work one missing source swift mt sent template also included slas client imagepng client imagepng please check whe case swiftmt sent thanks stefan ticket automatically created,-0.****************,0.0,396,0.0013603680375478939,0.*****************,1.***************,0.2,0.13,0.06,52.29%
Information Request,OEKB-5933,Major,M12-I5: not comprehensible Client reporting SLAs,Automation test,2024-05-02 16:14:00,2024-05-02 17:01:00,NO,"We noticed following situation at two BONU events BONU0000000424 and BONU0000000462: 
* BONU0000000424: No notification ""37"" generated for account 236510 due to Notification Status ""Processed With Failure"" and error description ""The address [Email, Current] is not found for the destination securitiesAccount"" 
* BONU0000000462: No notification ""43"" generated for account 225400 due to same condition 

We analyzed further and found the reason for that: For both clients a Client Reporting SLA exists with templates for ""Email, Current"": 

!image-2024-05-02-15-56-44-424.png! 

but for both clients no e-mail-address with purpose ""Current"" exists. So far okay, BUT there are two big topics in our opinion: 

*1)* Again a topic concerning the client reporting SLAs: Why are these client reporting SLAs in new M12? They exist neither in M10 QAS nor in M10 PROD nor were they created manually from us: 

For client 236500: 

!image-2024-05-02-16-06-25-609.png! 

For client 225400: 

!image-2024-05-02-16-04-37-613.png! 

Where do they come from? 

  

*2)* For the second point we create a separate bug as it could may have a different rootcause. 

BR, stefan 

[This ticket was automatically created]",noticed following situation two bonu event bonu bonu bonu notification generated account due notification status processed failure error description address email current found destination securitiesaccount bonu notification generated account due condition analyzed found reason client client reporting sla exists template email current imagepng client emailaddress purpose current exists far okay two big topic opinion topic concerning client reporting slas client reporting slas new exist neither qas prod created manually u client imagepng client imagepng come second point create separate bug could may different rootcause br stefan ticket automatically created,-0.*****************,0.0,396,0.0013603680375478939,0.*****************,1.***************,0.2,0.13,0.06,50.38%
Incident,OEKB-5932,Critical,M12-I5: TEND0000000484 - Client Payments fail due to incorrect sese.023,Automation test,2024-05-02 15:28:00,2024-05-02 19:02:00,NO,"TEND0000000484 
Client Payments have been created, sese.023 generated but with incorrect content. 
I noticed that (at least?), all Trading Details are missing => 

!image-2024-05-02-15-22-02-364.png|thumbnail! 

in comparison with sese.023 from M10 => 

!image-2024-05-02-15-24-11-092.png|thumbnail! 

Could you please urgently check? 
Thanks, Bert 




[This ticket was automatically created]",tend client payment created sese generated incorrect content noticed least trading detail missing imagepngthumbnail comparison sese imagepngthumbnail could please urgently check thanks bert ticket automatically created,-0.5452075731009245,0.0,396,0.0013603680375478939,0.3863018932752311,1.***************,0.2,0.15,0.15,82.95%
Information Request,OEKB-5931,Medium,M12 - New comment in Issuer Agent,Automation test,2024-05-02 12:43:00,2024-05-03 10:18:00,NO,"Hi Oussema, 

as discussed in our call today we now added comments in all Issuer Agents at M10 - all these are needed for SRD II reasons. All other new Issuer Agents in M12 are needed for other Corporate Actions only. 

The comment is ""{_}For SRD II{_}"". 

Two things here which we would ask you to clarify: 
* in current M10 Production the Issuer Agent 751000 EC Netherlands is INACTIVE, but is needed in new M12 environment for SRD II. We added a comment. Please make sure that this  Issuer Agent is also available in new PROD environment. 
* in M12 there are three entries which belong to Unicredit Bank Austria (Acting Entities 222100, 99500 UNICREDIT BANK AUSTRIA AG and 222100 again. As the two of them are from the old Issuer Agent list: Do we need them both? Can one of them be deleted? We want to avoid any problems out of that. 

Thanks, stefan 


[This ticket was automatically created]",hi oussema discussed call today added comment issuer agent needed srd ii reason new issuer agent needed corporate action comment srd ii two thing would ask clarify current production issuer agent ec netherlands inactive needed new environment srd ii added comment please make sure issuer agent also available new prod environment three entry belong unicredit bank austria acting entity unicredit bank austria ag two old issuer agent list need one deleted want avoid problem thanks stefan ticket automatically created,0.027031447738409042,0.0,396,0.0013603680375478939,0.*****************,1.***************,0.2,0.1,0.06,40.49%
Incident,OEKB-5930,Critical,"M12-I5: BONU0000000438 - CA Status is ""Confirmed"" although no payments have been processed",Automation test,2024-04-30 11:18:00,2024-05-03 19:46:00,NO,"BONU0000000438 
Only client entitlements have been calculated (no market entitlements). 
No payments have been processed. 
Of course, no MT566 could have been sent => 

!image-2024-04-30-11-16-04-268.png|thumbnail! 


However, the CA Status in ""Confirmed""!? => 

!image-2024-04-30-11-15-02-657.png|thumbnail! 

Could you please check! 
Thanks, Bert 



[This ticket was automatically created]",bonu client entitlement calculated market entitlement payment processed course mt could sent imagepngthumbnail however ca status confirmed imagepngthumbnail could please check thanks bert ticket automatically created,-0.010478977113962173,3.0,398,0.001315769868497432,0.25261974427849054,1.***************,0.2,0.15,0.15,62.89%
Incident,OEKB-5929,Major,M12-I5: BONU0000000462 - Parity In must not be rounded/truncated,Automation test,2024-04-30 09:18:00,2024-05-01 11:43:00,NO,"BONU0000000462 
MT564 (attached) was received containing the following SECU rate => :92D::ADEX//0,18091850142/1, 

This rate was also received recently in Production Environment, see M10 screen => 

!image-2024-04-30-09-14-02-552.png|thumbnail! 

However, in M12 the Parity In was mapped incorrectly, respectively rounded/truncated => 

!image-2024-04-30-09-15-14-336.png|thumbnail! 


Moreover, it is also not possible to add the missing decimal places. 
When trying to update the Parity In, the system is still saving only 7 decimal places. 


Could you please check? 
Thanks, Bert 

[This ticket was automatically created]",bonu mt attached received containing following secu rate dadex rate also received recently production environment see screen imagepngthumbnail however parity mapped incorrectly respectively roundedtruncated imagepngthumbnail moreover also possible add missing decimal place trying update parity system still saving decimal place could please check thanks bert ticket automatically created,-0.16319537162780762,1.0,398,0.001315769868497432,0.2907988429069519,1.***************,0.2,0.13,0.15,65.62%
Information Request,OEKB-5928,Major,M12-I5: BONU0000000438 - Market Entitlements not calculated,Automation test,2024-04-30 08:43:00,2024-05-01 11:56:00,NO,"BONU0000000438 
Client Entitlements have been calculated automatically on Record Date 29/04 EOD => 

!image-2024-04-30-08-38-53-268.png|thumbnail! 

However, Market Entitlements are missing => 

!image-2024-04-30-08-39-30-563.png|thumbnail! 


Could you please check! 
BR Bert 


[This ticket was automatically created]",bonu client entitlement calculated automatically record date eod imagepngthumbnail however market entitlement missing imagepngthumbnail could please check br bert ticket automatically created,-0.010325230658054352,1.0,398,0.001315769868497432,0.2525813076645136,1.***************,0.2,0.13,0.06,46.39%
Incident,OEKB-5927,Major,M12-I5: DECR - Failed Received MT566,Automation test,2024-04-29 15:23:00,2024-05-23 15:36:00,NO,"test with DECR0000000394 - AT0000639042 

Hello, 

the above Event is in Status ""Waiting Payment"". MC12 has also received an MT566 from 3i, but the message has not been assigned to the DECR event, and can be found under ""Failed Received MT566"".  

Can you please check! 

Thank you and BR, Dalibor 

  


[This ticket was automatically created]",test decr hello event status waiting payment mc also received mt message assigned decr event found failed received mt please check thank br dalibor ticket automatically created,-0.0526835061609745,24.0,399,0.001294022105465848,0.2631708765402436,1.***************,0.2,0.13,0.15,61.48%
Information Request,OEKB-5926,Medium,M12-I5: CAPD - Breakdown instruction not to find in 3i,Automation test,2024-04-25 15:33:00,2024-04-25 21:34:00,NO,"CAPD0000000352 

Following Breakdown instruction has been created in MegaCor: 

!bi.png! 

but unfortunately it was not found in 3i and was therefore not taken into consideration. 

Please check that and advise if there something is missing from our side or what has happened here. 

Thanks, stefan 


[This ticket was automatically created]",capd following breakdown instruction created megacor bipng unfortunately found therefore taken consideration please check advise something missing side happened thanks stefan ticket automatically created,-0.36285244300961494,0.0,403,0.0012105667184486217,0.34071311075240374,1.***************,0.2,0.1,0.06,55.11%
Incident,OEKB-5925,Major,M12-I5: CAPD - Error when trying to validate MP,Automation test,2024-04-25 14:33:00,2024-05-13 14:55:00,NO,"test with CAPD0000000386 

Hi,  

An error message appears when trying to validate MP for above CAPD. 

Can you please check? 

  

Thank you and BR, Dalibor 


[This ticket was automatically created]",test capd hi error message appears trying validate mp capd please check thank br dalibor ticket automatically created,-0.04660080000758171,18.0,403,0.0012105667184486217,0.2616502000018954,1.***************,0.2,0.13,0.15,61.25%
Information Request,OEKB-5923,Major,M12-I5: CAPD - MT566 failed in MegaBroker,Automation test,2024-04-25 12:28:00,2024-04-25 13:06:00,NO,"Event CAPD0000000386 

We wanted to import the attached MT566 but it failed in MB in Status ""Waiting Message Generation"": 

!image-2024-04-25-12-25-30-459.png! 

Please check why we couldn't import this MT566. 

  

Thanks, stefan 


[This ticket was automatically created]",event capd wanted import attached mt failed mb status waiting message generation imagepng please check couldnt import mt thanks stefan ticket automatically created,-0.026554668322205544,0.0,403,0.0012105667184486217,0.2566386670805514,1.***************,0.2,0.13,0.06,47.0%
Requirement,OEKB-5922,Critical,M12 | NEW Queues needed for special rules,Valdes ELIZABEHT,2024-04-25 11:51:00,2024-05-06 12:48:00,YES,"Dear All,  

during our UAT in M12 QAS we noticed, that we have missed the set up for some specific rules.  

Therefore we would need:  
* +*all MT564 to FDSEAT (DEF001)*+ - Forwarding of CA to zWP should be forwarded via a dedicated queue: ""{color:#00875a}*MB12.MT564.TO.ZWP.S*{color}"" 


* +*all MT564 to CCSYATWW (DEF003)*+ - Forward MT564 NEWM/REPL for Transformations to CCSYS should be forwarded via a dedicated queue: ""{color:#00875a}*MB12.MT564.TO.CCSYS.S*{color}"" 


* +*all MT564 to CCP.A (BIC CAAHUSW1XXXX)*+ should be forwarded via a dedicated queue: ""{color:#00875a}*MB12.MT564.TO.CCPA.S*{color}"" 

Please analyse and come back with a timeline when you can set up this configurations.  

Thanks & KR,  

Eli",dear uat qas noticed missed set specific rule therefore would need mt fdseat def forwarding ca zwp forwarded via dedicated queue colorambmttozwpscolor mt ccsyatww def forward mt newmrepl transformation ccsys forwarded via dedicated queue colorambmttoccsysscolor mt ccpa bic caahuswxxxx forwarded via dedicated queue colorambmttoccpascolor please analyse come back timeline set configuration thanks kr eli,-0.02202189341187477,11.0,403,0.0012105667184486217,0.2555054733529687,0.*****************,0.012503434490811616,0.15,0.09,26.2%
Incident,OEKB-5921,Critical,M12-I5: BONU0000000383 - Create Cash Payments out of Entitlements failed,Automation test,2024-04-25 10:53:00,2024-04-29 11:38:00,NO,"BONU0000000383 
Cash Entitlements for fractions have been updated, however, when trying to Create Cash Payments out of Entitlements, I'm not able to do so, as no results were found => 

!image-2024-04-25-10-50-06-927.png|thumbnail! 


Could you please check! 
Thanks, Bert 




[This ticket was automatically created]",bonu cash entitlement fraction updated however trying create cash payment entitlement im able result found imagepngthumbnail could please check thanks bert ticket automatically created,0.034404074773192406,4.0,403,0.0012105667184486217,0.2413989813067019,1.***************,0.2,0.15,0.15,61.21%
Incident,OEKB-5920,Critical,M12-I5: BONU0000000383 - Market Payment Amount Validation failed,Automation test,2024-04-25 10:43:00,2024-04-30 12:17:00,NO,"BONU0000000383 
When trying to validate Market Payment Amount, the following error popped up => 

!image-2024-04-25-10-40-38-838.png|thumbnail! 

Please check! 
Thanks, Bert 


[This ticket was automatically created]",bonu trying validate market payment amount following error popped imagepngthumbnail please check thanks bert ticket automatically created,0.028445744886994362,5.0,403,0.0012105667184486217,0.2428885637782514,1.***************,0.2,0.15,0.15,61.43%
Incident,OEKB-5919,Critical,M12-I5: SOFF0000000382 - Client Payments generated with wrong quantities,Automation test,2024-04-25 10:03:00,2024-05-24 11:58:00,NO,"SOFF0000000382 
was set up to credit new shares including 2 decimal positions (smallest denomination in T2S is 0,01). 
Entitlements were calculated accordingly => 

!image-2024-04-25-09-58-51-400.png|thumbnail! 

Market Payment was processed successfully => 

!image-2024-04-25-09-59-37-348.png|thumbnail! 


However, after checking Client Payments, I noticed that the instructions have been sent to T2S with incorrect quantities (obviously, the quantities have been rounded up) => 

!image-2024-04-25-10-01-01-981.png|thumbnail! 


Could you please check! 
Thanks, Bert 





[This ticket was automatically created]",soff set credit new share including decimal position smallest denomination t entitlement calculated accordingly imagepngthumbnail market payment processed successfully imagepngthumbnail however checking client payment noticed instruction sent t incorrect quantity obviously quantity rounded imagepngthumbnail could please check thanks bert ticket automatically created,0.008875280618667603,29.0,403,0.0012105667184486217,0.2477811798453331,1.***************,0.2,0.15,0.15,62.17%
Incident,OEKB-5918,Critical,M12-I5: SOFF0000000382 - Market Claim Payment (sese.023) Rejected in T2S/CCSYS,Automation test,2024-04-25 09:08:00,2024-04-26 19:11:00,NO,"SOFF0000000382 
Market Claim Payment was generated, sese.023 (attached) sent to T2S/CCSYS. 

However, the instructions have been rejected with the following Rejection Reason => 

*_IIMP146- No hold reason code must be informed if the Hold indicator set to false._* 

Could you please check! 
Thanks, Bert 




[This ticket was automatically created]",soff market claim payment generated sese attached sent tsccsys however instruction rejected following rejection reason iimp hold reason code must informed hold indicator set false could please check thanks bert ticket automatically created,-0.040272172540426254,1.0,403,0.0012105667184486217,0.26006804313510656,1.***************,0.2,0.15,0.15,64.01%
Incident,OEKB-5917,Major,"M12-I5: SOFF0000000382 - MT564 OUT sent due to Market Claim Entitlement ""Technically Rejected""",Automation test,2024-04-25 08:48:00,2024-05-02 15:50:00,NO,"SOFF0000000382 
Market Claims have been detected, Entitlements calculated, MT564 NEWM OUT (containing :22F::ADDB//CAPA) generated (message attached). 

However, the message is in status ""Technically Rejected"" => 

!image-2024-04-25-08-45-16-479.png|thumbnail! 

Could you please check? 
Thanks, Bert 


[This ticket was automatically created]",soff market claim detected entitlement calculated mt newm containing faddbcapa generated message attached however message status technically rejected imagepngthumbnail could please check thanks bert ticket automatically created,-0.044708261266350746,7.0,403,0.0012105667184486217,0.****************,1.***************,0.2,0.13,0.15,61.18%
Information Request,OEKB-5916,Major,M12-I5: different BIC for same account for MT566 OUT,Automation test,2024-04-24 17:23:00,2024-04-24 17:53:00,YES,"We noticed that from all outgoing MT566 some are rejected locally: 

!image-2024-04-24-17-19-43-384.png! 

as they are all for recipient BKAUATWW we searched in MegaCor and found out that for account 222100 MegaCor uses different recipient BICs: 

!image-2024-04-24-17-21-20-958.png! 

some with recipient BIC BKAUATWW were rejected, the others with BKAUATW0 were ACKED. 

  

Please check how this could happen. 

Thanks, stefan 


[This ticket was automatically created]",noticed outgoing mt rejected locally imagepng recipient bkauatww searched megacor found account megacor us different recipient bics imagepng recipient bic bkauatww rejected others bkauatw acked please check could happen thanks stefan ticket automatically created,0.031369224190711975,0.0,403,0.0012105667184486217,0.***************,1.***************,0.2,0.13,0.06,44.82%
Incident,OEKB-5914,Medium,M12-I5: CONV MAND - SECU OUT ISIN description not auto-filled,Automation test,2024-04-24 12:18:00,2024-05-06 09:31:00,NO,"When creating CONV MAND SECU option, I notice that SECU OUT ISIN description is not automatically filled => 


!screenshot-1.png|thumbnail! 

Could you please check? 

Thanks, Bert 


[This ticket was automatically created]",creating conv mand secu option notice secu isin description automatically filled screenshotpngthumbnail could please check thanks bert ticket automatically created,-0.002581801265478134,11.0,404,0.0011905578105407307,0.*****************,1.***************,0.2,0.1,0.15,55.1%
Information Request,OEKB-5913,Major,M12-I5: BONU0000000383 - MT566 IN failed in MegaBroker,Automation test,2024-04-24 12:08:00,2024-04-24 15:35:00,NO,"BONU0000000383 
When trying to import MT566 (attached), the generation failed in MegaBroker => 

!image-2024-04-24-12-06-44-713.png|thumbnail! 

Could you please check? 
Thanks, Bert 




[This ticket was automatically created]",bonu trying import mt attached generation failed megabroker imagepngthumbnail could please check thanks bert ticket automatically created,0.030421093106269836,0.0,404,0.0011905578105407307,0.24239472672343254,1.***************,0.2,0.13,0.06,44.86%
Incident,OEKB-5912,Critical,"M12-I5: SPLR0000000353 - MP still in status ""WaitingCashBookingConfirmation"" - SAP-Response not processed",Automation test,2024-04-24 10:43:00,2024-04-24 12:28:00,NO,"SPLR0000000353 
Market Payment CASH in USD was generated (Cash Interface attached). 

SAP response (attached) was received (Message ID 2900033) but did not reach MegaCor => 

!image-2024-04-24-10-36-11-823.png|thumbnail! 

!image-2024-04-24-10-35-47-763.png|thumbnail! 

Could you please check! 
Thanks, Bert 




[This ticket was automatically created]",splr market payment cash usd generated cash interface attached sap response attached received message id reach megacor imagepngthumbnail imagepngthumbnail could please check thanks bert ticket automatically created,-0.006382174789905548,0.0,404,0.0011905578105407307,0.2515955436974764,1.***************,0.2,0.15,0.15,62.74%
Incident,OEKB-5910,Medium,M12-I5: Create Secu Client Payments out of Entitlement - Result list should include Resulting Quantity,Automation test,2024-04-23 11:13:00,2024-05-08 10:34:00,NO,"When creating Secu Payments out of Entitlements, the Result list actually shows only the Eligible Quantity (also in the XLS file to be exported) => 

!image-2024-04-23-09-46-13-232.png|thumbnail! 

In M10, the Resulting Quantity is displayed which is much more useful for the user (also in the XLS file) => 

!image-2024-04-23-09-47-39-275.png|thumbnail! 

Please check the possiblity to display the Resulting Quantity in M12! 
Thanks, Bert 


[This ticket was automatically created]",creating secu payment entitlement result list actually show eligible quantity also xl file exported imagepngthumbnail resulting quantity displayed much useful user also xl file imagepngthumbnail please check possiblity display resulting quantity thanks bert ticket automatically created,0.10007669031620026,14.0,405,0.0011708796207911744,0.22498082742094994,1.***************,0.2,0.1,0.15,51.25%
Information Request,OEKB-5909,Major,M12-I5: SPLR0000000353 - Update Fraction Price - Entitlements missing,Gregor WILDING,2024-04-23 11:06:00,2024-04-23 16:05:00,NO,"SPLR0000000353 
There are, in total, 14 client entitlements for fractions (6,8 units) => 

!image-2024-04-23-10-09-11-145.png! 

However, when trying to update entitlements via ""Update Fraction Price"", the result list only displays 9 entitlements (3,6 units) => 

!image-2024-04-23-10-10-27-039.png! 

Could you please check! 
Thanks, Bert",splr total client entitlement fraction unit imagepng however trying update entitlement via update fraction price result list display entitlement unit imagepng could please check thanks bert,-0.004404567182064056,0.0,405,0.0011708796207911744,0.251101141795516,0.024216245168726842,0.0045885282524504465,0.13,0.06,16.85%
Information Request,OEKB-5908,Major,M12-I5: Notifications net sent out,Gregor WILDING,2024-04-23 11:04:00,2024-04-23 13:10:00,NO,"Today we noticed that there were 14 messages today in the morning which were obviously processed in MegaCor: 

!image-2024-04-22-15-25-24-714.png! 

and also in MegaBroker: 

!image-2024-04-22-15-26-02-021.png! 

But they never reached the SWIFT-network. 

Only one of them - flow out 2380026 - is understandable for us as messages to 3i are not seen in Alliance, but the rest is missing. 

Please check why this happened. 

  

Thanks, 

stefan",today noticed message today morning obviously processed megacor imagepng also megabroker imagepng never reached swiftnetwork one flow understandable u message seen alliance rest missing please check happened thanks stefan,0.026401691138744354,0.0,405,0.0011708796207911744,0.2433995772153139,0.024216245168726842,0.0045885282524504465,0.13,0.06,15.7%
Incident,OEKB-5907,Critical,M12-I5: SPLR0000000353 - MT566 IN - Impact failed / Performance Issues,Gregor WILDING,2024-04-23 11:02:00,2024-04-23 12:11:00,NO,"SPLR0000000353 
MT566 was sent but failed due to the following error => 

!image-2024-04-23-09-08-59-346.png! 

Moreover, we are facing Performance Issues (slowness, refresh did not always work, M12 was not available at all, now and then) since yesterday (22/04), which may also be the reason for the a.m. error. 

Could you please check! 
Thanks, Bert",splr mt sent failed due following error imagepng moreover facing performance issue slowness refresh always work available since yesterday may also reason error could please check thanks bert,-0.4262804165482521,0.0,405,0.0011708796207911744,0.356570104137063,0.024216245168726842,0.0045885282524504465,0.15,0.15,49.17%
Incident,OEKB-5906,Blocker,M12-I5: CERT0000000297 - Unfreeze Rejected,Automation test,2024-04-22 14:50:00,2024-04-30 08:45:00,YES,"CERT0000000297 
After successful freezing, I manually generated the unfreeze. 
However, it has been rejected, obviously, the Restriction Reference is missing in the semt.013 (attached), see CCSYS Rejection Reason => 

!image-2024-04-22-10-22-22-083.png|thumbnail! 

Please check! 
Thanks, Bert 


[This ticket was automatically created]",cert successful freezing manually generated unfreeze however rejected obviously restriction reference missing semt attached see ccsys rejection reason imagepngthumbnail please check thanks bert ticket automatically created,-0.01118234172463417,7.0,406,0.0011515266829096006,0.25279558543115854,1.***************,0.2,0.14,0.15,61.42%
Incident,OEKB-5905,Critical,M12-I5: TEND0000000342 - MT564 NEWM/REPE contains incorrect/incomplete tag :22F::DISF,Automation test,2024-04-22 14:50:00,2024-04-22 15:44:00,YES,"TEND0000000342 
After sending MT564 NEWM/REPE, we noticed that the tag :22F::DISF is populated incorrectly/incompetely which leads to a NACK in SWIFT Alliance => 

!image-2024-04-22-13-00-16-740.png|thumbnail! 

As no fractions are involved, in this case, MT564 should not include :22F::DISF at all. 

Could you please check! 
Thanks, Bert 




[This ticket was automatically created]",tend sending mt newmrepe noticed tag fdisf populated incorrectlyincompetely lead nack swift alliance imagepngthumbnail fraction involved case mt include fdisf could please check thanks bert ticket automatically created,0.010908141732215881,0.0,406,0.0011515266829096006,0.24727296456694603,1.***************,0.2,0.15,0.15,62.09%
Incident,OEKB-5904,Major,M12-I2: CAPD-003. CAPD event in foreign market - Event with incoming comments,Automation test,2024-04-22 14:50:00,2024-05-08 14:45:00,NO,"test with CAPD0000000351 

Hello, 

The above event was created using MT564 and then crosschecked.  

In the menu item ""Event with incoming comments"" you will find ""Incoming Comment"".  

I have ignored this, but the message does not disappear. 

  

Please check! 

  

Thank you and BR, Dalibor 

  


[This ticket was automatically created]",test capd hello event created using mt crosschecked menu item event incoming comment find incoming comment ignored message disappear please check thank br dalibor ticket automatically created,-0.010921293869614601,15.0,406,0.0011515266829096006,0.25273032346740365,1.***************,0.2,0.13,0.15,59.91%
Incident,OEKB-5902,Blocker,M12-I5: TEND0000000304 - CASH Market Payment failed due to incorrect sese.023,Automation test,2024-04-18 14:33:00,2024-04-19 11:36:00,YES,"TEND0000000304 
Market Payment cash (sese.023) has been rejected in T2S, sese.024 was received with Reason => 

NO VALID SECURITY SPECIFICATION 

When checking sese.023, I noticed that it does not contain the financial instrument (ISIN). 

Could you please check? 
Thanks, Bert 


[This ticket was automatically created]",tend market payment cash sese rejected t sese received reason valid security specification checking sese noticed contain financial instrument isin could please check thanks bert ticket automatically created,0.003599204123020172,0.0,410,0.0010772612553122203,0.24910019896924496,1.***************,0.2,0.14,0.15,60.87%
Incident,OEKB-5900,Major,"M12-I5: TEND0000000304 - Input CA - ""Is Agent involved""-flag set to FALSE but not saved for SECU OUT",Automation test,2024-04-18 11:58:00,2024-04-19 15:17:00,YES,"TEND0000000304 
CASE option was set up with SECU OUT movement and ""Is Agent involved""-flag set to FALSE => 

!image-2024-04-18-11-34-32-088.png|thumbnail! 

However, after validating I noticed that the flag is set back to TRUE by the system => 

!image-2024-04-18-11-37-06-688.png|thumbnail! 

After updating only the flag again, the correct value was finally saved. 

Nevertheless, please check why the value was not saved initially! 
Thanks, Bert 





[This ticket was automatically created]",tend case option set secu movement agent involvedflag set false imagepngthumbnail however validating noticed flag set back true system imagepngthumbnail updating flag correct value finally saved nevertheless please check value saved initially thanks bert ticket automatically created,0.06372369267046452,1.0,410,0.0010772612553122203,0.23406907683238387,1.***************,0.2,0.13,0.15,57.11%
Incident,OEKB-5899,Medium,M12-I5: TEND MAND CASE - SECU IN ISIN description not auto-filled,Automation test,2024-04-18 11:28:00,2024-04-18 16:59:00,NO,"When creating TEND MAND CASE option, I notice that SECU IN description is not automatically filled => 


!image-2024-04-18-11-23-38-606.png|thumbnail! 

Could you please check? 

Thanks, Bert 


[This ticket was automatically created]",creating tend mand case option notice secu description automatically filled imagepngthumbnail could please check thanks bert ticket automatically created,0.017490511760115623,0.0,410,0.0010772612553122203,0.****************,1.***************,0.2,0.1,0.15,54.34%
Incident,OEKB-5898,Major,M12-I5: Double positions,Automation test,2024-04-18 11:23:00,2024-04-18 17:22:00,NO,"Event SPLR0000000298 

as already reported in bug OEKB-4846 and in OEKB-5023 we noticed in new M12 again that there are two positions for the same account for some accounts (211600, 220500, 222138 and 222238) but not for all (not for 222200 and 232100): 

!image-2024-04-18-11-18-31-255.png! 

  

Please check why this occurs again in new environment. 

Thanks, stefan 


[This ticket was automatically created]",event splr already reported bug oekb oekb noticed new two position account account imagepng please check occurs new environment thanks stefan ticket automatically created,0.*****************,0.0,410,0.0010772612553122203,0.*****************,1.***************,0.2,0.13,0.15,56.22%
Information Request,OEKB-5897,Critical,M12-I5: Entitlements calculated in Status Cancelled,Automation test,2024-04-18 11:13:00,2024-04-18 13:12:00,YES,"Event SPLR0000000298 

After calculating entitlements manually we noticed that there are some Entitlements in Status CANCELLED immediately after calculating: 

!image-2024-04-18-11-07-21-826.png! 

  

First we thought that this is because of an other existing SPLR event SPLR0000000123 which also has cancelled entitlements, but account 232100 is cancelled in SPLR000000123 but not in SPLR0000000298, the other stati are the same - how could this be? 

Please check that 

  

thanks, stefan 

[This ticket was automatically created]",event splr calculating entitlement manually noticed entitlement status cancelled immediately calculating imagepng first thought existing splr event splr also cancelled entitlement account cancelled splr splr stati could please check thanks stefan ticket automatically created,-0.****************,0.0,410,0.0010772612553122203,0.****************,1.***************,0.2,0.15,0.06,68.69%
Incident,OEKB-5896,Medium,M12-I5: LTRD on SWIFT although not in event data,Automation test,2024-04-18 09:33:00,2024-04-18 13:29:00,NO,"Event SPLR0000000298 

I duplicated event SPLR0000000160 and created SPLR0000000298. After generating and sending notifications I noticed that following content is included in outgoing MT564: 

!image-2024-04-18-09-26-41-644.png! 

  

As I didn't fill in this field, nor it is visible we assume that it is hidden saved in the old event and gets also duplicated. 

Please adapt that as this field (and maybe there are other hidden fields too?) should not  be duplicated and sent out to the clients. 

Thanks, stefan 


[This ticket was automatically created]",event splr duplicated event splr created splr generating sending notification noticed following content included outgoing mt imagepng didnt fill field visible assume hidden saved old event get also duplicated please adapt field maybe hidden field duplicated sent client thanks stefan ticket automatically created,-0.0871192030608654,0.0,410,0.0010772612553122203,0.27177980076521635,1.***************,0.2,0.1,0.15,58.27%
Incident,OEKB-5895,Critical,M12-I5: CERT0000000297 - Freeze Rejected,Automation test,2024-04-18 08:48:00,2024-04-18 10:42:00,NO,"CERT0000000297 
Freeze instruction (semt.013 - see attached) was generated for a quantity that was instructed for NOQU option. 

However, the freeze attempt was rejected (see semt.014 attached) with reason 
""CUSTOMER REFERENCE VIOLATES FORMAT"" 

Could you please check! 

Thanks, Bert 


[This ticket was automatically created]",cert freeze instruction semt see attached generated quantity instructed noqu option however freeze attempt rejected see semt attached reason customer reference violates format could please check thanks bert ticket automatically created,-0.048599254339933395,0.0,410,0.0010772612553122203,0.*****************,1.***************,0.2,0.15,0.15,64.32%
Information Request,OEKB-5894,Major,M12-I5: - Failed received MT566 for Custodian NBB,Automation test,2024-04-17 16:53:00,2024-04-18 12:02:00,YES,"ISIN BE0001735860 

Hello,  

The NBB (National Bank of Belgium) custodian has sent us MT564 and MT566 messages for testing purposes.  

Unfortunately, the test did not produce the desired result and no event has been created. 

The MT566 messages can be found in the menu item ""Failed Received MT566"". 

The error description is ""Unknown Sender"". 

The MT564 messages can be found in Megabroker with the error message ""No address found for shortSwiftAddress"". 

  

Please check what the problem here is. 

  

Thank you and BR, Dalibor 

  

  

  


[This ticket was automatically created]",isin hello nbb national bank belgium custodian sent u mt mt message testing purpose unfortunately test produce desired result event created mt message found menu item failed received mt error description unknown sender mt message found megabroker error message address found shortswiftaddress please check problem thank br dalibor ticket automatically created,-0.****************,0.0,410,0.0010772612553122203,0.*****************,1.***************,0.2,0.13,0.06,57.35%
Incident,OEKB-5893,Medium,"M12-I5: Meeting Events (XMET, MEET, OMET) - Screen discrepancies",Automation test,2024-04-17 14:04:00,2024-04-18 11:53:00,NO,"I noticed that there are options which include 
""Withdrawal Begin/End"" fields, such as CONY, CONN => 

!image-2024-04-17-13-52-23-136.png|thumbnail! 

and other options (PROX, SPLI) where these fields are not available => 

!image-2024-04-17-13-53-03-763.png|thumbnail! 


Could you please check as ""Withdrawal""-fields should be available for all options within an event. 

Thanks, Bert 


[This ticket was automatically created]",noticed option include withdrawal beginend field cony conn imagepngthumbnail option prox spli field available imagepngthumbnail could please check withdrawalfields available option within event thanks bert ticket automatically created,-0.046616336330771446,0.0,411,0.0010594556929076101,0.26165408408269286,1.***************,0.2,0.1,0.15,56.75%
Incident,OEKB-5892,Medium,M12-I1: Pending Trades - XML Batch Operation - Record Count,Automation test,2024-04-17 13:18:00,2024-04-18 11:46:00,NO,"The ""Record count"" in Screen ""XML Batch Operation"" for Pending Trades isn´t always correct. 

Examples: 
* 16.04.2024 18:00 (see file pending_instructions_202404161600101.xml) 
** Record count in file = 0 / in screen 153. I really don´t know where the count of 153 is coming from. 
* 09.04.2024 18:00 (see file pending_instructions_202404091600091.xml) 
** Record count in file = 1 / in screen """" (empty) 
* 08.04.2024 18:00 (see file pending_instructions_202404081600106.xml) 
** Record count in file = 404 / in screen """" (empty) 

  

  


[This ticket was automatically created]",record count screen xml batch operation pending trade isnt always correct example see file pendinginstructionsxml record count file screen really dont know count coming see file pendinginstructionsxml record count file screen empty see file pendinginstructionsxml record count file screen empty ticket automatically created,-0.12295272387564182,0.0,411,0.0010594556929076101,0.28073818096891046,1.***************,0.2,0.1,0.15,59.61%
Incident,OEKB-5891,Critical,M12-I5: MEET0000000283 - MT565 OUT - Instruction Comment incomplete,Automation test,2024-04-17 13:08:00,2024-05-15 11:10:00,NO,"MEET0000000283 
Market instructions have been edited with Instruction comment (INST) => 

!image-2024-04-17-13-03-15-508.png|thumbnail! 

However, MT565 sent to Custodian, only includes a part of the comment => 

!image-2024-04-17-13-04-01-582.png|thumbnail! 

Could you please check! 
Thanks, Bert 






[This ticket was automatically created]",meet market instruction edited instruction comment inst imagepngthumbnail however mt sent custodian includes part comment imagepngthumbnail could please check thanks bert ticket automatically created,-0.002704992890357971,27.0,411,0.0010594556929076101,0.2506762482225895,1.***************,0.2,0.15,0.15,62.6%
Incident,OEKB-5890,Major,M12-I5: EXTM0000000282 - MATU not mapped correctly,Automation test,2024-04-17 12:33:00,2024-04-17 13:02:00,NO,"EXTM0000000282 
was set up with the new Maturity Date 02/12/2024 => 

!image-2024-04-17-12-27-25-150.png|thumbnail! 

However, in the MT564 OUT, the new MATU is not stated but the old one (under FIA sequence) => 

!image-2024-04-17-12-28-58-960.png|thumbnail! 

New MATU should also be populated under CADETL, see example from M10 => 

!image-2024-04-17-12-29-53-384.png|thumbnail! 


Could you please check? 

Thanks, Bert 








[This ticket was automatically created]",extm set new maturity date imagepngthumbnail however mt new matu stated old one fia sequence imagepngthumbnail new matu also populated cadetl see example imagepngthumbnail could please check thanks bert ticket automatically created,-0.02414643205702305,0.0,411,0.0010594556929076101,0.25603660801425576,1.***************,0.2,0.13,0.15,60.41%
Information Request,OEKB-5889,Major,M12-I5: EXTM0000000282 - No MT564 REPL sent to client,Automation test,2024-04-17 12:28:00,2024-04-22 12:16:00,YES,"EXTM0000000282 
After updating event data and validating the update, I tried to generate/send REPL messages manually, however, only REPL to DEF001 was generated and sent, not the REPL to client 222100 => 

!image-2024-04-17-12-23-26-119.png|thumbnail! 

Please check! 

Thanks, Bert 



[This ticket was automatically created]",extm updating event data validating update tried generatesend repl message manually however repl def generated sent repl client imagepngthumbnail please check thanks bert ticket automatically created,-0.0005094408988952637,4.0,411,0.0010594556929076101,0.2501273602247238,1.***************,0.2,0.13,0.06,46.02%
Incident,OEKB-5888,Major,M12-I5: changed date-format and wrong order in XML-CASHINTERFACE,Automation test,2024-04-17 11:43:00,2024-04-17 18:53:00,NO,"We noticed that the used date-format of Posting Date, Value Date and Settlement Date in the XML-CASHINTERFACE has changed from M10 to M12. 

Here you find an example of M10: 

!image-2024-04-17-11-31-17-814.png! 

Here is an example of how it looks in M12: 

!image-2024-04-17-11-32-46-061.png! 

M10 uses ""-"" in the dates, M12 not. 

Further we noticed that the order of the tags are different now: 

In ActsReq part 
- In M10: CreDtTm, OrgtrId, OrgtrRef, Version 
- In M12: Version, OrgtrRef, OrdtrId, CreDtTm 

In PmtTrfReq part 
- In M10: CdtrRef, DbtrRef, PostDt, TxRef, ValDt 
- In M12: TxRef, ValDt, PostDt, DbtRef, CdtrRef 

The SttlmInf part and SAPInf part look fine 

  

Please check and adapt that - thanks, 

stefan 


[This ticket was automatically created]",noticed used dateformat posting date value date settlement date xmlcashinterface changed find example imagepng example look imagepng us date noticed order tag different actsreq part credttm orgtrid orgtrref version version orgtrref ordtrid credttm pmttrfreq part cdtrref dbtrref postdt txref valdt txref valdt postdt dbtref cdtrref sttlminf part sapinf part look fine please check adapt thanks stefan ticket automatically created,0.012523466721177101,0.0,411,0.0010594556929076101,0.24686913331970572,1.***************,0.2,0.13,0.15,59.03%
Information Request,OEKB-5887,Medium,"M12-I5: After manual sending of notifications via SEND ALL, ""List of sent notifications"" does not appear",Automation test,2024-04-17 11:23:00,2024-04-24 18:57:00,NO,"I noticed the following discrepancy to M10 => 

I manually generated client notifications successfully. 
I manually sent client notifications via ""SEND ALL""-functionality successfully, but 
the screen still shows => 

!image-2024-04-17-11-21-44-342.png|thumbnail! 

This means, no ""List of sent notifications"" appears. 
I also noticed that this is not the case when sending one notification via ""SEND"". 

Could you please check? 

Thanks, Bert 




[This ticket was automatically created]",noticed following discrepancy manually generated client notification successfully manually sent client notification via send allfunctionality successfully screen still show imagepngthumbnail mean list sent notification appears also noticed case sending one notification via send could please check thanks bert ticket automatically created,-0.18111149966716766,7.0,411,0.0010594556929076101,0.2952778749167919,1.***************,0.2,0.1,0.06,48.29%
Incident,OEKB-5886,Major,M12-I5: - Dashboard Alert - Failed Messages,Automation test,2024-04-16 16:33:00,2024-05-10 16:45:00,YES,"Hello, 

I have noticed that some columns and information are missing in the ""Failed messages"" menus. 

For Example: 

Menu ""Failed Received MT566"" - instead of ""List received MT566"" it says ""List sent MT566"". 

Some columns are not available, Sender BIC, Error Description. 

Please also check all other submenus to ensure that the settings are identical to those in M10. 

  

Thank you and BR, Dalibor 


[This ticket was automatically created]",hello noticed column information missing failed message menu example menu failed received mt instead list received mt say list sent mt column available sender bic error description please also check submenu ensure setting identical thank br dalibor ticket automatically created,-0.21598439291119576,24.0,412,0.0010419444305634364,0.30399609822779894,1.***************,0.2,0.13,0.15,67.6%
Incident,OEKB-5885,Major,M12-I5: Generation of FlowOut Failed in MegaBroker,Automation test,2024-04-16 16:18:00,2024-04-19 13:38:00,NO,"Following three FlowOuts are still in Status Generation Failed since last Friday: 1690024, 1690028, 1690029. 

They concern the INTR-events INTR0000000235 and INTR0000000236. 

The error message is the same for all three cases: 

!image-2024-04-16-16-14-59-321.png! 

Please check, what is the problem with these ones. 

  

Thanks, 
Stefan 


[This ticket was automatically created]",following three flowouts still status generation failed since last friday concern intrevents intr intr error message three case imagepng please check problem one thanks stefan ticket automatically created,-0.10379031673073769,2.0,412,0.0010419444305634364,0.2759475791826844,1.***************,0.2,0.13,0.15,63.39%
Information Request,OEKB-5884,Major,M12-I5: DEF005 notifcations create no FlowOut in MB,Automation test,2024-04-16 15:28:00,2024-04-17 10:11:00,NO,"As discussed in the meeting before: Some Outgoing Notifications create no FlowOut in MegaBroker. We found out that it concerns only DEF005 messages. 
* They are shown in MegaCor (SEMEs 2233, 2332, 2338, 2343) 
* They are found in MegaBroker as FlowIn 
* In the FlowIn it is shown a FlowOut (e.g. FlowOut 186002 in FlowIn 2080041): 

!image-2024-04-16-15-21-47-870.png! 

!image-2024-04-16-15-22-11-688.png! 

  

If I want to open the FlowOut 1860042 it is not found in MegaBroker: 

!image-2024-04-16-15-23-20-220.png! 

  

That is the same for all four examples since last Friday. 

Please check what is the problem with the DEF005 FlowOuts. 

Thanks, stefan 


[This ticket was automatically created]",discussed meeting outgoing notification create flowout megabroker found concern def message shown megacor semes found megabroker flowin flowin shown flowout eg flowout flowin imagepng imagepng want open flowout found megabroker imagepng four example since last friday please check problem def flowouts thanks stefan ticket automatically created,0.0032183118164539337,0.0,412,0.0010419444305634364,0.24919542204588652,1.***************,0.2,0.13,0.06,45.88%
Incident,OEKB-5883,Critical,M12-I5: LIQU - XML-CASHINTERFACE with wrong Date,Automation test,2024-04-16 11:53:00,2024-04-16 13:38:00,NO,"Event LIQU0000000229 

in the outgoing XML-CASHINTERFACE we found the following date: 

!image-2024-04-16-11-48-45-680.png! 

In past tests in other messages we had already the mysterious month ""00"" - please check that also here as this message would most likely be rejected by SAP. 

Thanks, stefan 


[This ticket was automatically created]",event liqu outgoing xmlcashinterface found following date imagepng past test message already mysterious month please check also message would likely rejected sap thanks stefan ticket automatically created,-0.11068085581064224,0.0,412,0.0010419444305634364,0.27767021395266056,1.***************,0.2,0.15,0.15,66.65%
Incident,OEKB-5882,Blocker,M12-I5: SPLR0000000160 - Payments fail - sese.023 invalid,Automation test,2024-04-16 10:03:00,2024-04-16 14:24:00,NO,"SPLR0000000160 
After creating Market and Client Payments (sese.023 attached), we received sese.024 (attached) from T2S SIMU with error text 

_INVALID SETTLEMENT TRANSACTION TYPE. YOU ARE NOT ALLOWED TO INSTRUCT THIS TRANSACTION TYPE_ 

Not sure what is meant by ""invalid settlement transaction type"", however, we noticed that Trade Date is mapped incorrectly (Trade Date = business date instead of Pay Date) => 

!image-2024-04-16-09-58-30-593.png|thumbnail! 

Please check this, mapping must be as in M10 (Trade Date and Intended Settlement Date = Pay Date) => 

!image-2024-04-16-09-58-10-254.png|thumbnail! 


Moreover, the sese.023 includes an incorrect nostro sec. account => 

!image-2024-04-16-10-07-26-331.png! 

which is not available in T2S/CCSYS SIMU. 


Thanks, Bert 

[This ticket was automatically created]",splr creating market client payment sese attached received sese attached t simu error text invalid settlement transaction type allowed instruct transaction type sure meant invalid settlement transaction type however noticed trade date mapped incorrectly trade date business date instead pay date imagepngthumbnail please check mapping must trade date intended settlement date pay date imagepngthumbnail moreover sese includes incorrect nostro sec account imagepng available tsccsys simu thanks bert ticket automatically created,-0.*****************,0.0,412,0.0010419444305634364,0.****************,1.***************,0.2,0.14,0.15,67.12%
Incident,OEKB-5881,Medium,M12-I5: - DVCA0000000234 - Update issue,Automation test,2024-04-12 16:58:00,2024-04-15 17:08:00,NO,"DVCA0000000234 

the above event has been updated manually (ADTX). 

However, the currency and "" Ca Out$Order"" have also been updated.  

I don't understand what the two updates mean. 

  

Please check! 

  

Thank you and BR, Dalibor 


[This ticket was automatically created]",dvca event updated manually adtx however currency ca outorder also updated dont understand two update mean please check thank br dalibor ticket automatically created,-0.013049500063061714,3.0,415,0.000991128201046581,0.*****************,1.***************,0.2,0.1,0.15,55.49%
Incident,OEKB-5880,Major,M12-I5: CHAN0000000232 - XDTE filled for unknown reasons,Automation test,2024-04-12 13:33:00,2024-04-12 13:42:00,NO,"CHAN0000000232 
was set up due to incoming MT564 which contains no XDTE => 

:16R:CADETL 
:98A::ANOU//20240223 
:98A::EFFD//20240315 
:17B::RCHG//N 
:22F::CHAN//TERM 
:16S:CADETL 

However, XDTE was filled in event data => 

!image-2024-04-12-13-29-33-893.png|thumbnail! 

Please check! 
Thanks, Bert 



[This ticket was automatically created]",chan set due incoming mt contains xdte rcadetl aanou aeffd brchgn fchanterm scadetl however xdte filled event data imagepngthumbnail please check thanks bert ticket automatically created,0.03538423962891102,0.0,416,0.0009747462928068856,0.24115394009277225,1.***************,0.2,0.13,0.15,58.17%
Incident,OEKB-5879,Major,M12-I5: CHAN0000000232 - Incorrect Mapping of Event Purpose,Automation test,2024-04-12 13:23:00,2024-04-22 10:48:00,NO,"CHAN0000000232 
was set up due to incoming MT564 which includes => 

:22F::CHAN//TERM 

However, Event Purpose was mapped to NAME in event data (and MT564 OUT) => 

!image-2024-04-12-13-16-37-012.png|thumbnail! 

Please check! 
BR Bert 




[This ticket was automatically created]",chan set due incoming mt includes fchanterm however event purpose mapped name event data mt imagepngthumbnail please check br bert ticket automatically created,0.0017405245453119278,9.0,416,0.0009747462928068856,0.24956486886367202,1.***************,0.2,0.13,0.15,59.43%
Incident,OEKB-5878,Major,M12-I5: CHAN0000000154 - MT564 contains incorrect value (:25D::PROC),Automation test,2024-04-12 12:55:00,2024-04-22 10:40:00,NO,"CHAN0000000154 
Complete flag is set to true => 

!image-2024-04-12-12-51-40-407.png|thumbnail! 

However, MT564 OUT contains 
:25D::PROC//PREC 
which is incorrect. 

Please check! 
Thanks, Bert 




[This ticket was automatically created]",chan complete flag set true imagepngthumbnail however mt contains dprocprec incorrect please check thanks bert ticket automatically created,0.04150140844285488,9.0,416,0.0009747462928068856,0.23962464788928628,1.***************,0.2,0.13,0.15,57.94%
Incident,OEKB-5877,Major,M12-I5: Smoke-Tests - MT564NEWM messages sent to the wrong clients,Automation test,2024-04-11 16:07:00,2024-04-25 11:04:00,YES,"test with INTR0000000197 - AT0000A1U9N3 

Hello, 

Today I manually created a position in the ISIN mentioned above. (Client 2292 and 2295). 

I then created an INTR event manually and sent MT564 NEWM. 

What I noticed, is that the message has been sent to a client (247200 and 247289) who does not have an holding in this ISIN. 

In addition, the message to 229200 was not sent. 

  

Please check. 

  

Thank you and BR, Dalibor 

  

  


[This ticket was automatically created]",test intr ataun hello today manually created position isin mentioned client created intr event manually sent mt newm noticed message sent client holding isin addition message sent please check thank br dalibor ticket automatically created,-0.004903145134449005,13.0,417,0.0009586351536940199,0.25122578628361225,1.***************,0.2,0.13,0.15,59.68%
Incident,OEKB-5876,Major,M12-I5: SPLR0000000160 - CA Status incorrect after calculation of entitlements,Automation test,2024-04-11 15:28:00,2024-04-11 15:40:00,NO,"SPLR0000000160 
Entitlements have been calculated manually => 

!image-2024-04-11-15-26-16-914.png|thumbnail! 

However, CA Status still is ""Notified"" => 

!image-2024-04-11-15-25-53-645.png|thumbnail! 

Could you please check? 
Thanks, Bert 




[This ticket was automatically created]",splr entitlement calculated manually imagepngthumbnail however ca status still notified imagepngthumbnail could please check thanks bert ticket automatically created,0.0095283892005682,0.0,417,0.0009586351536940199,0.24761790269985795,1.***************,0.2,0.13,0.15,59.14%
Incident,OEKB-5875,Medium,cant reprocess faild position when costodian was created afterwards (NBB),Lukasz Walczuk,2024-04-09 12:43:00,2024-04-09 12:53:00,NO,"Hello Oussema, 

  

we have created the custodian after the failed position, but I can't reprocess the failed position . do you know what the problem could be? 

Please find attached the screenshot. Same with the second ISIN 

  

<SecPosition safeAcc=""OCSD222100"" ISIN=""BE0000346552"" safeType=""GS"" safePlace=""OCSD9751200"" secsQuan=""1000000.00000"" /> 

    <SecPosition safeAcc=""OCSD222100"" ISIN=""BE0001735860"" safeType=""GS"" safePlace=""OCSD9751200"" secsQuan=""1000000.00000"" /> 

  

BR 

Lukasz",hello oussema created custodian failed position cant reprocess failed position know problem could please find attached screenshot second isin secposition safeaccocsd isinbe safetypegs safeplaceocsd secsquan secposition safeaccocsd isinbe safetypegs safeplaceocsd secsquan br lukasz,-0.18362148851156235,0.0,419,0.0009272073551408984,0.2959053721278906,0.002645083300698369,0.000501194110431838,0.1,0.15,31.96%
Incident,OEKB-5874,Major,M12-I2: SPLR0000000160 - No Entitlements calculated via job,Automation test,2024-04-09 09:48:00,2024-04-12 10:50:00,NO,"SPLR0000000160 
Event was set up with Record Date 08/04. 
When checking the event today (09/04), I noticed that no entitlement calculation has taken place, CA Status is still ""Notified"". 

Could you please check! 

Thanks, Bert 


[This ticket was automatically created]",splr event set record date checking event today noticed entitlement calculation taken place ca status still notified could please check thanks bert ticket automatically created,0.006471680477261543,3.0,419,0.0009272073551408984,0.24838207988068461,1.***************,0.2,0.13,0.15,59.26%
Incident,OEKB-5873,Critical,M12-I5: Smoke-Tests - No Auto-Reject and Reject-Button missing,Automation test,2024-04-05 09:43:00,2024-04-08 11:11:00,YES,"For event TEND0000000111 an instruction has been received for Option 002 and SECU (which is actually Option 001) - the instruction got incorrectly Status InvalidData. It should be Auto-rejected but no MT567 has been sent out for this incoming MT565. 

Further: in Repair screen we have only the possibility to 'Repair' 

!image-2024-04-05-09-40-03-689.png! but not 'Reject' as it exists in M10: 

!image-2024-04-05-09-42-39-833.png! 

Please adapt this screen or explain how a Reject is correctly handled in M12. 

And more important: Why was there no Auto-reject for this instruction? 

BR, 
stefan 

[This ticket was automatically created]",event tend instruction received option secu actually option instruction got incorrectly status invaliddata autorejected mt sent incoming mt repair screen possibility repair imagepng reject exists imagepng please adapt screen explain reject correctly handled important autoreject instruction br stefan ticket automatically created,-0.12930479645729065,3.0,423,0.0008674089573070025,0.28232619911432266,1.***************,0.2,0.15,0.15,67.35%
Incident,OEKB-5872,Major,M12-I5: Smoke-Tests - Error when receiving MT565,Automation test,2024-04-05 09:38:00,2024-04-05 10:44:00,NO,"Event TEND0000000111 

Attached MT565 has been imported into MegaCor, unfortunately following error description is shown: 

!image-2024-04-05-09-32-50-535.png! 

What does this error mean? What is wrong with this instruction? 

Please check that - thanks, stefan 


[This ticket was automatically created]",event tend attached mt imported megacor unfortunately following error description shown imagepng error mean wrong instruction please check thanks stefan ticket automatically created,-0.19932381995022297,0.0,423,0.0008674089573070025,0.29983095498755574,1.***************,0.2,0.13,0.15,66.97%
Incident,OEKB-5871,Major,M12-I5: Smoke-Tests - Client Instruction 'BlockedForCutOffTime',Automation test,2024-04-05 09:38:00,2024-05-17 13:56:00,NO,"Event TEND0000000111 

The same instruction as reported in bug OEKB-5872 is in Status 'BlockedForCutOffTime' 

!image-2024-04-05-09-36-20-225.png! 

As we know this status only for payment instructions to CCSYS we do not understand that. 

Please check and explain that. 

Thanks, stefan 

[This ticket was automatically created]",event tend instruction reported bug oekb status blockedforcutofftime imagepng know status payment instruction ccsys understand please check explain thanks stefan ticket automatically created,-0.037716735154390335,42.0,423,0.0008674089573070025,0.2594291837885976,1.***************,0.2,0.13,0.15,60.91%
Incident,OEKB-5870,Major,CLONE - M12-I5: Smoke-Tests - Securities Events File manual import,Stefan RIBISCH,2024-04-04 15:57:00,2024-04-17 13:05:00,YES,"Dear All,  

I'm facing the problem that I can't import a Securities Events xml.  
I was trying to load it under following device:  
/opt/jboss/Megara/Devices/MegaBroker/zwp-in 

  

Please see also the file which I tried to load attached to this ticket.  

Thanks & KR,  

Eli",dear im facing problem cant import security event xml trying load following device optjbossmegaradevicesmegabrokerzwpin please see also file tried load attached ticket thanks kr eli,-0.21205143630504608,12.0,424,0.0008530719483006112,0.3030128590762615,0.*****************,0.*****************,0.13,0.15,40.57%
Incident,OEKB-5869,Critical,M12-I5: Smoke-Tests - Performance MegaBroker,Automation test,2024-04-04 15:43:00,2024-05-08 17:49:00,NO,"Today we created Market Payments for two events: DVCA0000000091 and SPLR0000000123. 

Concerning SPLR0000000123 we found 10 SESE.023 messages (2 for MP, 8 for CP) in MegaBroker. They were created on 12.04 and had status CREATED until 12.27: 

!image-2024-04-04-15-26-35-792.png! 

Concerning DVCA0000000091 an XML-CASHINTERFACE is found in MB. This has been received at 12.12 and was also in CREATED until 12.27: 

!image-2024-04-04-15-29-16-420.png! 

15 resp. 23 minutes is definitely too lang for processing. Why did it take so long? 

As both messages have changed the status at exactly 12:27:04: What happened at this time? Is there now a batch job which processes these notifications? Are they not processed realtime anymore? 

In addition: We checked further the receiving systems CCSYS and SAP and these notifications never reached CCSYS or SAP. Therefore we created an internal ticket to OEKB IT - is there anything you can contribute to this topic too? Anyway it unfortunately blocks our UAT as this is an essential functuality and necessary for the UAT tests. 

Thanks, stefan 


[This ticket was automatically created]",today created market payment two event dvca splr concerning splr found sese message mp cp megabroker created status created imagepng concerning dvca xmlcashinterface found mb received also created imagepng resp minute definitely lang processing take long message changed status exactly happened time batch job process notification processed realtime anymore addition checked receiving system ccsys sap notification never reached ccsys sap therefore created internal ticket oekb anything contribute topic anyway unfortunately block uat essential functuality necessary uat test thanks stefan ticket automatically created,0.0064544156193733215,34.0,424,0.0008530719483006112,0.24838639609515667,1.***************,0.2,0.15,0.15,62.26%
Incident,OEKB-5868,Major,M12-I5: Smoke-Tests - Ca Status not correct,Automation test,2024-04-03 15:13:00,2024-04-04 11:24:00,NO,"Event DVCA0000000091 

Today is Paydate of this event, but the Ca Status is still 'Notified' - it should be 'Waiting Payment'. 

Please check why the status didn't switch. 

Thanks, stefan 


[This ticket was automatically created]",event dvca today paydate event ca status still notified waiting payment please check status didnt switch thanks stefan ticket automatically created,-0.030994771048426628,0.0,425,0.0008389719092096418,0.*****************,1.***************,0.2,0.13,0.15,60.66%
Incident,OEKB-5867,Major,M12-I5: Smoke-Tests - ACK from SWIFT Network is not processed in MB/MC,Automation test,2024-04-03 10:53:00,2024-04-04 13:11:00,YES,"In addition to bug OEKB-5865 one of the eleven in Alliance received SWIFTs received an ACK from SWIFT Network: CS2102 which belongs to DVCA0000000091 and was sent for account 222200. 

Unfortunately this ACK is not received in MegaBroker/MegaCor, so the status of the notification in MegaCor is still ""Waiting Ack"": 

!image-2024-04-03-10-44-42-949.png! 

Eli meant, that the reason could be a missing 'Input Device' as for MX there is existing one, but for MT not - please check why the ACKs are not received and processed. 

Thanks, stefan 


[This ticket was automatically created]",addition bug oekb one eleven alliance received swift received ack swift network c belongs dvca sent account unfortunately ack received megabrokermegacor status notification megacor still waiting ack imagepng eli meant reason could missing input device mx existing one mt please check acks received processed thanks stefan ticket automatically created,-0.*****************,1.0,425,0.0008389719092096418,0.****************,1.***************,0.2,0.13,0.15,62.75%
Incident,OEKB-5866,Critical,M12-I5: Smoke-Tests - SWIFTs OUT are NACKED in Alliance,Automation test,2024-04-03 10:33:00,2024-04-03 10:36:00,YES,"Many messages are processed in MegaBroker and sent to SWIFT Network and in Alliance they get the Status NACK due to following errors - here are two examples: 

- seev.001 for event PROX0000000039 to FDSEATXXXXX for account DEF001 (Message Reference CS2235) 
!image-2024-04-03-10-22-27-349.png! 

- MT564 for event EXRI0000000020 to ZYAFATW0 for account 204500 (SEME CS2215) 
!image-2024-04-03-10-28-53-057.png! 

Please check why these messages are NACKed from SWIFT Network. 

Thanks, stefan 


[This ticket was automatically created]",many message processed megabroker sent swift network alliance get status nack due following error two example seev event prox fdseatxxxxx account def message reference c imagepng mt event exri zyafatw account seme c imagepng please check message nacked swift network thanks stefan ticket automatically created,-0.*****************,0.0,425,0.0008389719092096418,0.****************,1.***************,0.2,0.15,0.15,63.1%
Incident,OEKB-5865,Critical,M12-I5: Smoke-Tests - SWIFTs OUT are rejected locally in Alliance,Automation test,2024-04-03 10:13:00,2024-04-03 10:45:00,NO,"In addition to my last comment from today to bug OEKB-5850 we noticed that from the 11 SWIFTs which were processed in MegaBroker and were received in Alliance, 10 out of them were Rejected locally: 

!image-2024-04-03-10-07-07-452.png! 

The error message in every of these cases is the same: 

!image-2024-04-03-10-07-25-601.png! 

Please check what is the problem with these SWIFTs. 

Thanks, stefan 


[This ticket was automatically created]",addition last comment today bug oekb noticed swift processed megabroker received alliance rejected locally imagepng error message every case imagepng please check problem swift thanks stefan ticket automatically created,-0.*****************,0.0,425,0.0008389719092096418,0.****************,1.***************,0.2,0.15,0.15,69.59%
Incident,OEKB-5864,Major,M12-I5 NEW: Tax Payer Category not set up,Automation test,2024-04-02 14:48:00,2024-05-17 16:44:00,NO,"At event DVCA0000000040 we noticed that no US tax has been generated for any client. We found out that the reason is that no Tax Payer Category is filled in at the client securities accounts as the logic has been changed between M10 and M12 and no Fiscal Group is needed anymore but a Tax Payer Category at the securities account. 

The Fiscal Rule SLAs are set up but have no effect without the Tax Payer Category in the account if we understood everything correctly. 

Please check why this is not yet setup correctly and when this will happen. 

Thanks, stefan 


[This ticket was automatically created]",event dvca noticed u tax generated client found reason tax payer category filled client security account logic changed fiscal group needed anymore tax payer category security account fiscal rule slas set effect without tax payer category account understood everything correctly please check yet setup correctly happen thanks stefan ticket automatically created,0.014246426522731781,45.0,426,0.0008251049232659046,0.*****************,1.***************,0.2,0.13,0.15,58.97%
Information Request,OEKB-5863,Major,M12-I5: Smoke-Tests - Incorrect time information on outgoing seev.001,Automation test,2024-04-02 11:43:00,2024-04-19 19:29:00,NO,"Event PROX0000000050 

unfortunately all the time calculations are not correct again. Attached you find the flowIn and the flowOut - none of the included times were calculated correctly: 
* IssrDdlnForVtng --> Flow In: 2024-04-19T11:00:00Z; Flow Out: 2024-04-19T12:00:00:000+01:00; Correct would be 2024-04-19T13:00:00:000+02:00 
* MtgDtls --> FlowIn: 2024-04-25T13:30:00.000+02:00; FlowOut: 2024-04-25T13:30:00.000+01:00; Correct would be 2024-04-25T13:30:00.000+02:00 
* VoteDdln and RvcbltyDdln --> FlowIn: 2024-04-19T10:45:00Z; FlowOut: 2024-04-19T11:45:00:000+01:00; Correct would be 2024-04-19T12:45:00:000+02:00 
* VoteMktDdln --> FlowIn: 2024-04-19T11:00:00Z; FlowOut: 2024-04-19T00:00:00:000+01:00; Correct would be 2024-04-19T13:00:00:000+02:00 

Please check again the whole logic as this worked already fine in old M12 environment 


[This ticket was automatically created]",event prox unfortunately time calculation correct attached find flowin flowout none included time calculated correctly issrddlnforvtng flow tz flow correct would mtgdtls flowin flowout correct would voteddln rvcbltyddln flowin tz flowout correct would votemktddln flowin tz flowout correct would please check whole logic worked already fine old environment ticket automatically created,0.03427501767873764,17.0,426,0.0008251049232659046,0.2414312455803156,1.***************,0.2,0.13,0.06,44.71%
Information Request,OEKB-5861,Medium,Easter Monday - not captured,Valdes ELIZABEHT,2024-04-02 09:28:00,2024-04-02 10:24:00,NO,"Dear All,  

I just noticed, that in M12 we missed to set up the holidays.  

Therefore the 01.04.2024 was not set as holiday in M12 and it'S not processing the position deltas. 

Can you plesae tell us how we can handle this Issue now.  

Thanks & KR,  

Eli  

 ",dear noticed missed set holiday therefore set holiday processing position delta plesae tell u handle issue thanks kr eli,-0.052989229559898376,0.0,426,0.0008251049232659046,0.2632473073899746,0.*****************,0.012503434490811616,0.1,0.06,15.36%
Information Request,OEKB-5860,Major,M12 NEW - Client Notification Generation Batch Job didn't run,Automation test,2024-03-29 10:08:00,2024-03-29 14:33:00,YES,"e.g. Event PROX0000000050 

Event was created after 9.00 and now at 10.03 no notifiactions were generated: 

!image-2024-03-29-10-03-57-412.png! 

I checked the batch job execution and noticed that at 8.30 the client notification generation batch job ran, but at 9.30 not: 

!image-2024-03-29-10-02-05-753.png! 

What is the problem here? Normally it runs every hour. 

Further: I thought - and it has been changed already - Oussema changed the starting time to 55 instead of 30 - why is it back at 30 again? 

Please check that. 

Thanks, stefan 

[This ticket was automatically created]",eg event prox event created notifiactions generated imagepng checked batch job execution noticed client notification generation batch job ran imagepng problem normally run every hour thought changed already oussema changed starting time instead back please check thanks stefan ticket automatically created,-0.162645461037755,0.0,430,0.0007718914190992302,0.29066136525943875,1.***************,0.2,0.13,0.06,52.1%
Information Request,OEKB-5859,Medium,M12 NEW - General - Error message when using Repair Received Feed screen,Automation test,2024-03-28 16:58:00,2024-03-28 23:36:00,NO,"I wanted to see all entries since today midnight but got following error message: 

!image-2024-03-28-16-55-50-293.png! 

Please check - thanks, 

stefan 


[This ticket was automatically created]",wanted see entry since today midnight got following error message imagepng please check thanks stefan ticket automatically created,-0.03881067596375942,0.0,430,0.0007718914190992302,0.25970266899093986,1.***************,0.2,0.1,0.06,42.96%
Incident,OEKB-5858,Critical,M12-I5: Smoke-Tests - MT566 failed in MegaBroker,Automation test,2024-03-28 16:53:00,2024-03-29 00:58:00,YES,"Event DVCA0000000040 

A few minutes ago I wanted to import an MT566 but unfortunately it failed in MB due to: 

!image-2024-03-28-16-49-40-282.png! 

Please check what is the problem - thanks, stefan 


[This ticket was automatically created]",event dvca minute ago wanted import mt unfortunately failed mb due imagepng please check problem thanks stefan ticket automatically created,-0.07888805493712425,0.0,430,0.0007718914190992302,0.26972201373428106,1.***************,0.2,0.15,0.15,65.46%
Incident,OEKB-5857,Medium,M12-I5: Smoke-Tests - Information at Clients Entitlements,Automation test,2024-03-28 16:14:00,2024-04-02 13:08:00,NO,"Event DVCA0000000040 

Today I calculated the client entitlements and received the following message immediately: 

!image-2024-03-28-16-01-03-765.png! 

After clicking on OK I saw that three entitlements were marked in red colour: 

!image-2024-03-28-16-01-29-256.png! 

When viewing one of them, the system says: 

!image-2024-03-28-16-02-23-183.png! 

We assume that the reason is that there is no USD-cash-account available for these accounts but we do not really know it. 

If the system sets a status on Invalid Data the user needs to know why this happened and what is wrong. Please adapt the information message and/or provide more details of 'InvalidData'. 

Thanks, stefan 


[This ticket was automatically created]",event dvca today calculated client entitlement received following message immediately imagepng clicking ok saw three entitlement marked red colour imagepng viewing one system say imagepng assume reason usdcashaccount available account really know system set status invalid data user need know happened wrong please adapt information message andor provide detail invaliddata thanks stefan ticket automatically created,-0.*****************,4.0,431,0.0007591331761339514,0.****************,1.***************,0.2,0.1,0.15,55.6%
Information Request,OEKB-5856,Critical,M12 NEW - File device not working,Automation test,2024-03-28 15:28:00,2024-03-28 15:45:00,NO,"Concerning our bugs OEKB-5836, OEKB-5837, OEKB-5838 and OEKB-5855 we found out the following: 

The communication between WinSCP-Dropfolder and MegaCor doesn't work. The FileDevices are correctly configured in MegaCor but the files are not picked up and do not reach MegaCor. 

We use the following Host Name in WinSCP: web-megara01q.oekb.co.at, Port 22, User: jboss 

Isn't this correct? We saw that someone - we assume it's you - successfully imported files. 

  

Further we found out that the missing files of the above four environments came into the OLD M12 QAS because the file device and the queue device were still activated in OLD M12 QAS - how could that be? 

Please advise - thanks. 

stefan",concerning bug oekb oekb oekb oekb found following communication winscpdropfolder megacor doesnt work filedevices correctly configured megacor file picked reach megacor use following host name winscp webmegaraqoekbcoat port user jboss isnt correct saw someone assume successfully imported file found missing file four environment came old qas file device queue device still activated old qas could please advise thanks stefan,-0.01441853865981102,0.0,431,0.0007591331761339514,0.25360463466495275,1.***************,0.2,0.15,0.06,49.54%
Incident,OEKB-5855,Critical,M12 NEW - Position Interface FAILURE,Stefan RIBISCH,2024-03-28 14:23:00,2024-03-28 15:13:00,NO,"The position interface was the only one of the four interface files which worked correctly last week. 

Unfortunately beginning with the not-processing of the full-load on 22/03/2024 it began and afterwards it is partly successful and partly failed: 

!image-2024-03-28-13-51-19-496.png! 

According to the folder, beginning with this week totally 14 files should have been processed, only 8 were processed. Only two of them successful although for everyday a full load file is available: 

!image-2024-03-28-13-52-43-800.png! 

And here is also the ""records Count vs Number of Records""-topic existing. 

In Addition we have the last 'Failed Position Interface' entry created last week - no more this week although eight files were processed? Could that be? 

Please check that. 

Thanks, stefan",position interface one four interface file worked correctly last week unfortunately beginning notprocessing fullload began afterwards partly successful partly failed imagepng according folder beginning week totally file processed processed two successful although everyday full load file available imagepng also record count v number recordstopic existing addition last failed position interface entry created last week week although eight file processed could please check thanks stefan,-0.05096850171685219,0.0,431,0.0007591331761339514,0.26274212542921305,0.*****************,0.*****************,0.15,0.15,37.53%
Information Request,OEKB-5853,Critical,M12-I5: Smoke-Tests - Sending of seev.045 failed in MB,Automation test,2024-03-26 13:23:00,2024-03-26 13:42:00,NO,"Event SDHS0000000021 

following on bug OEKB-5852 we checked the messages in MegaBroker and noticed that all five ended in Status SENDING FAILED: 

!image-2024-03-26-13-20-26-483.png! 

Error message is for all the same: 

!image-2024-03-26-13-21-02-383.png! 

Eli checked already if there is a queue or something like that not opened but she couldn't find out the reason. 

Please check what is wrong here. 

Thanks, stefan 


[This ticket was automatically created]",event sdhs following bug oekb checked message megabroker noticed five ended status sending failed imagepng error message imagepng eli checked already queue something like opened couldnt find reason please check wrong thanks stefan ticket automatically created,-0.****************,0.0,433,0.0007342458303668003,0.*****************,1.***************,0.2,0.15,0.06,72.62%
Incident,OEKB-5852,Medium,M12-I5: Smoke-Tests - Not all notifications sent with next job,Automation test,2024-03-26 13:18:00,2024-03-26 13:20:00,NO,"Event SHDS0000000021 

The event has been created today and on 10:30 all five notifications have been generated (three ""normal"" clients and two DEF-accounts). At 11:21 four of them has been sent out, the fifth has not been sent. 

Later - I just wanted to create this bug - I noticed that the last notification has been sent out at 12:15 - so one job later. 

!image-2024-03-26-13-14-57-289.png! How and why could this happen? 

Could it be related to the fact that the notification generation job now runs at '30' instead of '55'? 

Thanks for your feedback. 

BR, stefan 


[This ticket was automatically created]",event shds event created today five notification generated three normal client two defaccounts four sent fifth sent later wanted create bug noticed last notification sent one job later imagepng could happen could related fact notification generation job run instead thanks feedback br stefan ticket automatically created,-0.****************,0.0,433,0.0007342458303668003,0.***************,1.***************,0.2,0.1,0.15,76.4%
Incident,OEKB-5850,Critical,M12-I5: Smoke-Tests - Not all MT564 OUT processed in MB,Automation test,2024-03-25 16:30:00,2024-03-28 10:22:00,NO,"Event DVCA0000000017 

ON 15:15 eight MT564 have been sent to MegaBroker - five of them have been processed, and three not: 

!image-2024-03-25-16-03-08-952.png! 

Please check what is the problem with some of these notifications - thanks. 

BR, stefan 


[This ticket was automatically created]",event dvca eight mt sent megabroker five processed three imagepng please check problem notification thanks br stefan ticket automatically created,0.002009991556406021,2.0,434,0.0007221098142532979,0.****************,1.***************,0.2,0.15,0.15,62.42%
Information Request,OEKB-5849,Minor,M12-I5: Smoke-Tests - Creation of 'SLA_AT' SLA not possible,Automation test,2024-03-25 15:43:00,2024-03-25 16:18:00,NO,"To be able to create event EXRI0000000020 we had to create the general SLA as it already exists in M10: 

!image-2024-03-25-15-39-28-798.png! 

As AFTER the creation of this SLA in PROD the ISIN-field became a mandatory field we couldn't create the SLA without entering an ISIN. So for every AT-event in the future we would have to create a separate SLA. 

Do you have an idea how we could create it in M12 NEW? Our suggest would be: 

1) You deactivate the mandatory ISIN-field 
2) I adapt the existing SLA by deleting the ISIN 
3) You activate the mandatory field again 

Otherwise YOU could directly delete the inserted ISIN by yourself? 

!image-2024-03-25-15-42-24-058.png! 

  

Please advise how we can solve this. 

Thanks, stefan 


[This ticket was automatically created]",able create event exri create general sla already exists imagepng creation sla prod isinfield became mandatory field couldnt create sla without entering isin every atevent future would create separate sla idea could create new suggest would deactivate mandatory isinfield adapt existing sla deleting isin activate mandatory field otherwise could directly delete inserted isin imagepng please advise solve thanks stefan ticket automatically created,-0.003144463524222374,0.0,434,0.0007221098142532979,0.2507861158810556,1.***************,0.2,0.07,0.06,37.12%
Incident,OEKB-5848,Critical,M12-I5: Smoke-Tests - Import of seev.001 and seev.045 not possible,Automation test,2024-03-25 10:43:00,2024-03-25 13:08:00,YES,"Today I wanted to import two SRD II messages - both failed in MB: 

!image-2024-03-25-10-39-35-896.png! 

due to following error: 

!image-2024-03-25-10-40-00-213.png! 

Please check why SRD II messages fail in MB - I attached them to this bug for you too. 

Thanks and BR, stefan 


[This ticket was automatically created]",today wanted import two srd ii message failed mb imagepng due following error imagepng please check srd ii message fail mb attached bug thanks br stefan ticket automatically created,-0.19303448125720024,0.0,434,0.0007221098142532979,0.29825862031430006,1.***************,0.2,0.15,0.15,69.74%
Incident,OEKB-5847,Critical,M12-I5: Smoke-Tests - Import of MT564 failed in MegaBroker,Automation test,2024-03-22 14:13:00,2024-03-24 23:59:00,NO,"I wanted to import the attached MT564 to create a TEND but unfortunately it failed in MegaBroker due to following reason: 

!image-2024-03-22-14-11-58-422.png! and never reached MegaCor. Yesterday in amport with similar data worked. 

To compare them I attached both for you and created here a screenshot of the header: 

!image-2024-03-22-13-05-38-192.png! 

Please advise why this message from today is not processed in MegaBroker. 

Thanks, stefan 


[This ticket was automatically created]",wanted import attached mt create tend unfortunately failed megabroker due following reason imagepng never reached megacor yesterday amport similar data worked compare attached created screenshot header imagepng please advise message today processed megabroker thanks stefan ticket automatically created,-0.025309674441814423,2.0,437,0.0006868921030384822,0.2563274186104536,1.***************,0.2,0.15,0.15,63.45%
Information Request,OEKB-5846,Critical,M12-I5: Smoke-Tests - Security-Event-File is not processed correctly in MB,Automation test,2024-03-22 10:38:00,2024-03-25 14:24:00,YES,"Attached you find the Security-Event-Input files of yesterday 8:51, 12:41, 15:41 and 18:41 which contained each more events (40, 13, 7 and 1) as received in MegaCor (11): 

!image-2024-03-22-10-25-50-164.png! 

What happened with the other fifty events? In MB the fourth one at 18:41 is still in Status 'Waiting Message Generation' which explains that there is no incoming notification of this file of 18:41. All the other look good in MB, no error is shown, except of course that there are massive less Flow Outs than Records in the files. 

Please check where this discrepancy comes from - thanks! 

  

Further, if we want to open one of the incoming notifications in MegaCor, an error similar to the one in OEKB-5842 appears and the message cannot be opened: 

!image-2024-03-22-10-41-58-977.png! 

BR, stefan 

[This ticket was automatically created]",attached find securityeventinput file yesterday contained event received megacor imagepng happened fifty event mb fourth one still status waiting message generation explains incoming notification file look good mb error shown except course massive less flow out record file please check discrepancy come thanks want open one incoming notification megacor error similar one oekb appears message opened imagepng br stefan ticket automatically created,-0.02693454548716545,3.0,437,0.0006868921030384822,0.25673363637179136,1.***************,0.2,0.15,0.06,50.01%
Information Request,OEKB-5845,Critical,M12-I5: Smoke-Tests - Sending of DEF005 Notification failed in MB,Automation test,2024-03-21 17:13:00,2024-03-22 08:14:00,YES,"For event DVCA0000000010 there should have been sent a DEF005 message to 3i. 

The message is found in MB, but sending failed due to following error: 

!image-2024-03-21-17-06-40-584.png! 

I attached the generated message also to this bug. 

Please check what is the problem here. 

Thanks, stefan 


[This ticket was automatically created]",event dvca sent def message message found mb sending failed due following error imagepng attached generated message also bug please check problem thanks stefan ticket automatically created,-0.42038823291659355,0.0,437,0.0006868921030384822,0.3550970582291484,1.***************,0.2,0.15,0.06,64.76%
Information Request,OEKB-5844,Major,M12-I5: Smoke-Tests - So many Client reporting SLAs,Automation test,2024-03-21 16:48:00,2024-03-27 12:47:00,NO,"Event DVCA0000000010 

we noticed that for two clients (211600 and 239900) no notification has been generated and found out that there are client reporting SLAs existing for these two clients which say ""Notification = NO"". In old M12 environment and also in M10 these SLAs are not existing. Why are they existing in new M12 environment? 

Further we noticed that compared to M12 OLD (232) there are existing over 1000 SLAs more in M12 NEW (1318). Can you explain that as we didn't expact and don't understand that. 

Thanks and BR, stefan 


[This ticket was automatically created]",event dvca noticed two client notification generated found client reporting slas existing two client say notification old environment also slas existing existing new environment noticed compared old existing slas new explain didnt expact dont understand thanks br stefan ticket automatically created,-0.016973629593849182,5.0,437,0.0006868921030384822,0.2542434073984623,1.***************,0.2,0.13,0.06,46.64%
Information Request,OEKB-5843,Major,M12-I5: Smoke-Tests - Securities Events File manual import,Valdes ELIZABEHT,2024-03-21 16:40:00,2024-03-21 23:22:00,YES,"Dear All,  

I'm facing the problem that I can't import a Securities Events xml.  
I was trying to load it under following device:  
/opt/jboss/Megara/Devices/MegaBroker/zwp-in 

  

Please see also the file which I tried to load attached to this ticket.  

Thanks & KR,  

Eli",dear im facing problem cant import security event xml trying load following device optjbossmegaradevicesmegabrokerzwpin please see also file tried load attached ticket thanks kr eli,-0.21205143630504608,0.0,438,0.0006755387751938444,0.3030128590762615,0.*****************,0.012503434490811616,0.13,0.06,25.83%
Incident,OEKB-5842,Critical,M12-I5: Smoke-Tests - Message references wrong?,Automation test,2024-03-21 15:08:00,2024-03-22 08:31:00,NO,"Event DVCA0000000010 

When checking the outgoing sent notifications MT564 I noticed that the Message Reference looks strange: !image-2024-03-21-15-01-29-888.png! 

Unfortunately we cannot check the message itself and have a look at the sent message due to following errer when trying to view the message: 

!image-2024-03-21-15-02-58-755.png! 

  

We assume this is due to an internal problem which we are analysing already. Or do you know the problem for this error? 

  

BR, stefan 


[This ticket was automatically created]",event dvca checking outgoing sent notification mt noticed message reference look strange imagepng unfortunately check message look sent message due following errer trying view message imagepng assume due internal problem analysing already know problem error br stefan ticket automatically created,-0.5660371538251638,0.0,438,0.0006755387751938444,0.39150928845629096,1.***************,0.2,0.15,0.15,83.73%
