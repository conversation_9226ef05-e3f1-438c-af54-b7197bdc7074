# Enhanced AI Agent Configuration
# Copy this file to .env and fill in your API keys for enhanced functionality

# =============================================================================
# HUGGING FACE API (Optional - for better LLM responses)
# =============================================================================
# Get your free token from: https://huggingface.co/settings/tokens
# This enables access to better language models for more intelligent responses
HUGGINGFACE_TOKEN=your_huggingface_token_here

# =============================================================================
# GOOGLE CUSTOM SEARCH API (Optional - for enhanced web search)
# =============================================================================
# Get API key from: https://developers.google.com/custom-search/v1/introduction
# Create Custom Search Engine at: https://cse.google.com/cse/
GOOGLE_API_KEY=your_google_api_key_here
GOOGLE_CSE_ID=your_custom_search_engine_id_here

# =============================================================================
# DJANGO CONFIGURATION
# =============================================================================
# Generate a new secret key for production
SECRET_KEY=your-secret-key-here
DJANGO_SECRET_KEY=your_django_secret_key_here

# Environment Configuration
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# Database Configuration
DATABASE_URL=sqlite:///db.sqlite3
DB_NAME=db.sqlite3

# =============================================================================
# AI AGENT FEATURES
# =============================================================================
# Enable/disable specific AI features
ENABLE_WEB_SEARCH=True
ENABLE_MEMORY_SYSTEM=True
ENABLE_LLM_FALLBACK=True

# =============================================================================
# SETUP INSTRUCTIONS
# =============================================================================
# 1. Copy this file to .env: cp .env.example .env
# 2. Fill in the API keys above (all are optional)
# 3. Restart the Django server: python manage.py runserver
#
# The AI Agent will work without any API keys using:
# - Built-in knowledge base for common terms
# - DuckDuckGo search (free, no API key needed)
# - Intelligent fallback responses
# - Full Jira analysis capabilities
#
# With API keys, you get:
# - Better LLM responses (Hugging Face)
# - Enhanced web search (Google)
# - More accurate and detailed answers

# Add this file to .gitignore and create a .env file with actual values